# Changelog

## [1.0.0] - 2024-01-XX

### Added
- Initial project setup with comprehensive architecture
- Spring Boot 3.5 reactive backend with WebFlux
- Vue.js 3.0 frontend applications (user and admin)
- MySQL 8.0 database schema
- Redis caching integration
- Docker containerization setup
- JWT authentication framework
- Complete project documentation

### Changed
- Renamed backend directory from `backend` to `monex-api` for better clarity
- Updated all configuration files to reflect the new backend directory name
- Updated Docker Compose configuration
- Updated documentation and setup scripts

### Technical Details
- **Backend**: Spring Boot 3.5 + WebFlux + R2DBC + MySQL + Redis
- **Frontend**: Vue.js 3.0 + TypeScript + Tailwind CSS + Pinia
- **Database**: MySQL 8.0 with comprehensive schema
- **Infrastructure**: Docker + Docker Compose
- **Documentation**: Complete setup and development guides

### Project Structure
```
monex/
├── monex-api/                  # Spring Boot 3.5 Reactive API
├── frontend-user/             # Vue.js 3.0 User Interface  
├── frontend-admin/            # Vue.js 3.0 Admin Interface
├── database/                  # Database schemas
├── docs/                      # Documentation
└── docker-compose.yml         # Multi-container setup
```
