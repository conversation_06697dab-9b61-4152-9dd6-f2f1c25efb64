{"version": 3, "names": ["_index", "require", "STANDARDIZED_TYPES", "exports", "FLIPPED_ALIAS_KEYS", "EXPRESSION_TYPES", "BINARY_TYPES", "SCOPABLE_TYPES", "BLOCKPARENT_TYPES", "BLOCK_TYPES", "STATEMENT_TYPES", "TERMINATORLESS_TYPES", "COMPLETIONSTATEMENT_TYPES", "CONDITIONAL_TYPES", "LOOP_TYPES", "WHILE_TYPES", "EXPRESSIONWRAPPER_TYPES", "FOR_TYPES", "FORXSTATEMENT_TYPES", "FUNCTION_TYPES", "FUNCTIONPARENT_TYPES", "PUREISH_TYPES", "DECLARATION_TYPES", "PATTERNLIKE_TYPES", "LVAL_TYPES", "TSENTITYNAME_TYPES", "LITERAL_TYPES", "IMMUTABLE_TYPES", "USERWHITESPACABLE_TYPES", "METHOD_TYPES", "OBJECTMEMBER_TYPES", "PROPERTY_TYPES", "UNARYLIKE_TYPES", "PATTERN_TYPES", "CLASS_TYPES", "IMPORTOREXPORTDECLARATION_TYPES", "EXPORTDECLARATION_TYPES", "MODULESPECIFIER_TYPES", "ACCESSOR_TYPES", "PRIVATE_TYPES", "FLOW_TYPES", "FLOWTYPE_TYPES", "FLOWBASEANNOTATION_TYPES", "FLOWDECLARATION_TYPES", "FLOWPREDICATE_TYPES", "ENUMBODY_TYPES", "ENUMMEMBER_TYPES", "JSX_TYPES", "MISCELLANEOUS_TYPES", "TYPESCRIPT_TYPES", "TSTYPEELEMENT_TYPES", "TSTYPE_TYPES", "TSBASETYPE_TYPES", "MODULEDECLARATION_TYPES"], "sources": ["../../../src/constants/generated/index.ts"], "sourcesContent": ["/*\n * This file is auto-generated! Do not modify it directly.\n * To re-generate run 'make build'\n */\nimport { FLIPPED_ALIAS_KEYS } from \"../../definitions/index.ts\";\n\nexport const STANDARDIZED_TYPES = FLIPPED_ALIAS_KEYS[\"Standardized\"];\nexport const EXPRESSION_TYPES = FLIPPED_ALIAS_KEYS[\"Expression\"];\nexport const BINARY_TYPES = FLIPPED_ALIAS_KEYS[\"Binary\"];\nexport const SCOPABLE_TYPES = FLIPPED_ALIAS_KEYS[\"Scopable\"];\nexport const BLOCKPARENT_TYPES = FLIPPED_ALIAS_KEYS[\"BlockParent\"];\nexport const BLOCK_TYPES = FLIPPED_ALIAS_KEYS[\"Block\"];\nexport const STATEMENT_TYPES = FLIPPED_ALIAS_KEYS[\"Statement\"];\nexport const TERMINATORLESS_TYPES = FLIPPED_ALIAS_KEYS[\"Terminatorless\"];\nexport const COMPLETIONSTATEMENT_TYPES =\n  FLIPPED_ALIAS_KEYS[\"CompletionStatement\"];\nexport const CONDITIONAL_TYPES = FLIPPED_ALIAS_KEYS[\"Conditional\"];\nexport const LOOP_TYPES = FLIPPED_ALIAS_KEYS[\"Loop\"];\nexport const WHILE_TYPES = FLIPPED_ALIAS_KEYS[\"While\"];\nexport const EXPRESSIONWRAPPER_TYPES = FLIPPED_ALIAS_KEYS[\"ExpressionWrapper\"];\nexport const FOR_TYPES = FLIPPED_ALIAS_KEYS[\"For\"];\nexport const FORXSTATEMENT_TYPES = FLIPPED_ALIAS_KEYS[\"ForXStatement\"];\nexport const FUNCTION_TYPES = FLIPPED_ALIAS_KEYS[\"Function\"];\nexport const FUNCTIONPARENT_TYPES = FLIPPED_ALIAS_KEYS[\"FunctionParent\"];\nexport const PUREISH_TYPES = FLIPPED_ALIAS_KEYS[\"Pureish\"];\nexport const DECLARATION_TYPES = FLIPPED_ALIAS_KEYS[\"Declaration\"];\nexport const PATTERNLIKE_TYPES = FLIPPED_ALIAS_KEYS[\"PatternLike\"];\nexport const LVAL_TYPES = FLIPPED_ALIAS_KEYS[\"LVal\"];\nexport const TSENTITYNAME_TYPES = FLIPPED_ALIAS_KEYS[\"TSEntityName\"];\nexport const LITERAL_TYPES = FLIPPED_ALIAS_KEYS[\"Literal\"];\nexport const IMMUTABLE_TYPES = FLIPPED_ALIAS_KEYS[\"Immutable\"];\nexport const USERWHITESPACABLE_TYPES = FLIPPED_ALIAS_KEYS[\"UserWhitespacable\"];\nexport const METHOD_TYPES = FLIPPED_ALIAS_KEYS[\"Method\"];\nexport const OBJECTMEMBER_TYPES = FLIPPED_ALIAS_KEYS[\"ObjectMember\"];\nexport const PROPERTY_TYPES = FLIPPED_ALIAS_KEYS[\"Property\"];\nexport const UNARYLIKE_TYPES = FLIPPED_ALIAS_KEYS[\"UnaryLike\"];\nexport const PATTERN_TYPES = FLIPPED_ALIAS_KEYS[\"Pattern\"];\nexport const CLASS_TYPES = FLIPPED_ALIAS_KEYS[\"Class\"];\nexport const IMPORTOREXPORTDECLARATION_TYPES =\n  FLIPPED_ALIAS_KEYS[\"ImportOrExportDeclaration\"];\nexport const EXPORTDECLARATION_TYPES = FLIPPED_ALIAS_KEYS[\"ExportDeclaration\"];\nexport const MODULESPECIFIER_TYPES = FLIPPED_ALIAS_KEYS[\"ModuleSpecifier\"];\nexport const ACCESSOR_TYPES = FLIPPED_ALIAS_KEYS[\"Accessor\"];\nexport const PRIVATE_TYPES = FLIPPED_ALIAS_KEYS[\"Private\"];\nexport const FLOW_TYPES = FLIPPED_ALIAS_KEYS[\"Flow\"];\nexport const FLOWTYPE_TYPES = FLIPPED_ALIAS_KEYS[\"FlowType\"];\nexport const FLOWBASEANNOTATION_TYPES =\n  FLIPPED_ALIAS_KEYS[\"FlowBaseAnnotation\"];\nexport const FLOWDECLARATION_TYPES = FLIPPED_ALIAS_KEYS[\"FlowDeclaration\"];\nexport const FLOWPREDICATE_TYPES = FLIPPED_ALIAS_KEYS[\"FlowPredicate\"];\nexport const ENUMBODY_TYPES = FLIPPED_ALIAS_KEYS[\"EnumBody\"];\nexport const ENUMMEMBER_TYPES = FLIPPED_ALIAS_KEYS[\"EnumMember\"];\nexport const JSX_TYPES = FLIPPED_ALIAS_KEYS[\"JSX\"];\nexport const MISCELLANEOUS_TYPES = FLIPPED_ALIAS_KEYS[\"Miscellaneous\"];\nexport const TYPESCRIPT_TYPES = FLIPPED_ALIAS_KEYS[\"TypeScript\"];\nexport const TSTYPEELEMENT_TYPES = FLIPPED_ALIAS_KEYS[\"TSTypeElement\"];\nexport const TSTYPE_TYPES = FLIPPED_ALIAS_KEYS[\"TSType\"];\nexport const TSBASETYPE_TYPES = FLIPPED_ALIAS_KEYS[\"TSBaseType\"];\n/**\n * @deprecated migrate to IMPORTOREXPORTDECLARATION_TYPES.\n */\nexport const MODULEDECLARATION_TYPES = IMPORTOREXPORTDECLARATION_TYPES;\n"], "mappings": ";;;;;;AAIA,IAAAA,MAAA,GAAAC,OAAA;AAEO,MAAMC,kBAAkB,GAAAC,OAAA,CAAAD,kBAAA,GAAGE,yBAAkB,CAAC,cAAc,CAAC;AAC7D,MAAMC,gBAAgB,GAAAF,OAAA,CAAAE,gBAAA,GAAGD,yBAAkB,CAAC,YAAY,CAAC;AACzD,MAAME,YAAY,GAAAH,OAAA,CAAAG,YAAA,GAAGF,yBAAkB,CAAC,QAAQ,CAAC;AACjD,MAAMG,cAAc,GAAAJ,OAAA,CAAAI,cAAA,GAAGH,yBAAkB,CAAC,UAAU,CAAC;AACrD,MAAMI,iBAAiB,GAAAL,OAAA,CAAAK,iBAAA,GAAGJ,yBAAkB,CAAC,aAAa,CAAC;AAC3D,MAAMK,WAAW,GAAAN,OAAA,CAAAM,WAAA,GAAGL,yBAAkB,CAAC,OAAO,CAAC;AAC/C,MAAMM,eAAe,GAAAP,OAAA,CAAAO,eAAA,GAAGN,yBAAkB,CAAC,WAAW,CAAC;AACvD,MAAMO,oBAAoB,GAAAR,OAAA,CAAAQ,oBAAA,GAAGP,yBAAkB,CAAC,gBAAgB,CAAC;AACjE,MAAMQ,yBAAyB,GAAAT,OAAA,CAAAS,yBAAA,GACpCR,yBAAkB,CAAC,qBAAqB,CAAC;AACpC,MAAMS,iBAAiB,GAAAV,OAAA,CAAAU,iBAAA,GAAGT,yBAAkB,CAAC,aAAa,CAAC;AAC3D,MAAMU,UAAU,GAAAX,OAAA,CAAAW,UAAA,GAAGV,yBAAkB,CAAC,MAAM,CAAC;AAC7C,MAAMW,WAAW,GAAAZ,OAAA,CAAAY,WAAA,GAAGX,yBAAkB,CAAC,OAAO,CAAC;AAC/C,MAAMY,uBAAuB,GAAAb,OAAA,CAAAa,uBAAA,GAAGZ,yBAAkB,CAAC,mBAAmB,CAAC;AACvE,MAAMa,SAAS,GAAAd,OAAA,CAAAc,SAAA,GAAGb,yBAAkB,CAAC,KAAK,CAAC;AAC3C,MAAMc,mBAAmB,GAAAf,OAAA,CAAAe,mBAAA,GAAGd,yBAAkB,CAAC,eAAe,CAAC;AAC/D,MAAMe,cAAc,GAAAhB,OAAA,CAAAgB,cAAA,GAAGf,yBAAkB,CAAC,UAAU,CAAC;AACrD,MAAMgB,oBAAoB,GAAAjB,OAAA,CAAAiB,oBAAA,GAAGhB,yBAAkB,CAAC,gBAAgB,CAAC;AACjE,MAAMiB,aAAa,GAAAlB,OAAA,CAAAkB,aAAA,GAAGjB,yBAAkB,CAAC,SAAS,CAAC;AACnD,MAAMkB,iBAAiB,GAAAnB,OAAA,CAAAmB,iBAAA,GAAGlB,yBAAkB,CAAC,aAAa,CAAC;AAC3D,MAAMmB,iBAAiB,GAAApB,OAAA,CAAAoB,iBAAA,GAAGnB,yBAAkB,CAAC,aAAa,CAAC;AAC3D,MAAMoB,UAAU,GAAArB,OAAA,CAAAqB,UAAA,GAAGpB,yBAAkB,CAAC,MAAM,CAAC;AAC7C,MAAMqB,kBAAkB,GAAAtB,OAAA,CAAAsB,kBAAA,GAAGrB,yBAAkB,CAAC,cAAc,CAAC;AAC7D,MAAMsB,aAAa,GAAAvB,OAAA,CAAAuB,aAAA,GAAGtB,yBAAkB,CAAC,SAAS,CAAC;AACnD,MAAMuB,eAAe,GAAAxB,OAAA,CAAAwB,eAAA,GAAGvB,yBAAkB,CAAC,WAAW,CAAC;AACvD,MAAMwB,uBAAuB,GAAAzB,OAAA,CAAAyB,uBAAA,GAAGxB,yBAAkB,CAAC,mBAAmB,CAAC;AACvE,MAAMyB,YAAY,GAAA1B,OAAA,CAAA0B,YAAA,GAAGzB,yBAAkB,CAAC,QAAQ,CAAC;AACjD,MAAM0B,kBAAkB,GAAA3B,OAAA,CAAA2B,kBAAA,GAAG1B,yBAAkB,CAAC,cAAc,CAAC;AAC7D,MAAM2B,cAAc,GAAA5B,OAAA,CAAA4B,cAAA,GAAG3B,yBAAkB,CAAC,UAAU,CAAC;AACrD,MAAM4B,eAAe,GAAA7B,OAAA,CAAA6B,eAAA,GAAG5B,yBAAkB,CAAC,WAAW,CAAC;AACvD,MAAM6B,aAAa,GAAA9B,OAAA,CAAA8B,aAAA,GAAG7B,yBAAkB,CAAC,SAAS,CAAC;AACnD,MAAM8B,WAAW,GAAA/B,OAAA,CAAA+B,WAAA,GAAG9B,yBAAkB,CAAC,OAAO,CAAC;AAC/C,MAAM+B,+BAA+B,GAAAhC,OAAA,CAAAgC,+BAAA,GAC1C/B,yBAAkB,CAAC,2BAA2B,CAAC;AAC1C,MAAMgC,uBAAuB,GAAAjC,OAAA,CAAAiC,uBAAA,GAAGhC,yBAAkB,CAAC,mBAAmB,CAAC;AACvE,MAAMiC,qBAAqB,GAAAlC,OAAA,CAAAkC,qBAAA,GAAGjC,yBAAkB,CAAC,iBAAiB,CAAC;AACnE,MAAMkC,cAAc,GAAAnC,OAAA,CAAAmC,cAAA,GAAGlC,yBAAkB,CAAC,UAAU,CAAC;AACrD,MAAMmC,aAAa,GAAApC,OAAA,CAAAoC,aAAA,GAAGnC,yBAAkB,CAAC,SAAS,CAAC;AACnD,MAAMoC,UAAU,GAAArC,OAAA,CAAAqC,UAAA,GAAGpC,yBAAkB,CAAC,MAAM,CAAC;AAC7C,MAAMqC,cAAc,GAAAtC,OAAA,CAAAsC,cAAA,GAAGrC,yBAAkB,CAAC,UAAU,CAAC;AACrD,MAAMsC,wBAAwB,GAAAvC,OAAA,CAAAuC,wBAAA,GACnCtC,yBAAkB,CAAC,oBAAoB,CAAC;AACnC,MAAMuC,qBAAqB,GAAAxC,OAAA,CAAAwC,qBAAA,GAAGvC,yBAAkB,CAAC,iBAAiB,CAAC;AACnE,MAAMwC,mBAAmB,GAAAzC,OAAA,CAAAyC,mBAAA,GAAGxC,yBAAkB,CAAC,eAAe,CAAC;AAC/D,MAAMyC,cAAc,GAAA1C,OAAA,CAAA0C,cAAA,GAAGzC,yBAAkB,CAAC,UAAU,CAAC;AACrD,MAAM0C,gBAAgB,GAAA3C,OAAA,CAAA2C,gBAAA,GAAG1C,yBAAkB,CAAC,YAAY,CAAC;AACzD,MAAM2C,SAAS,GAAA5C,OAAA,CAAA4C,SAAA,GAAG3C,yBAAkB,CAAC,KAAK,CAAC;AAC3C,MAAM4C,mBAAmB,GAAA7C,OAAA,CAAA6C,mBAAA,GAAG5C,yBAAkB,CAAC,eAAe,CAAC;AAC/D,MAAM6C,gBAAgB,GAAA9C,OAAA,CAAA8C,gBAAA,GAAG7C,yBAAkB,CAAC,YAAY,CAAC;AACzD,MAAM8C,mBAAmB,GAAA/C,OAAA,CAAA+C,mBAAA,GAAG9C,yBAAkB,CAAC,eAAe,CAAC;AAC/D,MAAM+C,YAAY,GAAAhD,OAAA,CAAAgD,YAAA,GAAG/C,yBAAkB,CAAC,QAAQ,CAAC;AACjD,MAAMgD,gBAAgB,GAAAjD,OAAA,CAAAiD,gBAAA,GAAGhD,yBAAkB,CAAC,YAAY,CAAC;AAIzD,MAAMiD,uBAAuB,GAAAlD,OAAA,CAAAkD,uBAAA,GAAGlB,+BAA+B", "ignoreList": []}