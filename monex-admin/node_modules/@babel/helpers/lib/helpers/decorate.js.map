{"version": 3, "names": ["_toArray", "require", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "_decorate", "decorators", "factory", "superClass", "mixins", "api", "_getDecoratorsApi", "i", "length", "r", "initialize", "O", "initializeInstanceElements", "decorated", "elements", "decorateClass", "_coalesceClassElements", "d", "map", "_createElementDescriptor", "initializeClassElements", "F", "runClassFinishers", "finishers", "elementsDefinitionOrder", "for<PERSON>ach", "kind", "element", "placement", "defineClassElement", "proto", "prototype", "receiver", "descriptor", "initializer", "enumerable", "writable", "configurable", "value", "call", "Object", "defineProperty", "key", "newElements", "placements", "static", "own", "addElementPlacement", "_hasDecorators", "push", "elementFinishersExtras", "decorateElement", "apply", "extras", "result", "decorateConstructor", "silent", "keys", "indexOf", "TypeError", "splice", "elementObject", "fromElementDescriptor", "elementFinisherExtras", "toElementFinisherExtras", "finisher", "newExtras", "j", "obj", "fromClassDescriptor", "elementsAndFinisher", "toClassDescriptor", "undefined", "k", "desc", "Symbol", "toStringTag", "toElementDescriptors", "elementObjects", "toArray", "toElementDescriptor", "disallowProperty", "String", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "assign", "_optionalCallableProperty", "constructor", "newConstructor", "name", "objectType", "def", "get", "set", "_coalesceGetterSetter", "other", "isSameElement", "find", "_isDataDescriptor", "ReferenceError"], "sources": ["../../src/helpers/decorate.js"], "sourcesContent": ["/* @minVersion 7.1.5 */\n\n// TODO: Only Babel 7\n\nimport to<PERSON>rray from \"toArray\";\nimport to<PERSON>roper<PERSON><PERSON><PERSON> from \"toPropertyKey\";\n\n/*::\n  type PropertyDescriptor =\n    | {\n        value: any,\n        writable: boolean,\n        configurable: boolean,\n        enumerable: boolean,\n      }\n    | {\n        get?: () => any,\n        set?: (v: any) => void,\n        configurable: boolean,\n        enumerable: boolean,\n      };\n\n  type FieldDescriptor ={\n    writable: boolean,\n    configurable: boolean,\n    enumerable: boolean,\n  };\n\n  type Placement = \"static\" | \"prototype\" | \"own\";\n  type Key = string | symbol; // PrivateName is not supported yet.\n\n  type ElementDescriptor =\n    | {\n        kind: \"method\",\n        key: Key,\n        placement: Placement,\n        descriptor: PropertyDescriptor\n      }\n    | {\n        kind: \"field\",\n        key: Key,\n        placement: Placement,\n        descriptor: FieldDescriptor,\n        initializer?: () => any,\n      };\n\n  // This is exposed to the user code\n  type ElementObjectInput = ElementDescriptor & {\n    [@@toStringTag]?: \"Descriptor\"\n  };\n\n  // This is exposed to the user code\n  type ElementObjectOutput = ElementDescriptor & {\n    [@@toStringTag]?: \"Descriptor\"\n    extras?: ElementDescriptor[],\n    finisher?: ClassFinisher,\n  };\n\n  // This is exposed to the user code\n  type ClassObject = {\n    [@@toStringTag]?: \"Descriptor\",\n    kind: \"class\",\n    elements: ElementDescriptor[],\n  };\n\n  type ElementDecorator = (descriptor: ElementObjectInput) => ?ElementObjectOutput;\n  type ClassDecorator = (descriptor: ClassObject) => ?ClassObject;\n  type ClassFinisher = <A, B>(cl: Class<A>) => Class<B>;\n\n  // Only used by Babel in the transform output, not part of the spec.\n  type ElementDefinition =\n    | {\n        kind: \"method\",\n        value: any,\n        key: Key,\n        static?: boolean,\n        decorators?: ElementDecorator[],\n      }\n    | {\n        kind: \"field\",\n        value: () => any,\n        key: Key,\n        static?: boolean,\n        decorators?: ElementDecorator[],\n    };\n\n  declare function ClassFactory<C>(initialize: (instance: C) => void): {\n    F: Class<C>,\n    d: ElementDefinition[]\n  }\n\n  */\n\n/*::\n  // Various combinations with/without extras and with one or many finishers\n\n  type ElementFinisherExtras = {\n    element: ElementDescriptor,\n    finisher?: ClassFinisher,\n    extras?: ElementDescriptor[],\n  };\n\n  type ElementFinishersExtras = {\n    element: ElementDescriptor,\n    finishers: ClassFinisher[],\n    extras: ElementDescriptor[],\n  };\n\n  type ElementsFinisher = {\n    elements: ElementDescriptor[],\n    finisher?: ClassFinisher,\n  };\n\n  type ElementsFinishers = {\n    elements: ElementDescriptor[],\n    finishers: ClassFinisher[],\n  };\n\n  */\n\n/*::\n\n  type Placements = {\n    static: Key[],\n    prototype: Key[],\n    own: Key[],\n  };\n\n  */\n\n// ClassDefinitionEvaluation (Steps 26-*)\nexport default function _decorate(\n  decorators /*: ClassDecorator[] */,\n  factory /*: ClassFactory */,\n  superClass /*: ?Class<*> */,\n  mixins /*: ?Array<Function> */,\n) /*: Class<*> */ {\n  var api = _getDecoratorsApi();\n  if (mixins) {\n    for (var i = 0; i < mixins.length; i++) {\n      api = mixins[i](api);\n    }\n  }\n\n  var r = factory(function initialize(O) {\n    api.initializeInstanceElements(O, decorated.elements);\n  }, superClass);\n  var decorated = api.decorateClass(\n    _coalesceClassElements(r.d.map(_createElementDescriptor)),\n    decorators,\n  );\n\n  api.initializeClassElements(r.F, decorated.elements);\n\n  return api.runClassFinishers(r.F, decorated.finishers);\n}\n\nfunction _getDecoratorsApi() {\n  _getDecoratorsApi = function () {\n    return api;\n  };\n\n  var api = {\n    elementsDefinitionOrder: [[\"method\"], [\"field\"]],\n\n    // InitializeInstanceElements\n    initializeInstanceElements: function (\n      /*::<C>*/ O /*: C */,\n      elements /*: ElementDescriptor[] */,\n    ) {\n      [\"method\", \"field\"].forEach(function (kind) {\n        elements.forEach(function (element /*: ElementDescriptor */) {\n          if (element.kind === kind && element.placement === \"own\") {\n            this.defineClassElement(O, element);\n          }\n        }, this);\n      }, this);\n    },\n\n    // InitializeClassElements\n    initializeClassElements: function (\n      /*::<C>*/ F /*: Class<C> */,\n      elements /*: ElementDescriptor[] */,\n    ) {\n      var proto = F.prototype;\n\n      [\"method\", \"field\"].forEach(function (kind) {\n        elements.forEach(function (element /*: ElementDescriptor */) {\n          var placement = element.placement;\n          if (\n            element.kind === kind &&\n            (placement === \"static\" || placement === \"prototype\")\n          ) {\n            var receiver = placement === \"static\" ? F : proto;\n            this.defineClassElement(receiver, element);\n          }\n        }, this);\n      }, this);\n    },\n\n    // DefineClassElement\n    defineClassElement: function (\n      /*::<C>*/ receiver /*: C | Class<C> */,\n      element /*: ElementDescriptor */,\n    ) {\n      var descriptor /*: PropertyDescriptor */ = element.descriptor;\n      if (element.kind === \"field\") {\n        var initializer = element.initializer;\n        descriptor = {\n          enumerable: descriptor.enumerable,\n          writable: descriptor.writable,\n          configurable: descriptor.configurable,\n          value: initializer === void 0 ? void 0 : initializer.call(receiver),\n        };\n      }\n      Object.defineProperty(receiver, element.key, descriptor);\n    },\n\n    // DecorateClass\n    decorateClass: function (\n      elements /*: ElementDescriptor[] */,\n      decorators /*: ClassDecorator[] */,\n    ) /*: ElementsFinishers */ {\n      var newElements /*: ElementDescriptor[] */ = [];\n      var finishers /*: ClassFinisher[] */ = [];\n      var placements /*: Placements */ = {\n        static: [],\n        prototype: [],\n        own: [],\n      };\n\n      elements.forEach(function (element /*: ElementDescriptor */) {\n        this.addElementPlacement(element, placements);\n      }, this);\n\n      elements.forEach(function (element /*: ElementDescriptor */) {\n        if (!_hasDecorators(element)) return newElements.push(element);\n\n        var elementFinishersExtras /*: ElementFinishersExtras */ =\n          this.decorateElement(element, placements);\n        newElements.push(elementFinishersExtras.element);\n        newElements.push.apply(newElements, elementFinishersExtras.extras);\n        finishers.push.apply(finishers, elementFinishersExtras.finishers);\n      }, this);\n\n      if (!decorators) {\n        return { elements: newElements, finishers: finishers };\n      }\n\n      var result /*: ElementsFinishers */ = this.decorateConstructor(\n        newElements,\n        decorators,\n      );\n      finishers.push.apply(finishers, result.finishers);\n      result.finishers = finishers;\n\n      return result;\n    },\n\n    // AddElementPlacement\n    addElementPlacement: function (\n      element /*: ElementDescriptor */,\n      placements /*: Placements */,\n      silent /*: boolean */,\n    ) {\n      var keys = placements[element.placement];\n      if (!silent && keys.indexOf(element.key) !== -1) {\n        throw new TypeError(\"Duplicated element (\" + element.key + \")\");\n      }\n      keys.push(element.key);\n    },\n\n    // DecorateElement\n    decorateElement: function (\n      element /*: ElementDescriptor */,\n      placements /*: Placements */,\n    ) /*: ElementFinishersExtras */ {\n      var extras /*: ElementDescriptor[] */ = [];\n      var finishers /*: ClassFinisher[] */ = [];\n\n      for (\n        var decorators = element.decorators, i = decorators.length - 1;\n        i >= 0;\n        i--\n      ) {\n        // (inlined) RemoveElementPlacement\n        var keys = placements[element.placement];\n        keys.splice(keys.indexOf(element.key), 1);\n\n        var elementObject /*: ElementObjectInput */ =\n          this.fromElementDescriptor(element);\n        var elementFinisherExtras /*: ElementFinisherExtras */ =\n          this.toElementFinisherExtras(\n            (0, decorators[i])(elementObject) /*: ElementObjectOutput */ ||\n              elementObject,\n          );\n\n        element = elementFinisherExtras.element;\n        this.addElementPlacement(element, placements);\n\n        if (elementFinisherExtras.finisher) {\n          finishers.push(elementFinisherExtras.finisher);\n        }\n\n        var newExtras /*: ElementDescriptor[] | void */ =\n          elementFinisherExtras.extras;\n        if (newExtras) {\n          for (var j = 0; j < newExtras.length; j++) {\n            this.addElementPlacement(newExtras[j], placements);\n          }\n          extras.push.apply(extras, newExtras);\n        }\n      }\n\n      return { element: element, finishers: finishers, extras: extras };\n    },\n\n    // DecorateConstructor\n    decorateConstructor: function (\n      elements /*: ElementDescriptor[] */,\n      decorators /*: ClassDecorator[] */,\n    ) /*: ElementsFinishers */ {\n      var finishers /*: ClassFinisher[] */ = [];\n\n      for (var i = decorators.length - 1; i >= 0; i--) {\n        var obj /*: ClassObject */ = this.fromClassDescriptor(elements);\n        var elementsAndFinisher /*: ElementsFinisher */ =\n          this.toClassDescriptor(\n            (0, decorators[i])(obj) /*: ClassObject */ || obj,\n          );\n\n        if (elementsAndFinisher.finisher !== undefined) {\n          finishers.push(elementsAndFinisher.finisher);\n        }\n\n        if (elementsAndFinisher.elements !== undefined) {\n          elements = elementsAndFinisher.elements;\n\n          for (var j = 0; j < elements.length - 1; j++) {\n            for (var k = j + 1; k < elements.length; k++) {\n              if (\n                elements[j].key === elements[k].key &&\n                elements[j].placement === elements[k].placement\n              ) {\n                throw new TypeError(\n                  \"Duplicated element (\" + elements[j].key + \")\",\n                );\n              }\n            }\n          }\n        }\n      }\n\n      return { elements: elements, finishers: finishers };\n    },\n\n    // FromElementDescriptor\n    fromElementDescriptor: function (\n      element /*: ElementDescriptor */,\n    ) /*: ElementObject */ {\n      var obj /*: ElementObject */ = {\n        kind: element.kind,\n        key: element.key,\n        placement: element.placement,\n        descriptor: element.descriptor,\n      };\n\n      var desc = {\n        value: \"Descriptor\",\n        configurable: true,\n      };\n      Object.defineProperty(obj, Symbol.toStringTag, desc);\n\n      if (element.kind === \"field\") obj.initializer = element.initializer;\n\n      return obj;\n    },\n\n    // ToElementDescriptors\n    toElementDescriptors: function (\n      elementObjects /*: ElementObject[] */,\n    ) /*: ElementDescriptor[] */ {\n      if (elementObjects === undefined) return;\n      return toArray(elementObjects).map(function (elementObject) {\n        var element = this.toElementDescriptor(elementObject);\n        this.disallowProperty(\n          elementObject,\n          \"finisher\",\n          \"An element descriptor\",\n        );\n        this.disallowProperty(elementObject, \"extras\", \"An element descriptor\");\n        return element;\n      }, this);\n    },\n\n    // ToElementDescriptor\n    toElementDescriptor: function (\n      elementObject /*: ElementObject */,\n    ) /*: ElementDescriptor */ {\n      var kind = String(elementObject.kind);\n      if (kind !== \"method\" && kind !== \"field\") {\n        throw new TypeError(\n          'An element descriptor\\'s .kind property must be either \"method\" or' +\n            ' \"field\", but a decorator created an element descriptor with' +\n            ' .kind \"' +\n            kind +\n            '\"',\n        );\n      }\n\n      var key = toPropertyKey(elementObject.key);\n\n      var placement = String(elementObject.placement);\n      if (\n        placement !== \"static\" &&\n        placement !== \"prototype\" &&\n        placement !== \"own\"\n      ) {\n        throw new TypeError(\n          'An element descriptor\\'s .placement property must be one of \"static\",' +\n            ' \"prototype\" or \"own\", but a decorator created an element descriptor' +\n            ' with .placement \"' +\n            placement +\n            '\"',\n        );\n      }\n\n      var descriptor /*: PropertyDescriptor */ = elementObject.descriptor;\n\n      this.disallowProperty(elementObject, \"elements\", \"An element descriptor\");\n\n      var element /*: ElementDescriptor */ = {\n        kind: kind,\n        key: key,\n        placement: placement,\n        descriptor: Object.assign({}, descriptor),\n      };\n\n      if (kind !== \"field\") {\n        this.disallowProperty(\n          elementObject,\n          \"initializer\",\n          \"A method descriptor\",\n        );\n      } else {\n        this.disallowProperty(\n          descriptor,\n          \"get\",\n          \"The property descriptor of a field descriptor\",\n        );\n        this.disallowProperty(\n          descriptor,\n          \"set\",\n          \"The property descriptor of a field descriptor\",\n        );\n        this.disallowProperty(\n          descriptor,\n          \"value\",\n          \"The property descriptor of a field descriptor\",\n        );\n\n        element.initializer = elementObject.initializer;\n      }\n\n      return element;\n    },\n\n    toElementFinisherExtras: function (\n      elementObject /*: ElementObject */,\n    ) /*: ElementFinisherExtras */ {\n      var element /*: ElementDescriptor */ =\n        this.toElementDescriptor(elementObject);\n      var finisher /*: ClassFinisher */ = _optionalCallableProperty(\n        elementObject,\n        \"finisher\",\n      );\n      var extras /*: ElementDescriptors[] */ = this.toElementDescriptors(\n        elementObject.extras,\n      );\n\n      return { element: element, finisher: finisher, extras: extras };\n    },\n\n    // FromClassDescriptor\n    fromClassDescriptor: function (\n      elements /*: ElementDescriptor[] */,\n    ) /*: ClassObject */ {\n      var obj = {\n        kind: \"class\",\n        elements: elements.map(this.fromElementDescriptor, this),\n      };\n\n      var desc = { value: \"Descriptor\", configurable: true };\n      Object.defineProperty(obj, Symbol.toStringTag, desc);\n\n      return obj;\n    },\n\n    // ToClassDescriptor\n    toClassDescriptor: function (\n      obj /*: ClassObject */,\n    ) /*: ElementsFinisher */ {\n      var kind = String(obj.kind);\n      if (kind !== \"class\") {\n        throw new TypeError(\n          'A class descriptor\\'s .kind property must be \"class\", but a decorator' +\n            ' created a class descriptor with .kind \"' +\n            kind +\n            '\"',\n        );\n      }\n\n      this.disallowProperty(obj, \"key\", \"A class descriptor\");\n      this.disallowProperty(obj, \"placement\", \"A class descriptor\");\n      this.disallowProperty(obj, \"descriptor\", \"A class descriptor\");\n      this.disallowProperty(obj, \"initializer\", \"A class descriptor\");\n      this.disallowProperty(obj, \"extras\", \"A class descriptor\");\n\n      var finisher = _optionalCallableProperty(obj, \"finisher\");\n      var elements = this.toElementDescriptors(obj.elements);\n\n      return { elements: elements, finisher: finisher };\n    },\n\n    // RunClassFinishers\n    runClassFinishers: function (\n      constructor /*: Class<*> */,\n      finishers /*: ClassFinisher[] */,\n    ) /*: Class<*> */ {\n      for (var i = 0; i < finishers.length; i++) {\n        var newConstructor /*: ?Class<*> */ = (0, finishers[i])(constructor);\n        if (newConstructor !== undefined) {\n          // NOTE: This should check if IsConstructor(newConstructor) is false.\n          if (typeof newConstructor !== \"function\") {\n            throw new TypeError(\"Finishers must return a constructor.\");\n          }\n          constructor = newConstructor;\n        }\n      }\n      return constructor;\n    },\n\n    disallowProperty: function (obj, name, objectType) {\n      if (obj[name] !== undefined) {\n        throw new TypeError(\n          objectType + \" can't have a .\" + name + \" property.\",\n        );\n      }\n    },\n  };\n\n  return api;\n}\n\n// ClassElementEvaluation\nfunction _createElementDescriptor(\n  def /*: ElementDefinition */,\n) /*: ElementDescriptor */ {\n  var key = toPropertyKey(def.key);\n\n  var descriptor /*: PropertyDescriptor */;\n  if (def.kind === \"method\") {\n    descriptor = {\n      value: def.value,\n      writable: true,\n      configurable: true,\n      enumerable: false,\n    };\n  } else if (def.kind === \"get\") {\n    descriptor = { get: def.value, configurable: true, enumerable: false };\n  } else if (def.kind === \"set\") {\n    descriptor = { set: def.value, configurable: true, enumerable: false };\n  } else if (def.kind === \"field\") {\n    descriptor = { configurable: true, writable: true, enumerable: true };\n  }\n\n  var element /*: ElementDescriptor */ = {\n    kind: def.kind === \"field\" ? \"field\" : \"method\",\n    key: key,\n    placement: def.static\n      ? \"static\"\n      : def.kind === \"field\"\n        ? \"own\"\n        : \"prototype\",\n    descriptor: descriptor,\n  };\n  if (def.decorators) element.decorators = def.decorators;\n  if (def.kind === \"field\") element.initializer = def.value;\n\n  return element;\n}\n\n// CoalesceGetterSetter\nfunction _coalesceGetterSetter(\n  element /*: ElementDescriptor */,\n  other /*: ElementDescriptor */,\n) {\n  if (element.descriptor.get !== undefined) {\n    other.descriptor.get = element.descriptor.get;\n  } else {\n    other.descriptor.set = element.descriptor.set;\n  }\n}\n\n// CoalesceClassElements\nfunction _coalesceClassElements(\n  elements /*: ElementDescriptor[] */,\n) /*: ElementDescriptor[] */ {\n  var newElements /*: ElementDescriptor[] */ = [];\n\n  var isSameElement = function (other /*: ElementDescriptor */) /*: boolean */ {\n    return (\n      other.kind === \"method\" &&\n      other.key === element.key &&\n      other.placement === element.placement\n    );\n  };\n\n  for (var i = 0; i < elements.length; i++) {\n    var element /*: ElementDescriptor */ = elements[i];\n    var other /*: ElementDescriptor */;\n\n    if (\n      element.kind === \"method\" &&\n      (other = newElements.find(isSameElement))\n    ) {\n      if (\n        _isDataDescriptor(element.descriptor) ||\n        _isDataDescriptor(other.descriptor)\n      ) {\n        if (_hasDecorators(element) || _hasDecorators(other)) {\n          throw new ReferenceError(\n            \"Duplicated methods (\" + element.key + \") can't be decorated.\",\n          );\n        }\n        other.descriptor = element.descriptor;\n      } else {\n        if (_hasDecorators(element)) {\n          if (_hasDecorators(other)) {\n            throw new ReferenceError(\n              \"Decorators can't be placed on different accessors with for \" +\n                \"the same property (\" +\n                element.key +\n                \").\",\n            );\n          }\n          other.decorators = element.decorators;\n        }\n        _coalesceGetterSetter(element, other);\n      }\n    } else {\n      newElements.push(element);\n    }\n  }\n\n  return newElements;\n}\n\nfunction _hasDecorators(element /*: ElementDescriptor */) /*: boolean */ {\n  return element.decorators && element.decorators.length;\n}\n\nfunction _isDataDescriptor(desc /*: PropertyDescriptor */) /*: boolean */ {\n  return (\n    desc !== undefined &&\n    !(desc.value === undefined && desc.writable === undefined)\n  );\n}\n\nfunction _optionalCallableProperty /*::<T>*/(\n  obj /*: T */,\n  name /*: $Keys<T> */,\n) /*: ?Function */ {\n  var value = obj[name];\n  if (value !== undefined && typeof value !== \"function\") {\n    throw new TypeError(\"Expected '\" + name + \"' to be a function\");\n  }\n  return value;\n}\n"], "mappings": ";;;;;;AAIA,IAAAA,QAAA,GAAAC,OAAA;AACA,IAAAC,cAAA,GAAAD,OAAA;AA8He,SAASE,SAASA,CAC/BC,UAAU,EACVC,OAAO,EACPC,UAAU,EACVC,MAAM,EACU;EAChB,IAAIC,GAAG,GAAGC,iBAAiB,CAAC,CAAC;EAC7B,IAAIF,MAAM,EAAE;IACV,KAAK,IAAIG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,MAAM,CAACI,MAAM,EAAED,CAAC,EAAE,EAAE;MACtCF,GAAG,GAAGD,MAAM,CAACG,CAAC,CAAC,CAACF,GAAG,CAAC;IACtB;EACF;EAEA,IAAII,CAAC,GAAGP,OAAO,CAAC,SAASQ,UAAUA,CAACC,CAAC,EAAE;IACrCN,GAAG,CAACO,0BAA0B,CAACD,CAAC,EAAEE,SAAS,CAACC,QAAQ,CAAC;EACvD,CAAC,EAAEX,UAAU,CAAC;EACd,IAAIU,SAAS,GAAGR,GAAG,CAACU,aAAa,CAC/BC,sBAAsB,CAACP,CAAC,CAACQ,CAAC,CAACC,GAAG,CAACC,wBAAwB,CAAC,CAAC,EACzDlB,UACF,CAAC;EAEDI,GAAG,CAACe,uBAAuB,CAACX,CAAC,CAACY,CAAC,EAAER,SAAS,CAACC,QAAQ,CAAC;EAEpD,OAAOT,GAAG,CAACiB,iBAAiB,CAACb,CAAC,CAACY,CAAC,EAAER,SAAS,CAACU,SAAS,CAAC;AACxD;AAEA,SAASjB,iBAAiBA,CAAA,EAAG;EAC3BA,iBAAiB,GAAG,SAAAA,CAAA,EAAY;IAC9B,OAAOD,GAAG;EACZ,CAAC;EAED,IAAIA,GAAG,GAAG;IACRmB,uBAAuB,EAAE,CAAC,CAAC,QAAQ,CAAC,EAAE,CAAC,OAAO,CAAC,CAAC;IAGhDZ,0BAA0B,EAAE,SAAAA,CAChBD,CAAC,EACXG,QAAQ,EACR;MACA,CAAC,QAAQ,EAAE,OAAO,CAAC,CAACW,OAAO,CAAC,UAAUC,IAAI,EAAE;QAC1CZ,QAAQ,CAACW,OAAO,CAAC,UAAUE,OAAO,EAA2B;UAC3D,IAAIA,OAAO,CAACD,IAAI,KAAKA,IAAI,IAAIC,OAAO,CAACC,SAAS,KAAK,KAAK,EAAE;YACxD,IAAI,CAACC,kBAAkB,CAAClB,CAAC,EAAEgB,OAAO,CAAC;UACrC;QACF,CAAC,EAAE,IAAI,CAAC;MACV,CAAC,EAAE,IAAI,CAAC;IACV,CAAC;IAGDP,uBAAuB,EAAE,SAAAA,CACbC,CAAC,EACXP,QAAQ,EACR;MACA,IAAIgB,KAAK,GAAGT,CAAC,CAACU,SAAS;MAEvB,CAAC,QAAQ,EAAE,OAAO,CAAC,CAACN,OAAO,CAAC,UAAUC,IAAI,EAAE;QAC1CZ,QAAQ,CAACW,OAAO,CAAC,UAAUE,OAAO,EAA2B;UAC3D,IAAIC,SAAS,GAAGD,OAAO,CAACC,SAAS;UACjC,IACED,OAAO,CAACD,IAAI,KAAKA,IAAI,KACpBE,SAAS,KAAK,QAAQ,IAAIA,SAAS,KAAK,WAAW,CAAC,EACrD;YACA,IAAII,QAAQ,GAAGJ,SAAS,KAAK,QAAQ,GAAGP,CAAC,GAAGS,KAAK;YACjD,IAAI,CAACD,kBAAkB,CAACG,QAAQ,EAAEL,OAAO,CAAC;UAC5C;QACF,CAAC,EAAE,IAAI,CAAC;MACV,CAAC,EAAE,IAAI,CAAC;IACV,CAAC;IAGDE,kBAAkB,EAAE,SAAAA,CACRG,QAAQ,EAClBL,OAAO,EACP;MACA,IAAIM,UAAU,GAA6BN,OAAO,CAACM,UAAU;MAC7D,IAAIN,OAAO,CAACD,IAAI,KAAK,OAAO,EAAE;QAC5B,IAAIQ,WAAW,GAAGP,OAAO,CAACO,WAAW;QACrCD,UAAU,GAAG;UACXE,UAAU,EAAEF,UAAU,CAACE,UAAU;UACjCC,QAAQ,EAAEH,UAAU,CAACG,QAAQ;UAC7BC,YAAY,EAAEJ,UAAU,CAACI,YAAY;UACrCC,KAAK,EAAEJ,WAAW,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,WAAW,CAACK,IAAI,CAACP,QAAQ;QACpE,CAAC;MACH;MACAQ,MAAM,CAACC,cAAc,CAACT,QAAQ,EAAEL,OAAO,CAACe,GAAG,EAAET,UAAU,CAAC;IAC1D,CAAC;IAGDlB,aAAa,EAAE,SAAAA,CACbD,QAAQ,EACRb,UAAU,EACe;MACzB,IAAI0C,WAAW,GAA8B,EAAE;MAC/C,IAAIpB,SAAS,GAA0B,EAAE;MACzC,IAAIqB,UAAU,GAAqB;QACjCC,MAAM,EAAE,EAAE;QACVd,SAAS,EAAE,EAAE;QACbe,GAAG,EAAE;MACP,CAAC;MAEDhC,QAAQ,CAACW,OAAO,CAAC,UAAUE,OAAO,EAA2B;QAC3D,IAAI,CAACoB,mBAAmB,CAACpB,OAAO,EAAEiB,UAAU,CAAC;MAC/C,CAAC,EAAE,IAAI,CAAC;MAER9B,QAAQ,CAACW,OAAO,CAAC,UAAUE,OAAO,EAA2B;QAC3D,IAAI,CAACqB,cAAc,CAACrB,OAAO,CAAC,EAAE,OAAOgB,WAAW,CAACM,IAAI,CAACtB,OAAO,CAAC;QAE9D,IAAIuB,sBAAsB,GACxB,IAAI,CAACC,eAAe,CAACxB,OAAO,EAAEiB,UAAU,CAAC;QAC3CD,WAAW,CAACM,IAAI,CAACC,sBAAsB,CAACvB,OAAO,CAAC;QAChDgB,WAAW,CAACM,IAAI,CAACG,KAAK,CAACT,WAAW,EAAEO,sBAAsB,CAACG,MAAM,CAAC;QAClE9B,SAAS,CAAC0B,IAAI,CAACG,KAAK,CAAC7B,SAAS,EAAE2B,sBAAsB,CAAC3B,SAAS,CAAC;MACnE,CAAC,EAAE,IAAI,CAAC;MAER,IAAI,CAACtB,UAAU,EAAE;QACf,OAAO;UAAEa,QAAQ,EAAE6B,WAAW;UAAEpB,SAAS,EAAEA;QAAU,CAAC;MACxD;MAEA,IAAI+B,MAAM,GAA4B,IAAI,CAACC,mBAAmB,CAC5DZ,WAAW,EACX1C,UACF,CAAC;MACDsB,SAAS,CAAC0B,IAAI,CAACG,KAAK,CAAC7B,SAAS,EAAE+B,MAAM,CAAC/B,SAAS,CAAC;MACjD+B,MAAM,CAAC/B,SAAS,GAAGA,SAAS;MAE5B,OAAO+B,MAAM;IACf,CAAC;IAGDP,mBAAmB,EAAE,SAAAA,CACnBpB,OAAO,EACPiB,UAAU,EACVY,MAAM,EACN;MACA,IAAIC,IAAI,GAAGb,UAAU,CAACjB,OAAO,CAACC,SAAS,CAAC;MACxC,IAAI,CAAC4B,MAAM,IAAIC,IAAI,CAACC,OAAO,CAAC/B,OAAO,CAACe,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE;QAC/C,MAAM,IAAIiB,SAAS,CAAC,sBAAsB,GAAGhC,OAAO,CAACe,GAAG,GAAG,GAAG,CAAC;MACjE;MACAe,IAAI,CAACR,IAAI,CAACtB,OAAO,CAACe,GAAG,CAAC;IACxB,CAAC;IAGDS,eAAe,EAAE,SAAAA,CACfxB,OAAO,EACPiB,UAAU,EACoB;MAC9B,IAAIS,MAAM,GAA8B,EAAE;MAC1C,IAAI9B,SAAS,GAA0B,EAAE;MAEzC,KACE,IAAItB,UAAU,GAAG0B,OAAO,CAAC1B,UAAU,EAAEM,CAAC,GAAGN,UAAU,CAACO,MAAM,GAAG,CAAC,EAC9DD,CAAC,IAAI,CAAC,EACNA,CAAC,EAAE,EACH;QAEA,IAAIkD,IAAI,GAAGb,UAAU,CAACjB,OAAO,CAACC,SAAS,CAAC;QACxC6B,IAAI,CAACG,MAAM,CAACH,IAAI,CAACC,OAAO,CAAC/B,OAAO,CAACe,GAAG,CAAC,EAAE,CAAC,CAAC;QAEzC,IAAImB,aAAa,GACf,IAAI,CAACC,qBAAqB,CAACnC,OAAO,CAAC;QACrC,IAAIoC,qBAAqB,GACvB,IAAI,CAACC,uBAAuB,CAC1B,CAAC,CAAC,EAAE/D,UAAU,CAACM,CAAC,CAAC,EAAEsD,aAAa,CAAC,IAC/BA,aACJ,CAAC;QAEHlC,OAAO,GAAGoC,qBAAqB,CAACpC,OAAO;QACvC,IAAI,CAACoB,mBAAmB,CAACpB,OAAO,EAAEiB,UAAU,CAAC;QAE7C,IAAImB,qBAAqB,CAACE,QAAQ,EAAE;UAClC1C,SAAS,CAAC0B,IAAI,CAACc,qBAAqB,CAACE,QAAQ,CAAC;QAChD;QAEA,IAAIC,SAAS,GACXH,qBAAqB,CAACV,MAAM;QAC9B,IAAIa,SAAS,EAAE;UACb,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,SAAS,CAAC1D,MAAM,EAAE2D,CAAC,EAAE,EAAE;YACzC,IAAI,CAACpB,mBAAmB,CAACmB,SAAS,CAACC,CAAC,CAAC,EAAEvB,UAAU,CAAC;UACpD;UACAS,MAAM,CAACJ,IAAI,CAACG,KAAK,CAACC,MAAM,EAAEa,SAAS,CAAC;QACtC;MACF;MAEA,OAAO;QAAEvC,OAAO,EAAEA,OAAO;QAAEJ,SAAS,EAAEA,SAAS;QAAE8B,MAAM,EAAEA;MAAO,CAAC;IACnE,CAAC;IAGDE,mBAAmB,EAAE,SAAAA,CACnBzC,QAAQ,EACRb,UAAU,EACe;MACzB,IAAIsB,SAAS,GAA0B,EAAE;MAEzC,KAAK,IAAIhB,CAAC,GAAGN,UAAU,CAACO,MAAM,GAAG,CAAC,EAAED,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;QAC/C,IAAI6D,GAAG,GAAsB,IAAI,CAACC,mBAAmB,CAACvD,QAAQ,CAAC;QAC/D,IAAIwD,mBAAmB,GACrB,IAAI,CAACC,iBAAiB,CACpB,CAAC,CAAC,EAAEtE,UAAU,CAACM,CAAC,CAAC,EAAE6D,GAAG,CAAC,IAAuBA,GAChD,CAAC;QAEH,IAAIE,mBAAmB,CAACL,QAAQ,KAAKO,SAAS,EAAE;UAC9CjD,SAAS,CAAC0B,IAAI,CAACqB,mBAAmB,CAACL,QAAQ,CAAC;QAC9C;QAEA,IAAIK,mBAAmB,CAACxD,QAAQ,KAAK0D,SAAS,EAAE;UAC9C1D,QAAQ,GAAGwD,mBAAmB,CAACxD,QAAQ;UAEvC,KAAK,IAAIqD,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGrD,QAAQ,CAACN,MAAM,GAAG,CAAC,EAAE2D,CAAC,EAAE,EAAE;YAC5C,KAAK,IAAIM,CAAC,GAAGN,CAAC,GAAG,CAAC,EAAEM,CAAC,GAAG3D,QAAQ,CAACN,MAAM,EAAEiE,CAAC,EAAE,EAAE;cAC5C,IACE3D,QAAQ,CAACqD,CAAC,CAAC,CAACzB,GAAG,KAAK5B,QAAQ,CAAC2D,CAAC,CAAC,CAAC/B,GAAG,IACnC5B,QAAQ,CAACqD,CAAC,CAAC,CAACvC,SAAS,KAAKd,QAAQ,CAAC2D,CAAC,CAAC,CAAC7C,SAAS,EAC/C;gBACA,MAAM,IAAI+B,SAAS,CACjB,sBAAsB,GAAG7C,QAAQ,CAACqD,CAAC,CAAC,CAACzB,GAAG,GAAG,GAC7C,CAAC;cACH;YACF;UACF;QACF;MACF;MAEA,OAAO;QAAE5B,QAAQ,EAAEA,QAAQ;QAAES,SAAS,EAAEA;MAAU,CAAC;IACrD,CAAC;IAGDuC,qBAAqB,EAAE,SAAAA,CACrBnC,OAAO,EACc;MACrB,IAAIyC,GAAG,GAAwB;QAC7B1C,IAAI,EAAEC,OAAO,CAACD,IAAI;QAClBgB,GAAG,EAAEf,OAAO,CAACe,GAAG;QAChBd,SAAS,EAAED,OAAO,CAACC,SAAS;QAC5BK,UAAU,EAAEN,OAAO,CAACM;MACtB,CAAC;MAED,IAAIyC,IAAI,GAAG;QACTpC,KAAK,EAAE,YAAY;QACnBD,YAAY,EAAE;MAChB,CAAC;MACDG,MAAM,CAACC,cAAc,CAAC2B,GAAG,EAAEO,MAAM,CAACC,WAAW,EAAEF,IAAI,CAAC;MAEpD,IAAI/C,OAAO,CAACD,IAAI,KAAK,OAAO,EAAE0C,GAAG,CAAClC,WAAW,GAAGP,OAAO,CAACO,WAAW;MAEnE,OAAOkC,GAAG;IACZ,CAAC;IAGDS,oBAAoB,EAAE,SAAAA,CACpBC,cAAc,EACa;MAC3B,IAAIA,cAAc,KAAKN,SAAS,EAAE;MAClC,OAAOO,QAAO,CAACD,cAAc,CAAC,CAAC5D,GAAG,CAAC,UAAU2C,aAAa,EAAE;QAC1D,IAAIlC,OAAO,GAAG,IAAI,CAACqD,mBAAmB,CAACnB,aAAa,CAAC;QACrD,IAAI,CAACoB,gBAAgB,CACnBpB,aAAa,EACb,UAAU,EACV,uBACF,CAAC;QACD,IAAI,CAACoB,gBAAgB,CAACpB,aAAa,EAAE,QAAQ,EAAE,uBAAuB,CAAC;QACvE,OAAOlC,OAAO;MAChB,CAAC,EAAE,IAAI,CAAC;IACV,CAAC;IAGDqD,mBAAmB,EAAE,SAAAA,CACnBnB,aAAa,EACY;MACzB,IAAInC,IAAI,GAAGwD,MAAM,CAACrB,aAAa,CAACnC,IAAI,CAAC;MACrC,IAAIA,IAAI,KAAK,QAAQ,IAAIA,IAAI,KAAK,OAAO,EAAE;QACzC,MAAM,IAAIiC,SAAS,CACjB,oEAAoE,GAClE,8DAA8D,GAC9D,UAAU,GACVjC,IAAI,GACJ,GACJ,CAAC;MACH;MAEA,IAAIgB,GAAG,GAAGyC,cAAa,CAACtB,aAAa,CAACnB,GAAG,CAAC;MAE1C,IAAId,SAAS,GAAGsD,MAAM,CAACrB,aAAa,CAACjC,SAAS,CAAC;MAC/C,IACEA,SAAS,KAAK,QAAQ,IACtBA,SAAS,KAAK,WAAW,IACzBA,SAAS,KAAK,KAAK,EACnB;QACA,MAAM,IAAI+B,SAAS,CACjB,uEAAuE,GACrE,sEAAsE,GACtE,oBAAoB,GACpB/B,SAAS,GACT,GACJ,CAAC;MACH;MAEA,IAAIK,UAAU,GAA6B4B,aAAa,CAAC5B,UAAU;MAEnE,IAAI,CAACgD,gBAAgB,CAACpB,aAAa,EAAE,UAAU,EAAE,uBAAuB,CAAC;MAEzE,IAAIlC,OAAO,GAA4B;QACrCD,IAAI,EAAEA,IAAI;QACVgB,GAAG,EAAEA,GAAG;QACRd,SAAS,EAAEA,SAAS;QACpBK,UAAU,EAAEO,MAAM,CAAC4C,MAAM,CAAC,CAAC,CAAC,EAAEnD,UAAU;MAC1C,CAAC;MAED,IAAIP,IAAI,KAAK,OAAO,EAAE;QACpB,IAAI,CAACuD,gBAAgB,CACnBpB,aAAa,EACb,aAAa,EACb,qBACF,CAAC;MACH,CAAC,MAAM;QACL,IAAI,CAACoB,gBAAgB,CACnBhD,UAAU,EACV,KAAK,EACL,+CACF,CAAC;QACD,IAAI,CAACgD,gBAAgB,CACnBhD,UAAU,EACV,KAAK,EACL,+CACF,CAAC;QACD,IAAI,CAACgD,gBAAgB,CACnBhD,UAAU,EACV,OAAO,EACP,+CACF,CAAC;QAEDN,OAAO,CAACO,WAAW,GAAG2B,aAAa,CAAC3B,WAAW;MACjD;MAEA,OAAOP,OAAO;IAChB,CAAC;IAEDqC,uBAAuB,EAAE,SAAAA,CACvBH,aAAa,EACgB;MAC7B,IAAIlC,OAAO,GACT,IAAI,CAACqD,mBAAmB,CAACnB,aAAa,CAAC;MACzC,IAAII,QAAQ,GAAwBoB,yBAAyB,CAC3DxB,aAAa,EACb,UACF,CAAC;MACD,IAAIR,MAAM,GAA+B,IAAI,CAACwB,oBAAoB,CAChEhB,aAAa,CAACR,MAChB,CAAC;MAED,OAAO;QAAE1B,OAAO,EAAEA,OAAO;QAAEsC,QAAQ,EAAEA,QAAQ;QAAEZ,MAAM,EAAEA;MAAO,CAAC;IACjE,CAAC;IAGDgB,mBAAmB,EAAE,SAAAA,CACnBvD,QAAQ,EACW;MACnB,IAAIsD,GAAG,GAAG;QACR1C,IAAI,EAAE,OAAO;QACbZ,QAAQ,EAAEA,QAAQ,CAACI,GAAG,CAAC,IAAI,CAAC4C,qBAAqB,EAAE,IAAI;MACzD,CAAC;MAED,IAAIY,IAAI,GAAG;QAAEpC,KAAK,EAAE,YAAY;QAAED,YAAY,EAAE;MAAK,CAAC;MACtDG,MAAM,CAACC,cAAc,CAAC2B,GAAG,EAAEO,MAAM,CAACC,WAAW,EAAEF,IAAI,CAAC;MAEpD,OAAON,GAAG;IACZ,CAAC;IAGDG,iBAAiB,EAAE,SAAAA,CACjBH,GAAG,EACqB;MACxB,IAAI1C,IAAI,GAAGwD,MAAM,CAACd,GAAG,CAAC1C,IAAI,CAAC;MAC3B,IAAIA,IAAI,KAAK,OAAO,EAAE;QACpB,MAAM,IAAIiC,SAAS,CACjB,uEAAuE,GACrE,0CAA0C,GAC1CjC,IAAI,GACJ,GACJ,CAAC;MACH;MAEA,IAAI,CAACuD,gBAAgB,CAACb,GAAG,EAAE,KAAK,EAAE,oBAAoB,CAAC;MACvD,IAAI,CAACa,gBAAgB,CAACb,GAAG,EAAE,WAAW,EAAE,oBAAoB,CAAC;MAC7D,IAAI,CAACa,gBAAgB,CAACb,GAAG,EAAE,YAAY,EAAE,oBAAoB,CAAC;MAC9D,IAAI,CAACa,gBAAgB,CAACb,GAAG,EAAE,aAAa,EAAE,oBAAoB,CAAC;MAC/D,IAAI,CAACa,gBAAgB,CAACb,GAAG,EAAE,QAAQ,EAAE,oBAAoB,CAAC;MAE1D,IAAIH,QAAQ,GAAGoB,yBAAyB,CAACjB,GAAG,EAAE,UAAU,CAAC;MACzD,IAAItD,QAAQ,GAAG,IAAI,CAAC+D,oBAAoB,CAACT,GAAG,CAACtD,QAAQ,CAAC;MAEtD,OAAO;QAAEA,QAAQ,EAAEA,QAAQ;QAAEmD,QAAQ,EAAEA;MAAS,CAAC;IACnD,CAAC;IAGD3C,iBAAiB,EAAE,SAAAA,CACjBgE,WAAW,EACX/D,SAAS,EACO;MAChB,KAAK,IAAIhB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGgB,SAAS,CAACf,MAAM,EAAED,CAAC,EAAE,EAAE;QACzC,IAAIgF,cAAc,GAAoB,CAAC,CAAC,EAAEhE,SAAS,CAAChB,CAAC,CAAC,EAAE+E,WAAW,CAAC;QACpE,IAAIC,cAAc,KAAKf,SAAS,EAAE;UAEhC,IAAI,OAAOe,cAAc,KAAK,UAAU,EAAE;YACxC,MAAM,IAAI5B,SAAS,CAAC,sCAAsC,CAAC;UAC7D;UACA2B,WAAW,GAAGC,cAAc;QAC9B;MACF;MACA,OAAOD,WAAW;IACpB,CAAC;IAEDL,gBAAgB,EAAE,SAAAA,CAAUb,GAAG,EAAEoB,IAAI,EAAEC,UAAU,EAAE;MACjD,IAAIrB,GAAG,CAACoB,IAAI,CAAC,KAAKhB,SAAS,EAAE;QAC3B,MAAM,IAAIb,SAAS,CACjB8B,UAAU,GAAG,iBAAiB,GAAGD,IAAI,GAAG,YAC1C,CAAC;MACH;IACF;EACF,CAAC;EAED,OAAOnF,GAAG;AACZ;AAGA,SAASc,wBAAwBA,CAC/BuE,GAAG,EACsB;EACzB,IAAIhD,GAAG,GAAGyC,cAAa,CAACO,GAAG,CAAChD,GAAG,CAAC;EAEhC,IAAIT,UAAU;EACd,IAAIyD,GAAG,CAAChE,IAAI,KAAK,QAAQ,EAAE;IACzBO,UAAU,GAAG;MACXK,KAAK,EAAEoD,GAAG,CAACpD,KAAK;MAChBF,QAAQ,EAAE,IAAI;MACdC,YAAY,EAAE,IAAI;MAClBF,UAAU,EAAE;IACd,CAAC;EACH,CAAC,MAAM,IAAIuD,GAAG,CAAChE,IAAI,KAAK,KAAK,EAAE;IAC7BO,UAAU,GAAG;MAAE0D,GAAG,EAAED,GAAG,CAACpD,KAAK;MAAED,YAAY,EAAE,IAAI;MAAEF,UAAU,EAAE;IAAM,CAAC;EACxE,CAAC,MAAM,IAAIuD,GAAG,CAAChE,IAAI,KAAK,KAAK,EAAE;IAC7BO,UAAU,GAAG;MAAE2D,GAAG,EAAEF,GAAG,CAACpD,KAAK;MAAED,YAAY,EAAE,IAAI;MAAEF,UAAU,EAAE;IAAM,CAAC;EACxE,CAAC,MAAM,IAAIuD,GAAG,CAAChE,IAAI,KAAK,OAAO,EAAE;IAC/BO,UAAU,GAAG;MAAEI,YAAY,EAAE,IAAI;MAAED,QAAQ,EAAE,IAAI;MAAED,UAAU,EAAE;IAAK,CAAC;EACvE;EAEA,IAAIR,OAAO,GAA4B;IACrCD,IAAI,EAAEgE,GAAG,CAAChE,IAAI,KAAK,OAAO,GAAG,OAAO,GAAG,QAAQ;IAC/CgB,GAAG,EAAEA,GAAG;IACRd,SAAS,EAAE8D,GAAG,CAAC7C,MAAM,GACjB,QAAQ,GACR6C,GAAG,CAAChE,IAAI,KAAK,OAAO,GAClB,KAAK,GACL,WAAW;IACjBO,UAAU,EAAEA;EACd,CAAC;EACD,IAAIyD,GAAG,CAACzF,UAAU,EAAE0B,OAAO,CAAC1B,UAAU,GAAGyF,GAAG,CAACzF,UAAU;EACvD,IAAIyF,GAAG,CAAChE,IAAI,KAAK,OAAO,EAAEC,OAAO,CAACO,WAAW,GAAGwD,GAAG,CAACpD,KAAK;EAEzD,OAAOX,OAAO;AAChB;AAGA,SAASkE,qBAAqBA,CAC5BlE,OAAO,EACPmE,KAAK,EACL;EACA,IAAInE,OAAO,CAACM,UAAU,CAAC0D,GAAG,KAAKnB,SAAS,EAAE;IACxCsB,KAAK,CAAC7D,UAAU,CAAC0D,GAAG,GAAGhE,OAAO,CAACM,UAAU,CAAC0D,GAAG;EAC/C,CAAC,MAAM;IACLG,KAAK,CAAC7D,UAAU,CAAC2D,GAAG,GAAGjE,OAAO,CAACM,UAAU,CAAC2D,GAAG;EAC/C;AACF;AAGA,SAAS5E,sBAAsBA,CAC7BF,QAAQ,EACmB;EAC3B,IAAI6B,WAAW,GAA8B,EAAE;EAE/C,IAAIoD,aAAa,GAAG,SAAAA,CAAUD,KAAK,EAA0C;IAC3E,OACEA,KAAK,CAACpE,IAAI,KAAK,QAAQ,IACvBoE,KAAK,CAACpD,GAAG,KAAKf,OAAO,CAACe,GAAG,IACzBoD,KAAK,CAAClE,SAAS,KAAKD,OAAO,CAACC,SAAS;EAEzC,CAAC;EAED,KAAK,IAAIrB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGO,QAAQ,CAACN,MAAM,EAAED,CAAC,EAAE,EAAE;IACxC,IAAIoB,OAAO,GAA4Bb,QAAQ,CAACP,CAAC,CAAC;IAClD,IAAIuF,KAAK;IAET,IACEnE,OAAO,CAACD,IAAI,KAAK,QAAQ,KACxBoE,KAAK,GAAGnD,WAAW,CAACqD,IAAI,CAACD,aAAa,CAAC,CAAC,EACzC;MACA,IACEE,iBAAiB,CAACtE,OAAO,CAACM,UAAU,CAAC,IACrCgE,iBAAiB,CAACH,KAAK,CAAC7D,UAAU,CAAC,EACnC;QACA,IAAIe,cAAc,CAACrB,OAAO,CAAC,IAAIqB,cAAc,CAAC8C,KAAK,CAAC,EAAE;UACpD,MAAM,IAAII,cAAc,CACtB,sBAAsB,GAAGvE,OAAO,CAACe,GAAG,GAAG,uBACzC,CAAC;QACH;QACAoD,KAAK,CAAC7D,UAAU,GAAGN,OAAO,CAACM,UAAU;MACvC,CAAC,MAAM;QACL,IAAIe,cAAc,CAACrB,OAAO,CAAC,EAAE;UAC3B,IAAIqB,cAAc,CAAC8C,KAAK,CAAC,EAAE;YACzB,MAAM,IAAII,cAAc,CACtB,6DAA6D,GAC3D,qBAAqB,GACrBvE,OAAO,CAACe,GAAG,GACX,IACJ,CAAC;UACH;UACAoD,KAAK,CAAC7F,UAAU,GAAG0B,OAAO,CAAC1B,UAAU;QACvC;QACA4F,qBAAqB,CAAClE,OAAO,EAAEmE,KAAK,CAAC;MACvC;IACF,CAAC,MAAM;MACLnD,WAAW,CAACM,IAAI,CAACtB,OAAO,CAAC;IAC3B;EACF;EAEA,OAAOgB,WAAW;AACpB;AAEA,SAASK,cAAcA,CAACrB,OAAO,EAA0C;EACvE,OAAOA,OAAO,CAAC1B,UAAU,IAAI0B,OAAO,CAAC1B,UAAU,CAACO,MAAM;AACxD;AAEA,SAASyF,iBAAiBA,CAACvB,IAAI,EAA2C;EACxE,OACEA,IAAI,KAAKF,SAAS,IAClB,EAAEE,IAAI,CAACpC,KAAK,KAAKkC,SAAS,IAAIE,IAAI,CAACtC,QAAQ,KAAKoC,SAAS,CAAC;AAE9D;AAEA,SAASa,yBAAyBA,CAChCjB,GAAG,EACHoB,IAAI,EACa;EACjB,IAAIlD,KAAK,GAAG8B,GAAG,CAACoB,IAAI,CAAC;EACrB,IAAIlD,KAAK,KAAKkC,SAAS,IAAI,OAAOlC,KAAK,KAAK,UAAU,EAAE;IACtD,MAAM,IAAIqB,SAAS,CAAC,YAAY,GAAG6B,IAAI,GAAG,oBAAoB,CAAC;EACjE;EACA,OAAOlD,KAAK;AACd", "ignoreList": []}