package com.monex.controller;

import com.monex.entity.Stock;
import com.monex.service.StockService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.math.BigDecimal;

/**
 * REST controller for stock-related endpoints
 */
@Slf4j
@RestController
@RequestMapping("/api/stocks")
@RequiredArgsConstructor
@Tag(name = "Stock Management", description = "APIs for managing stock data and information")
public class StockController {
    
    private final StockService stockService;
    
    @GetMapping
    @Operation(summary = "Get all active stocks", description = "Retrieve all active stocks in the system")
    public Flux<Stock> getAllStocks() {
        log.info("GET /api/stocks - Fetching all active stocks");
        return stockService.getAllActiveStocks();
    }
    
    @GetMapping("/{symbol}")
    @Operation(summary = "Get stock by symbol", description = "Retrieve a specific stock by its symbol")
    public Mono<ResponseEntity<Stock>> getStockBySymbol(
            @Parameter(description = "Stock symbol", example = "AAPL")
            @PathVariable String symbol) {
        log.info("GET /api/stocks/{} - Fetching stock by symbol", symbol);
        return stockService.getStockBySymbol(symbol.toUpperCase())
                .map(ResponseEntity::ok)
                .defaultIfEmpty(ResponseEntity.notFound().build());
    }
    
    @GetMapping("/sector/{sector}")
    @Operation(summary = "Get stocks by sector", description = "Retrieve all stocks in a specific sector")
    public Flux<Stock> getStocksBySector(
            @Parameter(description = "Sector name", example = "Technology")
            @PathVariable String sector) {
        log.info("GET /api/stocks/sector/{} - Fetching stocks by sector", sector);
        return stockService.getStocksBySector(sector);
    }
    
    @GetMapping("/top-performers")
    @Operation(summary = "Get top performing stocks", description = "Retrieve top performing stocks by day change percentage")
    public Flux<Stock> getTopPerformers(
            @Parameter(description = "Number of stocks to return", example = "10")
            @RequestParam(defaultValue = "10") int limit) {
        log.info("GET /api/stocks/top-performers?limit={} - Fetching top performers", limit);
        return stockService.getTopPerformers(limit);
    }
    
    @GetMapping("/worst-performers")
    @Operation(summary = "Get worst performing stocks", description = "Retrieve worst performing stocks by day change percentage")
    public Flux<Stock> getWorstPerformers(
            @Parameter(description = "Number of stocks to return", example = "10")
            @RequestParam(defaultValue = "10") int limit) {
        log.info("GET /api/stocks/worst-performers?limit={} - Fetching worst performers", limit);
        return stockService.getWorstPerformers(limit);
    }
    
    @GetMapping("/search")
    @Operation(summary = "Search stocks", description = "Search stocks by name or symbol")
    public Flux<Stock> searchStocks(
            @Parameter(description = "Search query", example = "Apple")
            @RequestParam String q) {
        log.info("GET /api/stocks/search?q={} - Searching stocks", q);
        return stockService.searchStocks(q);
    }
    
    @GetMapping("/high-volume")
    @Operation(summary = "Get high volume stocks", description = "Retrieve stocks with volume higher than average by specified multiplier")
    public Flux<Stock> getHighVolumeStocks(
            @Parameter(description = "Volume multiplier", example = "2.0")
            @RequestParam(defaultValue = "2.0") BigDecimal multiplier) {
        log.info("GET /api/stocks/high-volume?multiplier={} - Fetching high volume stocks", multiplier);
        return stockService.getHighVolumeStocks(multiplier);
    }
    
    @PostMapping
    @Operation(summary = "Create or update stock", description = "Create a new stock or update existing one")
    public Mono<Stock> saveStock(@RequestBody Stock stock) {
        log.info("POST /api/stocks - Saving stock: {}", stock.getSymbol());
        return stockService.saveStock(stock);
    }
    
    @DeleteMapping("/{id}")
    @Operation(summary = "Delete stock", description = "Delete a stock by its ID")
    public Mono<ResponseEntity<Void>> deleteStock(
            @Parameter(description = "Stock ID")
            @PathVariable Long id) {
        log.info("DELETE /api/stocks/{} - Deleting stock", id);
        return stockService.deleteStock(id)
                .then(Mono.just(ResponseEntity.noContent().<Void>build()));
    }
}
