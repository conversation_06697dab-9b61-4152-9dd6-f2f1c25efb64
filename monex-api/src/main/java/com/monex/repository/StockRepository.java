package com.monex.repository;

import com.monex.entity.Stock;
import org.springframework.data.r2dbc.repository.Query;
import org.springframework.data.r2dbc.repository.R2dbcRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.math.BigDecimal;

/**
 * Repository interface for Stock entity operations
 */
@Repository
public interface StockRepository extends R2dbcRepository<Stock, Long> {
    
    /**
     * Find stock by symbol
     */
    Mono<Stock> findBySymbol(String symbol);
    
    /**
     * Find all active stocks
     */
    Flux<Stock> findByIsActiveTrue();
    
    /**
     * Find stocks by sector
     */
    Flux<Stock> findBySectorAndIsActiveTrue(String sector);
    
    /**
     * Find stocks by exchange
     */
    Flux<Stock> findByExchangeAndIsActiveTrue(String exchange);
    
    /**
     * Find stocks with market cap greater than specified value
     */
    @Query("SELECT * FROM stocks WHERE market_cap > :marketCap AND is_active = true ORDER BY market_cap DESC")
    Flux<Stock> findByMarketCapGreaterThan(@Param("marketCap") BigDecimal marketCap);
    
    /**
     * Find stocks with price change percentage greater than specified value
     */
    @Query("SELECT * FROM stocks WHERE day_change_percent > :changePercent AND is_active = true ORDER BY day_change_percent DESC")
    Flux<Stock> findByDayChangePercentGreaterThan(@Param("changePercent") BigDecimal changePercent);
    
    /**
     * Find top performing stocks by day change percentage
     */
    @Query("SELECT * FROM stocks WHERE is_active = true ORDER BY day_change_percent DESC LIMIT :limit")
    Flux<Stock> findTopPerformers(@Param("limit") int limit);
    
    /**
     * Find worst performing stocks by day change percentage
     */
    @Query("SELECT * FROM stocks WHERE is_active = true ORDER BY day_change_percent ASC LIMIT :limit")
    Flux<Stock> findWorstPerformers(@Param("limit") int limit);
    
    /**
     * Search stocks by name or symbol
     */
    @Query("SELECT * FROM stocks WHERE (UPPER(name) LIKE UPPER(CONCAT('%', :query, '%')) OR UPPER(symbol) LIKE UPPER(CONCAT('%', :query, '%'))) AND is_active = true")
    Flux<Stock> searchByNameOrSymbol(@Param("query") String query);
    
    /**
     * Find stocks with high volume
     */
    @Query("SELECT * FROM stocks WHERE volume > avg_volume * :multiplier AND is_active = true ORDER BY volume DESC")
    Flux<Stock> findHighVolumeStocks(@Param("multiplier") BigDecimal multiplier);
}
