package com.monex.repository;

import com.monex.entity.User;
import org.springframework.data.r2dbc.repository.R2dbcRepository;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

/**
 * Repository interface for User entity operations
 */
@Repository
public interface UserRepository extends R2dbcRepository<User, Long> {
    
    /**
     * Find user by username
     */
    Mono<User> findByUsername(String username);
    
    /**
     * Find user by email
     */
    Mono<User> findByEmail(String email);
    
    /**
     * Find user by username or email
     */
    Mono<User> findByUsernameOrEmail(String username, String email);
    
    /**
     * Find all active users
     */
    Flux<User> findByIsActiveTrue();
    
    /**
     * Find users by role
     */
    Flux<User> findByRoleAndIsActiveTrue(User.UserRole role);
    
    /**
     * Check if username exists
     */
    Mono<Boolean> existsByUsername(String username);
    
    /**
     * Check if email exists
     */
    Mono<Boolean> existsByEmail(String email);
}
