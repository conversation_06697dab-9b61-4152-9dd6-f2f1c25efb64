package com.monex.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.Id;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.relational.core.mapping.Column;
import org.springframework.data.relational.core.mapping.Table;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * Stock entity representing basic stock information
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table("stocks")
public class Stock {
    
    @Id
    private Long id;
    
    @Column("symbol")
    private String symbol;
    
    @Column("name")
    private String name;
    
    @Column("exchange")
    private String exchange;
    
    @Column("sector")
    private String sector;
    
    @Column("industry")
    private String industry;
    
    @Column("market_cap")
    private BigDecimal marketCap;
    
    @Column("current_price")
    private BigDecimal currentPrice;
    
    @Column("previous_close")
    private BigDecimal previousClose;
    
    @Column("day_change")
    private BigDecimal dayChange;
    
    @Column("day_change_percent")
    private BigDecimal dayChangePercent;
    
    @Column("volume")
    private Long volume;
    
    @Column("avg_volume")
    private Long avgVolume;
    
    @Column("pe_ratio")
    private BigDecimal peRatio;
    
    @Column("dividend_yield")
    private BigDecimal dividendYield;
    
    @Column("fifty_two_week_high")
    private BigDecimal fiftyTwoWeekHigh;
    
    @Column("fifty_two_week_low")
    private BigDecimal fiftyTwoWeekLow;
    
    @Column("description")
    private String description;
    
    @Column("is_active")
    private Boolean isActive;
    
    @CreatedDate
    @Column("created_at")
    private LocalDateTime createdAt;
    
    @LastModifiedDate
    @Column("updated_at")
    private LocalDateTime updatedAt;
}
