package com.monex.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.Id;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.relational.core.mapping.Column;
import org.springframework.data.relational.core.mapping.Table;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * Stock analysis entity containing LLM-generated analysis results
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table("stock_analyses")
public class StockAnalysis {
    
    @Id
    private Long id;
    
    @Column("stock_id")
    private Long stockId;
    
    @Column("analysis_type")
    private AnalysisType analysisType;
    
    @Column("recommendation")
    private Recommendation recommendation;
    
    @Column("confidence_score")
    private BigDecimal confidenceScore;
    
    @Column("target_price")
    private BigDecimal targetPrice;
    
    @Column("risk_level")
    private RiskLevel riskLevel;
    
    @Column("summary")
    private String summary;
    
    @Column("detailed_analysis")
    private String detailedAnalysis;
    
    @Column("key_factors")
    private String keyFactors;
    
    @Column("risks")
    private String risks;
    
    @Column("opportunities")
    private String opportunities;
    
    @Column("analyst_id")
    private Long analystId;
    
    @Column("is_active")
    private Boolean isActive;
    
    @CreatedDate
    @Column("created_at")
    private LocalDateTime createdAt;
    
    @LastModifiedDate
    @Column("updated_at")
    private LocalDateTime updatedAt;
    
    public enum AnalysisType {
        TECHNICAL, FUNDAMENTAL, SENTIMENT, COMPREHENSIVE
    }
    
    public enum Recommendation {
        STRONG_BUY, BUY, HOLD, SELL, STRONG_SELL
    }
    
    public enum RiskLevel {
        LOW, MEDIUM, HIGH, VERY_HIGH
    }
}
