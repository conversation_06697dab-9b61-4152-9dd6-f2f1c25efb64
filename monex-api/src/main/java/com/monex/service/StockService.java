package com.monex.service;

import com.monex.entity.Stock;
import com.monex.repository.StockRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.ReactiveRedisTemplate;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.math.BigDecimal;
import java.time.Duration;

/**
 * Service class for stock-related operations
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class StockService {
    
    private final StockRepository stockRepository;
    private final ReactiveRedisTemplate<String, Object> redisTemplate;
    
    private static final String STOCK_CACHE_PREFIX = "stock:";
    private static final Duration CACHE_TTL = Duration.ofMinutes(15);
    
    /**
     * Get all active stocks
     */
    public Flux<Stock> getAllActiveStocks() {
        log.debug("Fetching all active stocks");
        return stockRepository.findByIsActiveTrue()
                .doOnNext(stock -> log.debug("Found stock: {}", stock.getSymbol()));
    }
    
    /**
     * Get stock by symbol with caching
     */
    public Mono<Stock> getStockBySymbol(String symbol) {
        String cacheKey = STOCK_CACHE_PREFIX + symbol;
        
        return redisTemplate.opsForValue()
                .get(cacheKey)
                .cast(Stock.class)
                .doOnNext(stock -> log.debug("Found stock in cache: {}", symbol))
                .switchIfEmpty(
                    stockRepository.findBySymbol(symbol)
                            .doOnNext(stock -> {
                                log.debug("Found stock in database: {}", symbol);
                                // Cache the result
                                redisTemplate.opsForValue()
                                        .set(cacheKey, stock, CACHE_TTL)
                                        .subscribe();
                            })
                );
    }
    
    /**
     * Get stocks by sector
     */
    public Flux<Stock> getStocksBySector(String sector) {
        log.debug("Fetching stocks for sector: {}", sector);
        return stockRepository.findBySectorAndIsActiveTrue(sector);
    }
    
    /**
     * Get top performing stocks
     */
    public Flux<Stock> getTopPerformers(int limit) {
        log.debug("Fetching top {} performing stocks", limit);
        return stockRepository.findTopPerformers(limit);
    }
    
    /**
     * Get worst performing stocks
     */
    public Flux<Stock> getWorstPerformers(int limit) {
        log.debug("Fetching worst {} performing stocks", limit);
        return stockRepository.findWorstPerformers(limit);
    }
    
    /**
     * Search stocks by name or symbol
     */
    public Flux<Stock> searchStocks(String query) {
        log.debug("Searching stocks with query: {}", query);
        return stockRepository.searchByNameOrSymbol(query);
    }
    
    /**
     * Get stocks with high volume
     */
    public Flux<Stock> getHighVolumeStocks(BigDecimal multiplier) {
        log.debug("Fetching high volume stocks with multiplier: {}", multiplier);
        return stockRepository.findHighVolumeStocks(multiplier);
    }
    
    /**
     * Save or update stock
     */
    public Mono<Stock> saveStock(Stock stock) {
        log.debug("Saving stock: {}", stock.getSymbol());
        return stockRepository.save(stock)
                .doOnNext(savedStock -> {
                    // Invalidate cache
                    String cacheKey = STOCK_CACHE_PREFIX + savedStock.getSymbol();
                    redisTemplate.delete(cacheKey).subscribe();
                    log.debug("Stock saved and cache invalidated: {}", savedStock.getSymbol());
                });
    }
    
    /**
     * Delete stock by ID
     */
    public Mono<Void> deleteStock(Long id) {
        log.debug("Deleting stock with ID: {}", id);
        return stockRepository.findById(id)
                .doOnNext(stock -> {
                    // Invalidate cache
                    String cacheKey = STOCK_CACHE_PREFIX + stock.getSymbol();
                    redisTemplate.delete(cacheKey).subscribe();
                })
                .flatMap(stock -> stockRepository.deleteById(id))
                .doOnSuccess(unused -> log.debug("Stock deleted with ID: {}", id));
    }
}
