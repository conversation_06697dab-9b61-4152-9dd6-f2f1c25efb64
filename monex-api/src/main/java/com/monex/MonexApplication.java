package com.monex;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.data.r2dbc.config.EnableR2dbcAuditing;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * Main application class for Monex - AI-Powered Stock Selection Platform
 * 
 * This application provides:
 * - Reactive REST APIs for stock data and analysis
 * - LLM-powered investment recommendations
 * - Real-time data processing with WebFlux
 * - Redis caching for performance optimization
 */
@SpringBootApplication
@EnableR2dbcAuditing
@EnableScheduling
public class MonexApplication {

    public static void main(String[] args) {
        SpringApplication.run(MonexApplication.class, args);
    }
}
