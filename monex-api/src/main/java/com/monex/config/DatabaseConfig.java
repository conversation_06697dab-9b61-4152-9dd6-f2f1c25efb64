package com.monex.config;

import io.r2dbc.mysql.MySqlConnectionConfiguration;
import io.r2dbc.mysql.MySqlConnectionFactory;
import io.r2dbc.spi.ConnectionFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.r2dbc.config.AbstractR2dbcConfiguration;
import org.springframework.data.r2dbc.repository.config.EnableR2dbcRepositories;
import org.springframework.r2dbc.connection.R2dbcTransactionManager;
import org.springframework.transaction.ReactiveTransactionManager;

import java.time.Duration;

/**
 * Database configuration for R2DBC reactive database access
 */
@Configuration
@EnableR2dbcRepositories(basePackages = "com.monex.repository")
public class DatabaseConfig extends AbstractR2dbcConfiguration {

    @Value("${spring.r2dbc.url}")
    private String databaseUrl;
    
    @Value("${spring.r2dbc.username}")
    private String username;
    
    @Value("${spring.r2dbc.password}")
    private String password;

    @Override
    @Bean
    public ConnectionFactory connectionFactory() {
        // Extract database name from URL
        String databaseName = extractDatabaseName(databaseUrl);
        
        return MySqlConnectionFactory.from(
            MySqlConnectionConfiguration.builder()
                .host("localhost")
                .port(3306)
                .username(username)
                .password(password)
                .database(databaseName)
                .connectTimeout(Duration.ofSeconds(30))
                .build()
        );
    }
    
    @Bean
    public ReactiveTransactionManager transactionManager(ConnectionFactory connectionFactory) {
        return new R2dbcTransactionManager(connectionFactory);
    }
    
    private String extractDatabaseName(String url) {
        // Extract database name from r2dbc:mysql://localhost:3306/database_name
        String[] parts = url.split("/");
        return parts[parts.length - 1];
    }
}
