server:
  port: 8080
  
spring:
  application:
    name: monex-backend
    
  # R2DBC Configuration for Reactive Database Access
  r2dbc:
    url: r2dbc:mysql://localhost:3306/monex
    username: ${DB_USERNAME:monex_user}
    password: ${DB_PASSWORD:monex_password}
    pool:
      initial-size: 10
      max-size: 20
      max-idle-time: 30m
      validation-query: SELECT 1
      
  # Redis Configuration for Caching
  data:
    redis:
      host: ${REDIS_HOST:localhost}
      port: ${REDIS_PORT:6379}
      password: ${REDIS_PASSWORD:}
      database: 0
      timeout: 2000ms
      lettuce:
        pool:
          max-active: 8
          max-idle: 8
          min-idle: 0
          
  # Security Configuration
  security:
    oauth2:
      resourceserver:
        jwt:
          issuer-uri: ${JWT_ISSUER_URI:http://localhost:8080}
          
  # Jackson Configuration
  jackson:
    default-property-inclusion: non_null
    serialization:
      write-dates-as-timestamps: false
      
# Logging Configuration
logging:
  level:
    com.monex: DEBUG
    org.springframework.data.r2dbc: DEBUG
    io.r2dbc.mysql: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: logs/monex-backend.log
    
# Management and Monitoring
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: always
      
# Custom Application Properties
monex:
  # JWT Configuration
  jwt:
    secret: ${JWT_SECRET:your-secret-key-change-this-in-production}
    expiration: 86400000 # 24 hours in milliseconds
    
  # External API Configuration
  api:
    stock-data:
      base-url: ${STOCK_API_URL:https://api.example.com}
      api-key: ${STOCK_API_KEY:your-api-key}
      timeout: 30s
      
    llm:
      base-url: ${LLM_API_URL:https://api.openai.com/v1}
      api-key: ${LLM_API_KEY:your-openai-api-key}
      model: ${LLM_MODEL:gpt-4}
      timeout: 60s
      
  # Cache Configuration
  cache:
    stock-data-ttl: 300 # 5 minutes
    analysis-ttl: 3600 # 1 hour
    recommendation-ttl: 1800 # 30 minutes
    
  # Scheduling Configuration
  scheduler:
    stock-data-update: "0 */15 * * * *" # Every 15 minutes
    analysis-update: "0 0 */6 * * *"    # Every 6 hours
    cleanup: "0 0 2 * * *"              # Daily at 2 AM

---
# Development Profile
spring:
  config:
    activate:
      on-profile: dev
  r2dbc:
    url: r2dbc:mysql://localhost:3306/monex_dev
  data:
    redis:
      database: 1

logging:
  level:
    root: INFO
    com.monex: DEBUG

---
# Test Profile
spring:
  config:
    activate:
      on-profile: test
  r2dbc:
    url: r2dbc:h2:mem:///testdb
  data:
    redis:
      database: 2

---
# Production Profile
spring:
  config:
    activate:
      on-profile: prod
      
logging:
  level:
    root: WARN
    com.monex: INFO
  file:
    name: /var/log/monex/monex-backend.log
