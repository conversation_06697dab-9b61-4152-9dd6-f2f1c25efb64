# Monex - AI-Powered Stock Selection Platform

A comprehensive stock selection platform that leverages Large Language Models (LLMs) to analyze multiple data sources and recommend suitable investment stocks.

## 🚀 Features

- **AI-Powered Analysis**: Uses LLMs to analyze historical stock data, financial news, earnings reports, and K-line charts
- **Investment Recommendations**: Generates data-driven investment recommendations
- **Stock Screening**: Advanced filtering and screening capabilities
- **Dual Interface**: Separate user and admin interfaces
- **Real-time Data**: Reactive backend with real-time data processing

## 🏗️ Architecture

### Tech Stack
- **Backend**: Spring Boot 3.5 with WebFlux (Reactive)
- **Database**: MySQL 8.0
- **Cache**: Redis
- **Frontend**: Vue.js 3.0
- **Containerization**: Docker

### Project Structure
```
monex/
├── monex-api/                  # Spring Boot 3.5 Reactive API
├── frontend-user/             # Vue.js 3.0 User Interface
├── frontend-admin/            # Vue.js 3.0 Admin Interface
├── database/                  # Database schemas and migrations
├── docker/                    # Docker configurations
├── docs/                      # Documentation
└── README.md                  # Project overview
```

## 🛠️ Development Setup

### Prerequisites
- Java 21+
- Node.js 18+
- MySQL 8.0
- Redis 6+
- Docker (optional)

### Quick Start

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd monex
   ```

2. **Start Backend Services**
   ```bash
   cd monex-api
   ./mvnw spring-boot:run
   ```

3. **Start User Frontend**
   ```bash
   cd frontend-user
   npm install
   npm run dev
   ```

4. **Start Admin Frontend**
   ```bash
   cd frontend-admin
   npm install
   npm run dev
   ```

## 📊 Core Components

### Backend Services
- **Stock Data Service**: Historical data processing and storage
- **Analysis Service**: LLM-powered stock analysis
- **Recommendation Engine**: Investment recommendation generation
- **User Management**: Authentication and authorization
- **Cache Service**: Redis-based performance optimization

### Frontend Applications
- **User Interface**: Investment dashboard and portfolio management
- **Admin Interface**: System configuration and monitoring

## 🔧 Configuration

Environment variables and configuration files are located in:
- Backend: `monex-api/src/main/resources/application.yml`
- Frontend User: `frontend-user/.env`
- Frontend Admin: `frontend-admin/.env`

## 📝 API Documentation

API documentation is available at:
- Development: `http://localhost:8080/swagger-ui.html`
- Production: `https://api.monex.com/swagger-ui.html`

## 🚀 Deployment

### Docker Deployment
```bash
docker-compose up -d
```

### Manual Deployment
Refer to the deployment guide in `docs/deployment.md`

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Commit your changes
4. Push to the branch
5. Create a Pull Request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.
