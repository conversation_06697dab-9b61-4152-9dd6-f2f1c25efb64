version: '3.8'

services:
  # MySQL Database
  mysql:
    image: mysql:8.0
    container_name: monex-mysql
    environment:
      MYSQL_ROOT_PASSWORD: root_password
      MYSQL_DATABASE: monex
      MYSQL_USER: monex_user
      MYSQL_PASSWORD: monex_password
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./database/schema.sql:/docker-entrypoint-initdb.d/schema.sql
    networks:
      - monex-network
    restart: unless-stopped

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: monex-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - monex-network
    restart: unless-stopped
    command: redis-server --appendonly yes

  # Backend API
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: monex-backend
    environment:
      SPRING_PROFILES_ACTIVE: docker
      DB_USERNAME: monex_user
      DB_PASSWORD: monex_password
      REDIS_HOST: redis
      REDIS_PORT: 6379
    ports:
      - "8080:8080"
    depends_on:
      - mysql
      - redis
    networks:
      - monex-network
    restart: unless-stopped
    volumes:
      - ./logs:/app/logs

  # User Frontend
  frontend-user:
    build:
      context: ./frontend-user
      dockerfile: Dockerfile
    container_name: monex-frontend-user
    ports:
      - "3000:80"
    depends_on:
      - backend
    networks:
      - monex-network
    restart: unless-stopped

  # Admin Frontend
  frontend-admin:
    build:
      context: ./frontend-admin
      dockerfile: Dockerfile
    container_name: monex-frontend-admin
    ports:
      - "3001:80"
    depends_on:
      - backend
    networks:
      - monex-network
    restart: unless-stopped

volumes:
  mysql_data:
  redis_data:

networks:
  monex-network:
    driver: bridge
