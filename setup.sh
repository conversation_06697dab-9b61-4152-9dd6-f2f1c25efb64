#!/bin/bash

# Monex Platform Setup Script
# This script sets up the development environment for the Monex platform

set -e

echo "🚀 Setting up Monex AI-Powered Stock Selection Platform..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check prerequisites
check_prerequisites() {
    print_status "Checking prerequisites..."
    
    # Check Java
    if command -v java &> /dev/null; then
        JAVA_VERSION=$(java -version 2>&1 | head -n 1 | cut -d'"' -f2 | cut -d'.' -f1)
        if [ "$JAVA_VERSION" -ge 21 ]; then
            print_status "Java $JAVA_VERSION found ✓"
        else
            print_error "Java 21+ required, found Java $JAVA_VERSION"
            exit 1
        fi
    else
        print_error "Java not found. Please install Java 21+"
        exit 1
    fi
    
    # Check Node.js
    if command -v node &> /dev/null; then
        NODE_VERSION=$(node -v | cut -d'v' -f2 | cut -d'.' -f1)
        if [ "$NODE_VERSION" -ge 18 ]; then
            print_status "Node.js v$(node -v) found ✓"
        else
            print_error "Node.js 18+ required, found v$(node -v)"
            exit 1
        fi
    else
        print_error "Node.js not found. Please install Node.js 18+"
        exit 1
    fi
    
    # Check MySQL
    if command -v mysql &> /dev/null; then
        print_status "MySQL found ✓"
    else
        print_warning "MySQL not found. Please install MySQL 8.0+"
    fi
    
    # Check Redis
    if command -v redis-server &> /dev/null; then
        print_status "Redis found ✓"
    else
        print_warning "Redis not found. Please install Redis 6+"
    fi
    
    # Check Docker
    if command -v docker &> /dev/null; then
        print_status "Docker found ✓"
    else
        print_warning "Docker not found. Docker is optional but recommended"
    fi
}

# Setup environment files
setup_environment() {
    print_status "Setting up environment files..."
    
    if [ ! -f .env ]; then
        cp .env.example .env
        print_status "Created .env file from template"
        print_warning "Please edit .env file with your configuration"
    else
        print_status ".env file already exists"
    fi
    
    # Frontend user environment
    if [ ! -f frontend-user/.env.local ]; then
        cp frontend-user/.env frontend-user/.env.local
        print_status "Created frontend-user/.env.local"
    fi
    
    # Frontend admin environment
    if [ ! -f frontend-admin/.env.local ]; then
        cp frontend-admin/.env frontend-admin/.env.local
        print_status "Created frontend-admin/.env.local"
    fi
}

# Setup database
setup_database() {
    print_status "Setting up database..."
    
    if command -v mysql &> /dev/null; then
        print_status "Creating database schema..."
        mysql -u root -p < database/schema.sql || print_warning "Database setup failed. Please run manually."
    else
        print_warning "MySQL not available. Please set up database manually using database/schema.sql"
    fi
}

# Install backend dependencies
setup_backend() {
    print_status "Setting up backend..."

    cd monex-api
    
    # Make mvnw executable
    chmod +x mvnw
    
    # Download dependencies
    print_status "Downloading Maven dependencies..."
    ./mvnw dependency:go-offline -B
    
    print_status "Backend setup complete"
    cd ..
}

# Install frontend dependencies
setup_frontend() {
    print_status "Setting up frontend applications..."
    
    # User frontend
    print_status "Installing user frontend dependencies..."
    cd frontend-user
    npm install
    cd ..
    
    # Admin frontend
    print_status "Installing admin frontend dependencies..."
    cd frontend-admin
    npm install
    cd ..
    
    print_status "Frontend setup complete"
}

# Create startup scripts
create_scripts() {
    print_status "Creating startup scripts..."
    
    # Development startup script
    cat > start-dev.sh << 'EOF'
#!/bin/bash
echo "Starting Monex development environment..."

# Start backend
echo "Starting backend..."
cd monex-api && ./mvnw spring-boot:run &
BACKEND_PID=$!

# Wait for backend to start
sleep 30

# Start user frontend
echo "Starting user frontend..."
cd ../frontend-user && npm run dev &
USER_FRONTEND_PID=$!

# Start admin frontend
echo "Starting admin frontend..."
cd ../frontend-admin && npm run dev &
ADMIN_FRONTEND_PID=$!

echo "All services started!"
echo "User Frontend: http://localhost:5173"
echo "Admin Frontend: http://localhost:5174"
echo "Backend API: http://localhost:8080"
echo "API Docs: http://localhost:8080/swagger-ui.html"

# Wait for any process to exit
wait $BACKEND_PID $USER_FRONTEND_PID $ADMIN_FRONTEND_PID
EOF
    
    chmod +x start-dev.sh
    print_status "Created start-dev.sh script"
}

# Main setup function
main() {
    echo "=================================================="
    echo "  Monex AI-Powered Stock Selection Platform"
    echo "=================================================="
    echo ""
    
    check_prerequisites
    setup_environment
    setup_backend
    setup_frontend
    create_scripts
    
    echo ""
    print_status "Setup complete! 🎉"
    echo ""
    echo "Next steps:"
    echo "1. Edit .env file with your configuration"
    echo "2. Set up MySQL database (if not using Docker)"
    echo "3. Start Redis server (if not using Docker)"
    echo "4. Run './start-dev.sh' to start development environment"
    echo "   OR"
    echo "   Run 'docker-compose up -d' for containerized setup"
    echo ""
    echo "Documentation: docs/setup.md"
    echo ""
}

# Run main function
main "$@"
