import { api } from './client'
import type {
  User,
  LoginRequest,
  RegisterRequest,
  LoginResponse,
  UpdateProfileRequest,
  ChangePasswordRequest
} from '@/types/user'

export const authApi = {
  // Login user
  login: (credentials: LoginRequest): Promise<LoginResponse> =>
    api.post('/auth/login', credentials),

  // Register new user
  register: (userData: RegisterRequest): Promise<LoginResponse> =>
    api.post('/auth/register', userData),

  // Get current user profile
  getCurrentUser: (): Promise<User> =>
    api.get('/auth/me'),

  // Update user profile
  updateProfile: (profileData: UpdateProfileRequest): Promise<User> =>
    api.put('/auth/profile', profileData),

  // Change password
  changePassword: (passwordData: ChangePasswordRequest): Promise<void> =>
    api.post('/auth/change-password', passwordData),

  // Refresh token
  refreshToken: (): Promise<{ token: string; expiresIn: number }> =>
    api.post('/auth/refresh'),

  // Logout (optional - mainly for server-side session cleanup)
  logout: (): Promise<void> =>
    api.post('/auth/logout'),

  // Request password reset
  requestPasswordReset: (email: string): Promise<void> =>
    api.post('/auth/forgot-password', { email }),

  // Reset password with token
  resetPassword: (token: string, newPassword: string): Promise<void> =>
    api.post('/auth/reset-password', { token, newPassword }),

  // Verify email
  verifyEmail: (token: string): Promise<void> =>
    api.post('/auth/verify-email', { token }),

  // Resend verification email
  resendVerification: (): Promise<void> =>
    api.post('/auth/resend-verification'),
}
