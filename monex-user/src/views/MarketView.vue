<template>
  <div class="space-y-6">
    <!-- <PERSON> Header -->
    <div class="md:flex md:items-center md:justify-between">
      <div class="min-w-0 flex-1">
        <h2 class="text-2xl font-bold leading-7 text-gray-900 sm:truncate sm:text-3xl sm:tracking-tight">
          Market Overview
        </h2>
        <p class="mt-1 text-sm text-gray-500">
          Real-time market data and trends
        </p>
      </div>
    </div>

    <!-- Market Indices -->
    <div class="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
      <div
        v-for="index in marketIndices"
        :key="index.symbol"
        class="bg-white overflow-hidden shadow rounded-lg"
      >
        <div class="p-5">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="h-8 w-8 rounded-full bg-blue-100 flex items-center justify-center">
                <ChartBarIcon class="h-5 w-5 text-blue-600" />
              </div>
            </div>
            <div class="ml-5 w-0 flex-1">
              <dl>
                <dt class="text-sm font-medium text-gray-500 truncate">{{ index.name }}</dt>
                <dd class="text-lg font-medium text-gray-900">{{ index.value }}</dd>
              </dl>
            </div>
          </div>
          <div class="mt-4">
            <div class="flex items-center">
              <div
                :class="[
                  index.change > 0 ? 'text-green-600' : 'text-red-600',
                  'flex items-center text-sm font-medium'
                ]"
              >
                <component
                  :is="index.change > 0 ? ArrowUpIcon : ArrowDownIcon"
                  class="h-4 w-4 mr-1"
                />
                {{ Math.abs(index.change) }}%
              </div>
              <div class="ml-2 text-sm text-gray-500">
                {{ index.change > 0 ? '+' : '' }}{{ index.changeValue }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Market Sectors -->
    <div class="bg-white shadow rounded-lg">
      <div class="px-4 py-5 sm:p-6">
        <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">
          Sector Performance
        </h3>
        <div class="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3">
          <div
            v-for="sector in sectors"
            :key="sector.name"
            class="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow"
          >
            <div class="flex items-center justify-between">
              <div>
                <h4 class="text-sm font-medium text-gray-900">{{ sector.name }}</h4>
                <p class="text-xs text-gray-500">{{ sector.companies }} companies</p>
              </div>
              <div class="text-right">
                <p
                  :class="[
                    sector.change > 0 ? 'text-green-600' : 'text-red-600',
                    'text-sm font-medium'
                  ]"
                >
                  {{ sector.change > 0 ? '+' : '' }}{{ sector.change }}%
                </p>
              </div>
            </div>
            <div class="mt-2">
              <div class="w-full bg-gray-200 rounded-full h-2">
                <div
                  :class="[
                    sector.change > 0 ? 'bg-green-500' : 'bg-red-500',
                    'h-2 rounded-full transition-all duration-300'
                  ]"
                  :style="{ width: `${Math.min(Math.abs(sector.change) * 10, 100)}%` }"
                ></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Top Movers -->
    <div class="grid grid-cols-1 gap-6 lg:grid-cols-2">
      <!-- Top Gainers -->
      <div class="bg-white shadow rounded-lg">
        <div class="px-4 py-5 sm:p-6">
          <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">
            Top Gainers
          </h3>
          <div class="space-y-3">
            <div
              v-for="stock in topGainers"
              :key="stock.symbol"
              class="flex items-center justify-between p-3 bg-green-50 rounded-lg"
            >
              <div class="flex items-center">
                <div class="flex-shrink-0">
                  <div class="h-8 w-8 rounded-full bg-green-100 flex items-center justify-center">
                    <span class="text-xs font-medium text-green-600">{{ stock.symbol }}</span>
                  </div>
                </div>
                <div class="ml-3">
                  <p class="text-sm font-medium text-gray-900">{{ stock.name }}</p>
                  <p class="text-xs text-gray-500">${{ stock.price }}</p>
                </div>
              </div>
              <div class="text-right">
                <p class="text-sm font-medium text-green-600">+{{ stock.change }}%</p>
                <p class="text-xs text-green-500">+${{ stock.changeValue }}</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Top Losers -->
      <div class="bg-white shadow rounded-lg">
        <div class="px-4 py-5 sm:p-6">
          <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">
            Top Losers
          </h3>
          <div class="space-y-3">
            <div
              v-for="stock in topLosers"
              :key="stock.symbol"
              class="flex items-center justify-between p-3 bg-red-50 rounded-lg"
            >
              <div class="flex items-center">
                <div class="flex-shrink-0">
                  <div class="h-8 w-8 rounded-full bg-red-100 flex items-center justify-center">
                    <span class="text-xs font-medium text-red-600">{{ stock.symbol }}</span>
                  </div>
                </div>
                <div class="ml-3">
                  <p class="text-sm font-medium text-gray-900">{{ stock.name }}</p>
                  <p class="text-xs text-gray-500">${{ stock.price }}</p>
                </div>
              </div>
              <div class="text-right">
                <p class="text-sm font-medium text-red-600">{{ stock.change }}%</p>
                <p class="text-xs text-red-500">-${{ Math.abs(stock.changeValue) }}</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { ChartBarIcon, ArrowUpIcon, ArrowDownIcon } from '@heroicons/vue/24/outline'

const marketIndices = ref([
  {
    symbol: 'SPX',
    name: 'S&P 500',
    value: '4,567.89',
    change: 0.8,
    changeValue: '36.54'
  },
  {
    symbol: 'IXIC',
    name: 'NASDAQ',
    value: '14,234.56',
    change: 1.2,
    changeValue: '170.81'
  },
  {
    symbol: 'DJI',
    name: 'Dow Jones',
    value: '35,678.90',
    change: 0.3,
    changeValue: '107.04'
  },
  {
    symbol: 'RUT',
    name: 'Russell 2000',
    value: '2,123.45',
    change: -0.5,
    changeValue: '-10.62'
  }
])

const sectors = ref([
  { name: 'Technology', companies: 156, change: 1.8 },
  { name: 'Healthcare', companies: 89, change: 0.9 },
  { name: 'Financial', companies: 134, change: -0.3 },
  { name: 'Energy', companies: 67, change: 2.4 },
  { name: 'Consumer Discretionary', companies: 98, change: 0.7 },
  { name: 'Industrials', companies: 112, change: -0.8 }
])

const topGainers = ref([
  { symbol: 'NVDA', name: 'NVIDIA Corp', price: '485.32', change: 8.7, changeValue: '38.92' },
  { symbol: 'AMD', name: 'Advanced Micro Devices', price: '142.56', change: 6.3, changeValue: '8.47' },
  { symbol: 'TSLA', name: 'Tesla Inc', price: '234.78', change: 5.9, changeValue: '13.08' }
])

const topLosers = ref([
  { symbol: 'META', name: 'Meta Platforms', price: '298.45', change: -4.2, changeValue: -13.12 },
  { symbol: 'NFLX', name: 'Netflix Inc', price: '387.91', change: -3.8, changeValue: -15.32 },
  { symbol: 'PYPL', name: 'PayPal Holdings', price: '67.23', change: -3.1, changeValue: -2.15 }
])
</script>
