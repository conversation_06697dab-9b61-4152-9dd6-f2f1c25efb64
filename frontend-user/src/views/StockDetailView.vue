<template>
  <div class="space-y-6">
    <div class="md:flex md:items-center md:justify-between">
      <div class="min-w-0 flex-1">
        <h2 class="text-2xl font-bold leading-7 text-gray-900 sm:truncate sm:text-3xl sm:tracking-tight">
          {{ $route.params.symbol }}
        </h2>
        <p class="mt-1 text-sm text-gray-500">
          Detailed stock information and analysis
        </p>
      </div>
    </div>

    <div class="bg-white shadow rounded-lg p-6">
      <div class="text-center py-12">
        <ChartBarIcon class="mx-auto h-12 w-12 text-gray-400" />
        <h3 class="mt-2 text-sm font-medium text-gray-900">Stock Details</h3>
        <p class="mt-1 text-sm text-gray-500">
          Detailed stock information for {{ $route.params.symbol }} will be displayed here.
        </p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ChartBarIcon } from '@heroicons/vue/24/outline'
</script>
