<template>
  <div class="min-h-screen flex items-center justify-center bg-gray-50">
    <div class="max-w-md w-full text-center">
      <div class="mb-8">
        <h1 class="text-9xl font-bold text-gray-300">404</h1>
      </div>
      <div class="mb-8">
        <h2 class="text-2xl font-bold text-gray-900 mb-2">Page not found</h2>
        <p class="text-gray-600">
          Sorry, we couldn't find the page you're looking for.
        </p>
      </div>
      <div class="space-y-4">
        <router-link
          to="/"
          class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
        >
          <HomeIcon class="mr-2 h-4 w-4" />
          Go back home
        </router-link>
        <div>
          <button
            @click="$router.go(-1)"
            class="text-sm font-medium text-blue-600 hover:text-blue-500"
          >
            ← Go back
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { HomeIcon } from '@heroicons/vue/24/outline'
</script>
