<template>
  <div class="space-y-6">
    <div class="md:flex md:items-center md:justify-between">
      <div class="min-w-0 flex-1">
        <h2 class="text-2xl font-bold leading-7 text-gray-900 sm:truncate sm:text-3xl sm:tracking-tight">
          Fundamental Analysis
        </h2>
        <p class="mt-1 text-sm text-gray-500">
          Financial metrics, ratios, and company fundamentals
        </p>
      </div>
    </div>

    <div class="bg-white shadow rounded-lg p-6">
      <div class="text-center py-12">
        <DocumentTextIcon class="mx-auto h-12 w-12 text-gray-400" />
        <h3 class="mt-2 text-sm font-medium text-gray-900">Fundamental Analysis</h3>
        <p class="mt-1 text-sm text-gray-500">
          Fundamental analysis tools and financial metrics will be implemented here.
        </p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { DocumentTextIcon } from '@heroicons/vue/24/outline'
</script>
