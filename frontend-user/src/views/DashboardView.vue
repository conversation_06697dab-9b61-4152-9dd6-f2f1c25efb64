<template>
  <div class="space-y-6">
    <!-- <PERSON> Header -->
    <div class="md:flex md:items-center md:justify-between">
      <div class="min-w-0 flex-1">
        <h2 class="text-2xl font-bold leading-7 text-gray-900 sm:truncate sm:text-3xl sm:tracking-tight">
          Dashboard
        </h2>
        <p class="mt-1 text-sm text-gray-500">
          Welcome back, {{ authStore.userName }}! Here's your investment overview.
        </p>
      </div>
      <div class="mt-4 flex md:ml-4 md:mt-0">
        <button
          type="button"
          class="inline-flex items-center rounded-md bg-blue-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-blue-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-blue-600"
        >
          <PlusIcon class="-ml-0.5 mr-1.5 h-5 w-5" />
          Add to Portfolio
        </button>
      </div>
    </div>

    <!-- Stats Overview -->
    <div class="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
      <div
        v-for="stat in stats"
        :key="stat.name"
        class="relative overflow-hidden rounded-lg bg-white px-4 py-5 shadow sm:px-6"
      >
        <dt>
          <div class="absolute rounded-md bg-blue-500 p-3">
            <component :is="stat.icon" class="h-6 w-6 text-white" />
          </div>
          <p class="ml-16 truncate text-sm font-medium text-gray-500">{{ stat.name }}</p>
        </dt>
        <dd class="ml-16 flex items-baseline">
          <p class="text-2xl font-semibold text-gray-900">{{ stat.value }}</p>
          <p
            :class="[
              stat.changeType === 'increase' ? 'text-green-600' : 'text-red-600',
              'ml-2 flex items-baseline text-sm font-semibold'
            ]"
          >
            <component
              :is="stat.changeType === 'increase' ? ArrowUpIcon : ArrowDownIcon"
              class="h-4 w-4 flex-shrink-0 self-center"
            />
            <span class="sr-only">{{ stat.changeType === 'increase' ? 'Increased' : 'Decreased' }} by</span>
            {{ stat.change }}
          </p>
        </dd>
      </div>
    </div>

    <!-- Charts and Recent Activity -->
    <div class="grid grid-cols-1 gap-6 lg:grid-cols-2">
      <!-- Portfolio Performance Chart -->
      <div class="bg-white overflow-hidden shadow rounded-lg">
        <div class="p-6">
          <h3 class="text-lg font-medium text-gray-900 mb-4">Portfolio Performance</h3>
          <div class="h-64 flex items-center justify-center text-gray-500">
            <ChartBarIcon class="h-12 w-12 mr-2" />
            <span>Chart will be implemented with Chart.js</span>
          </div>
        </div>
      </div>

      <!-- Recent Recommendations -->
      <div class="bg-white overflow-hidden shadow rounded-lg">
        <div class="p-6">
          <h3 class="text-lg font-medium text-gray-900 mb-4">AI Recommendations</h3>
          <div class="space-y-4">
            <div
              v-for="recommendation in recommendations"
              :key="recommendation.symbol"
              class="flex items-center justify-between p-3 bg-gray-50 rounded-lg"
            >
              <div class="flex items-center">
                <div class="flex-shrink-0">
                  <div class="h-10 w-10 rounded-full bg-blue-100 flex items-center justify-center">
                    <span class="text-sm font-medium text-blue-600">{{ recommendation.symbol }}</span>
                  </div>
                </div>
                <div class="ml-3">
                  <p class="text-sm font-medium text-gray-900">{{ recommendation.name }}</p>
                  <p class="text-sm text-gray-500">{{ recommendation.recommendation }}</p>
                </div>
              </div>
              <div class="text-right">
                <p class="text-sm font-medium text-gray-900">${{ recommendation.price }}</p>
                <p
                  :class="[
                    recommendation.change > 0 ? 'text-green-600' : 'text-red-600',
                    'text-sm'
                  ]"
                >
                  {{ recommendation.change > 0 ? '+' : '' }}{{ recommendation.change }}%
                </p>
              </div>
            </div>
          </div>
          <div class="mt-4">
            <router-link
              to="/recommendations"
              class="text-sm font-medium text-blue-600 hover:text-blue-500"
            >
              View all recommendations →
            </router-link>
          </div>
        </div>
      </div>
    </div>

    <!-- Market Overview -->
    <div class="bg-white overflow-hidden shadow rounded-lg">
      <div class="p-6">
        <h3 class="text-lg font-medium text-gray-900 mb-4">Market Overview</h3>
        <div class="grid grid-cols-1 gap-4 sm:grid-cols-3">
          <div
            v-for="index in marketIndices"
            :key="index.name"
            class="text-center p-4 border border-gray-200 rounded-lg"
          >
            <p class="text-sm font-medium text-gray-500">{{ index.name }}</p>
            <p class="text-2xl font-semibold text-gray-900">{{ index.value }}</p>
            <p
              :class="[
                index.change > 0 ? 'text-green-600' : 'text-red-600',
                'text-sm font-medium'
              ]"
            >
              {{ index.change > 0 ? '+' : '' }}{{ index.change }}%
            </p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import {
  PlusIcon,
  ChartBarIcon,
  ArrowUpIcon,
  ArrowDownIcon,
  CurrencyDollarIcon,
  TrendingUpIcon,
  BriefcaseIcon,
  EyeIcon
} from '@heroicons/vue/24/outline'
import { useAuthStore } from '@/stores/auth'

const authStore = useAuthStore()

const stats = ref([
  {
    name: 'Portfolio Value',
    value: '$125,430',
    change: '+4.75%',
    changeType: 'increase',
    icon: CurrencyDollarIcon
  },
  {
    name: 'Total Return',
    value: '+$12,543',
    change: '+8.2%',
    changeType: 'increase',
    icon: TrendingUpIcon
  },
  {
    name: 'Holdings',
    value: '24',
    change: '+2',
    changeType: 'increase',
    icon: BriefcaseIcon
  },
  {
    name: 'Watchlist',
    value: '47',
    change: '+5',
    changeType: 'increase',
    icon: EyeIcon
  }
])

const recommendations = ref([
  {
    symbol: 'AAPL',
    name: 'Apple Inc.',
    recommendation: 'Strong Buy',
    price: '185.42',
    change: 2.3
  },
  {
    symbol: 'MSFT',
    name: 'Microsoft Corp.',
    recommendation: 'Buy',
    price: '378.91',
    change: 1.8
  },
  {
    symbol: 'GOOGL',
    name: 'Alphabet Inc.',
    recommendation: 'Hold',
    price: '142.56',
    change: -0.5
  }
])

const marketIndices = ref([
  { name: 'S&P 500', value: '4,567.89', change: 0.8 },
  { name: 'NASDAQ', value: '14,234.56', change: 1.2 },
  { name: 'DOW', value: '35,678.90', change: 0.3 }
])
</script>
