<template>
  <aside class="w-64 bg-white shadow-sm border-r border-gray-200 min-h-screen">
    <nav class="mt-8 px-4">
      <ul class="space-y-2">
        <li v-for="item in navigationItems" :key="item.name">
          <router-link
            :to="item.to"
            :class="[
              'group flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors duration-150',
              isActiveRoute(item.to)
                ? 'bg-blue-100 text-blue-700'
                : 'text-gray-700 hover:bg-gray-100 hover:text-gray-900'
            ]"
          >
            <component
              :is="item.icon"
              :class="[
                'mr-3 h-5 w-5',
                isActiveRoute(item.to)
                  ? 'text-blue-500'
                  : 'text-gray-400 group-hover:text-gray-500'
              ]"
            />
            {{ item.name }}
          </router-link>
        </li>
      </ul>

      <!-- Portfolio Section -->
      <div class="mt-8" v-if="authStore.isAuthenticated">
        <h3 class="px-3 text-xs font-semibold text-gray-500 uppercase tracking-wider">
          Portfolio
        </h3>
        <ul class="mt-2 space-y-1">
          <li v-for="portfolio in portfolioItems" :key="portfolio.name">
            <router-link
              :to="portfolio.to"
              :class="[
                'group flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors duration-150',
                isActiveRoute(portfolio.to)
                  ? 'bg-blue-100 text-blue-700'
                  : 'text-gray-700 hover:bg-gray-100 hover:text-gray-900'
              ]"
            >
              <component
                :is="portfolio.icon"
                :class="[
                  'mr-3 h-5 w-5',
                  isActiveRoute(portfolio.to)
                    ? 'text-blue-500'
                    : 'text-gray-400 group-hover:text-gray-500'
                ]"
              />
              {{ portfolio.name }}
            </router-link>
          </li>
        </ul>
      </div>

      <!-- Analysis Section -->
      <div class="mt-8">
        <h3 class="px-3 text-xs font-semibold text-gray-500 uppercase tracking-wider">
          Analysis
        </h3>
        <ul class="mt-2 space-y-1">
          <li v-for="analysis in analysisItems" :key="analysis.name">
            <router-link
              :to="analysis.to"
              :class="[
                'group flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors duration-150',
                isActiveRoute(analysis.to)
                  ? 'bg-blue-100 text-blue-700'
                  : 'text-gray-700 hover:bg-gray-100 hover:text-gray-900'
              ]"
            >
              <component
                :is="analysis.icon"
                :class="[
                  'mr-3 h-5 w-5',
                  isActiveRoute(analysis.to)
                    ? 'text-blue-500'
                    : 'text-gray-400 group-hover:text-gray-500'
                ]"
              />
              {{ analysis.name }}
            </router-link>
          </li>
        </ul>
      </div>
    </nav>
  </aside>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useRoute } from 'vue-router'
import {
  HomeIcon,
  ChartBarIcon,
  StarIcon,
  CogIcon,
  BriefcaseIcon,
  EyeIcon,
  SparklesIcon,
  TrendingUpIcon,
  DocumentTextIcon
} from '@heroicons/vue/24/outline'
import { useAuthStore } from '@/stores/auth'

const route = useRoute()
const authStore = useAuthStore()

const navigationItems = [
  { name: 'Dashboard', to: '/', icon: HomeIcon },
  { name: 'Market Overview', to: '/market', icon: ChartBarIcon },
  { name: 'Stock Screener', to: '/screener', icon: TrendingUpIcon },
  { name: 'Recommendations', to: '/recommendations', icon: SparklesIcon },
  { name: 'Settings', to: '/settings', icon: CogIcon },
]

const portfolioItems = computed(() => [
  { name: 'My Portfolio', to: '/portfolio', icon: BriefcaseIcon },
  { name: 'Watchlist', to: '/watchlist', icon: EyeIcon },
  { name: 'Favorites', to: '/favorites', icon: StarIcon },
])

const analysisItems = [
  { name: 'Technical Analysis', to: '/analysis/technical', icon: ChartBarIcon },
  { name: 'Fundamental Analysis', to: '/analysis/fundamental', icon: DocumentTextIcon },
  { name: 'AI Insights', to: '/analysis/ai', icon: SparklesIcon },
]

const isActiveRoute = (to: string): boolean => {
  if (to === '/') {
    return route.path === '/'
  }
  return route.path.startsWith(to)
}
</script>
