{"name": "vue-eslint-parser", "version": "10.1.3", "description": "The ESLint custom parser for `.vue` files.", "main": "index.js", "files": ["index.*"], "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "peerDependencies": {"eslint": "^8.57.0 || ^9.0.0"}, "dependencies": {"debug": "^4.4.0", "eslint-scope": "^8.2.0", "eslint-visitor-keys": "^4.2.0", "espree": "^10.3.0", "esquery": "^1.6.0", "lodash": "^4.17.21", "semver": "^7.6.3"}, "devDependencies": {"@babel/core": "^7.26.7", "@babel/eslint-parser": "^7.26.5", "@babel/plugin-syntax-decorators": "^7.25.9", "@babel/plugin-syntax-pipeline-operator": "^7.26.7", "@babel/plugin-syntax-typescript": "^7.25.9", "@eslint/eslintrc": "^3.2.0", "@eslint/js": "^9.19.0", "@types/debug": "^4.1.7", "@types/estree": "^1.0.0", "@types/lodash": "^4.14.186", "@types/mocha": "^9.0.0", "@types/node": "^18.8.4", "@types/semver": "^7.3.12", "@typescript-eslint/eslint-plugin": "^8.22.0", "@typescript-eslint/parser": "^8.22.0", "chokidar": "^3.5.2", "cross-spawn": "^7.0.3", "dts-bundle": "^0.7.3", "eslint": "^9.19.0", "eslint-plugin-eslint-comments": "^3.2.0", "eslint-plugin-jsonc": "^2.19.1", "eslint-plugin-n": "^17.15.1", "eslint-plugin-node-dependencies": "^0.12.0", "eslint-plugin-prettier": "^5.2.3", "eslint-plugin-unicorn": "^57.0.0", "fs-extra": "^10.0.0", "jsonc-eslint-parser": "^2.0.3", "mocha": "^9.1.3", "npm-run-all": "^4.1.5", "nyc": "^15.1.0", "opener": "^1.5.2", "prettier": "^3.4.2", "rimraf": "^3.0.2", "rollup": "^2.60.0", "rollup-plugin-node-resolve": "^5.2.0", "rollup-plugin-replace": "^2.2.0", "rollup-plugin-sourcemaps": "^0.6.3", "ts-node": "^10.9.2", "typescript": "~5.7.3", "wait-on": "^6.0.0", "warun": "^1.0.0"}, "scripts": {"prebuild": "npm run -s clean", "build": "tsc --module es2015 && rollup -c -o index.js && dts-bundle --name vue-eslint-parser --main .temp/index.d.ts --out ../index.d.ts", "clean": "rimraf .nyc_output .temp coverage index.*", "coverage": "opener ./coverage/lcov-report/index.html", "lint": "eslint src test package.json", "pretest": "run-s build lint", "test": "npm run -s test:mocha", "test:mocha": "mocha --require ts-node/register \"test/*.js\" --reporter dot --timeout 60000", "test:cover": "nyc mocha \"test/*.js\" --reporter dot --timeout 60000", "test:debug": "mocha --require ts-node/register/transpile-only \"test/*.js\" --reporter dot --timeout 60000", "update-fixtures": "ts-node --transpile-only scripts/update-fixtures-ast.js && ts-node --transpile-only scripts/update-fixtures-document-fragment.js", "preversion": "npm test", "version": "npm run -s build", "postversion": "git push && git push --tags", "prewatch": "npm run -s clean", "watch": "run-p watch:*", "watch:tsc": "tsc --module es2015 --watch", "watch:rollup": "wait-on .temp/index.js && rollup -c -o index.js --watch", "watch:test": "wait-on index.js && warun index.js \"test/*.js\" \"test/fixtures/ast/*/*.json\" \"test/fixtures/*\" --debounce 1000 --no-initial -- nyc mocha \"test/*.js\" --reporter dot --timeout 10000", "watch:update-ast": "wait-on index.js && warun index.js \"test/fixtures/ast/*/*.vue\" -- node scripts/update-fixtures-ast.js", "watch:coverage-report": "wait-on coverage/lcov-report/index.html && opener coverage/lcov-report/index.html"}, "repository": {"type": "git", "url": "git+https://github.com/vuejs/vue-eslint-parser.git"}, "keywords": [], "author": "<PERSON><PERSON>", "license": "MIT", "bugs": {"url": "https://github.com/vuejs/vue-eslint-parser/issues"}, "homepage": "https://github.com/vuejs/vue-eslint-parser#readme", "funding": "https://github.com/sponsors/mysticatea"}