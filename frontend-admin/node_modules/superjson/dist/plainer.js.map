{"version": 3, "file": "plainer.js", "sourceRoot": "", "sources": ["../src/plainer.ts"], "names": [], "mappings": "AAAA,OAAO,EACL,OAAO,EACP,aAAa,EACb,KAAK,EACL,aAAa,EACb,WAAW,EACX,KAAK,GACN,MAAM,SAAS,CAAC;AACjB,OAAO,EAAE,SAAS,EAAE,aAAa,EAAE,MAAM,sBAAsB,CAAC;AAChE,OAAO,EACL,2BAA2B,EAC3B,cAAc,EAEd,gBAAgB,GACjB,MAAM,kBAAkB,CAAC;AAC1B,OAAO,EAAE,QAAQ,EAAE,OAAO,EAAE,MAAM,WAAW,CAAC;AAC9C,OAAO,EAAE,SAAS,EAAE,MAAM,sBAAsB,CAAC;AACjD,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,MAAM,iBAAiB,CAAC;AASnD,SAAS,QAAQ,CACf,IAAsB,EACtB,MAAsC,EACtC,SAAmB,EAAE;IAErB,IAAI,CAAC,IAAI,EAAE;QACT,OAAO;KACR;IAED,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;QAClB,OAAO,CAAC,IAAI,EAAE,CAAC,OAAO,EAAE,GAAG,EAAE,EAAE,CAC7B,QAAQ,CAAC,OAAO,EAAE,MAAM,EAAE,CAAC,GAAG,MAAM,EAAE,GAAG,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC,CAC1D,CAAC;QACF,OAAO;KACR;IAED,MAAM,CAAC,SAAS,EAAE,QAAQ,CAAC,GAAG,IAAI,CAAC;IACnC,IAAI,QAAQ,EAAE;QACZ,OAAO,CAAC,QAAQ,EAAE,CAAC,KAAK,EAAE,GAAG,EAAE,EAAE;YAC/B,QAAQ,CAAC,KAAK,EAAE,MAAM,EAAE,CAAC,GAAG,MAAM,EAAE,GAAG,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QAC1D,CAAC,CAAC,CAAC;KACJ;IAED,MAAM,CAAC,SAAS,EAAE,MAAM,CAAC,CAAC;AAC5B,CAAC;AAED,MAAM,UAAU,qBAAqB,CACnC,KAAU,EACV,WAA0C,EAC1C,SAAoB;IAEpB,QAAQ,CAAC,WAAW,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,EAAE;QACnC,KAAK,GAAG,OAAO,CAAC,KAAK,EAAE,IAAI,EAAE,CAAC,CAAC,EAAE,CAAC,gBAAgB,CAAC,CAAC,EAAE,IAAI,EAAE,SAAS,CAAC,CAAC,CAAC;IAC1E,CAAC,CAAC,CAAC;IAEH,OAAO,KAAK,CAAC;AACf,CAAC;AAED,MAAM,UAAU,mCAAmC,CACjD,KAAU,EACV,WAA2C;IAE3C,SAAS,KAAK,CAAC,cAAwB,EAAE,IAAY;QACnD,MAAM,MAAM,GAAG,OAAO,CAAC,KAAK,EAAE,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC;QAE/C,cAAc,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC,OAAO,CAAC,mBAAmB,CAAC,EAAE;YAC1D,KAAK,GAAG,OAAO,CAAC,KAAK,EAAE,mBAAmB,EAAE,GAAG,EAAE,CAAC,MAAM,CAAC,CAAC;QAC5D,CAAC,CAAC,CAAC;IACL,CAAC;IAED,IAAI,OAAO,CAAC,WAAW,CAAC,EAAE;QACxB,MAAM,CAAC,IAAI,EAAE,KAAK,CAAC,GAAG,WAAW,CAAC;QAClC,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,EAAE;YAC3B,KAAK,GAAG,OAAO,CAAC,KAAK,EAAE,SAAS,CAAC,aAAa,CAAC,EAAE,GAAG,EAAE,CAAC,KAAK,CAAC,CAAC;QAChE,CAAC,CAAC,CAAC;QAEH,IAAI,KAAK,EAAE;YACT,OAAO,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;SACvB;KACF;SAAM;QACL,OAAO,CAAC,WAAW,EAAE,KAAK,CAAC,CAAC;KAC7B;IAED,OAAO,KAAK,CAAC;AACf,CAAC;AAED,MAAM,MAAM,GAAG,CAAC,MAAW,EAAE,SAAoB,EAAW,EAAE,CAC5D,aAAa,CAAC,MAAM,CAAC;IACrB,OAAO,CAAC,MAAM,CAAC;IACf,KAAK,CAAC,MAAM,CAAC;IACb,KAAK,CAAC,MAAM,CAAC;IACb,2BAA2B,CAAC,MAAM,EAAE,SAAS,CAAC,CAAC;AAEjD,SAAS,WAAW,CAAC,MAAW,EAAE,IAAW,EAAE,UAA6B;IAC1E,MAAM,WAAW,GAAG,UAAU,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;IAE3C,IAAI,WAAW,EAAE;QACf,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;KACxB;SAAM;QACL,UAAU,CAAC,GAAG,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC;KAChC;AACH,CAAC;AAYD,MAAM,UAAU,sCAAsC,CACpD,WAA8B,EAC9B,MAAe;IAEf,MAAM,MAAM,GAA6B,EAAE,CAAC;IAC5C,IAAI,iBAAiB,GAAyB,SAAS,CAAC;IAExD,WAAW,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;QAC1B,IAAI,KAAK,CAAC,MAAM,IAAI,CAAC,EAAE;YACrB,OAAO;SACR;QAED,iEAAiE;QACjE,sEAAsE;QACtE,qGAAqG;QACrG,IAAI,CAAC,MAAM,EAAE;YACX,KAAK,GAAG,KAAK;iBACV,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;iBAC7B,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,MAAM,CAAC,CAAC;SACxC;QAED,MAAM,CAAC,kBAAkB,EAAE,GAAG,cAAc,CAAC,GAAG,KAAK,CAAC;QAEtD,IAAI,kBAAkB,CAAC,MAAM,KAAK,CAAC,EAAE;YACnC,iBAAiB,GAAG,cAAc,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC;SACvD;aAAM;YACL,MAAM,CAAC,aAAa,CAAC,kBAAkB,CAAC,CAAC,GAAG,cAAc,CAAC,GAAG,CAC5D,aAAa,CACd,CAAC;SACH;IACH,CAAC,CAAC,CAAC;IAEH,IAAI,iBAAiB,EAAE;QACrB,IAAI,aAAa,CAAC,MAAM,CAAC,EAAE;YACzB,OAAO,CAAC,iBAAiB,CAAC,CAAC;SAC5B;aAAM;YACL,OAAO,CAAC,iBAAiB,EAAE,MAAM,CAAC,CAAC;SACpC;KACF;SAAM;QACL,OAAO,aAAa,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,MAAM,CAAC;KACnD;AACH,CAAC;AAED,MAAM,CAAC,MAAM,MAAM,GAAG,CACpB,MAAW,EACX,UAA6B,EAC7B,SAAoB,EACpB,MAAe,EACf,OAAc,EAAE,EAChB,oBAA2B,EAAE,EAC7B,cAAc,IAAI,GAAG,EAAmB,EAChC,EAAE;IACV,MAAM,SAAS,GAAG,WAAW,CAAC,MAAM,CAAC,CAAC;IAEtC,IAAI,CAAC,SAAS,EAAE;QACd,WAAW,CAAC,MAAM,EAAE,IAAI,EAAE,UAAU,CAAC,CAAC;QAEtC,MAAM,IAAI,GAAG,WAAW,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;QACrC,IAAI,IAAI,EAAE;YACR,wDAAwD;YACxD,OAAO,MAAM;gBACX,CAAC,CAAC;oBACE,gBAAgB,EAAE,IAAI;iBACvB;gBACH,CAAC,CAAC,IAAI,CAAC;SACV;KACF;IAED,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,SAAS,CAAC,EAAE;QAC9B,MAAM,WAAW,GAAG,cAAc,CAAC,MAAM,EAAE,SAAS,CAAC,CAAC;QAEtD,MAAM,MAAM,GAAW,WAAW;YAChC,CAAC,CAAC;gBACE,gBAAgB,EAAE,WAAW,CAAC,KAAK;gBACnC,WAAW,EAAE,CAAC,WAAW,CAAC,IAAI,CAAC;aAChC;YACH,CAAC,CAAC;gBACE,gBAAgB,EAAE,MAAM;aACzB,CAAC;QACN,IAAI,CAAC,SAAS,EAAE;YACd,WAAW,CAAC,GAAG,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;SACjC;QACD,OAAO,MAAM,CAAC;KACf;IAED,IAAI,QAAQ,CAAC,iBAAiB,EAAE,MAAM,CAAC,EAAE;QACvC,8BAA8B;QAC9B,OAAO;YACL,gBAAgB,EAAE,IAAI;SACvB,CAAC;KACH;IAED,MAAM,oBAAoB,GAAG,cAAc,CAAC,MAAM,EAAE,SAAS,CAAC,CAAC;IAC/D,MAAM,WAAW,GAAG,oBAAoB,EAAE,KAAK,IAAI,MAAM,CAAC;IAE1D,MAAM,gBAAgB,GAAQ,OAAO,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;IAC7D,MAAM,gBAAgB,GAAyC,EAAE,CAAC;IAElE,OAAO,CAAC,WAAW,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,EAAE;QACpC,IACE,KAAK,KAAK,WAAW;YACrB,KAAK,KAAK,aAAa;YACvB,KAAK,KAAK,WAAW,EACrB;YACA,MAAM,IAAI,KAAK,CACb,qBAAqB,KAAK,0EAA0E,CACrG,CAAC;SACH;QAED,MAAM,eAAe,GAAG,MAAM,CAC5B,KAAK,EACL,UAAU,EACV,SAAS,EACT,MAAM,EACN,CAAC,GAAG,IAAI,EAAE,KAAK,CAAC,EAChB,CAAC,GAAG,iBAAiB,EAAE,MAAM,CAAC,EAC9B,WAAW,CACZ,CAAC;QAEF,gBAAgB,CAAC,KAAK,CAAC,GAAG,eAAe,CAAC,gBAAgB,CAAC;QAE3D,IAAI,OAAO,CAAC,eAAe,CAAC,WAAW,CAAC,EAAE;YACxC,gBAAgB,CAAC,KAAK,CAAC,GAAG,eAAe,CAAC,WAAW,CAAC;SACvD;aAAM,IAAI,aAAa,CAAC,eAAe,CAAC,WAAW,CAAC,EAAE;YACrD,OAAO,CAAC,eAAe,CAAC,WAAW,EAAE,CAAC,IAAI,EAAE,GAAG,EAAE,EAAE;gBACjD,gBAAgB,CAAC,SAAS,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,GAAG,CAAC,GAAG,IAAI,CAAC;YACxD,CAAC,CAAC,CAAC;SACJ;IACH,CAAC,CAAC,CAAC;IAEH,MAAM,MAAM,GAAW,aAAa,CAAC,gBAAgB,CAAC;QACpD,CAAC,CAAC;YACE,gBAAgB;YAChB,WAAW,EAAE,CAAC,CAAC,oBAAoB;gBACjC,CAAC,CAAC,CAAC,oBAAoB,CAAC,IAAI,CAAC;gBAC7B,CAAC,CAAC,SAAS;SACd;QACH,CAAC,CAAC;YACE,gBAAgB;YAChB,WAAW,EAAE,CAAC,CAAC,oBAAoB;gBACjC,CAAC,CAAC,CAAC,oBAAoB,CAAC,IAAI,EAAE,gBAAgB,CAAC;gBAC/C,CAAC,CAAC,gBAAgB;SACrB,CAAC;IACN,IAAI,CAAC,SAAS,EAAE;QACd,WAAW,CAAC,GAAG,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;KACjC;IAED,OAAO,MAAM,CAAC;AAChB,CAAC,CAAC"}