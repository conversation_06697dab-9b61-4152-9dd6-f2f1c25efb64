(function(){"use strict";/**
* @vue/shared v3.5.13
* (c) 2018-present <PERSON><PERSON> (<PERSON>) You and Vue contributors
* @license MIT
**//*! #__NO_SIDE_EFFECTS__ */function Es(e){const t=Object.create(null);for(const n of e.split(","))t[n]=1;return n=>n in t}const le={},on=[],Ke=()=>{},Tc=()=>!1,ao=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&(e.charCodeAt(2)>122||e.charCodeAt(2)<97),bs=e=>e.startsWith("onUpdate:"),be=Object.assign,ws=(e,t)=>{const n=e.indexOf(t);n>-1&&e.splice(n,1)},Oc=Object.prototype.hasOwnProperty,oe=(e,t)=>Oc.call(e,t),Y=Array.isArray,En=e=>co(e)==="[object Map]",Ac=e=>co(e)==="[object Set]",K=e=>typeof e=="function",me=e=>typeof e=="string",Ht=e=>typeof e=="symbol",de=e=>e!==null&&typeof e=="object",Qr=e=>(de(e)||K(e))&&K(e.then)&&K(e.catch),Cc=Object.prototype.toString,co=e=>Cc.call(e),Dc=e=>co(e).slice(8,-1),Pc=e=>co(e)==="[object Object]",Ss=e=>me(e)&&e!=="NaN"&&e[0]!=="-"&&""+parseInt(e,10)===e,bn=Es(",key,ref,ref_for,ref_key,onVnodeBeforeMount,onVnodeMounted,onVnodeBeforeUpdate,onVnodeUpdated,onVnodeBeforeUnmount,onVnodeUnmounted"),fo=e=>{const t=Object.create(null);return n=>t[n]||(t[n]=e(n))},Ic=/-(\w)/g,He=fo(e=>e.replace(Ic,(t,n)=>n?n.toUpperCase():"")),Rc=/\B([A-Z])/g,St=fo(e=>e.replace(Rc,"-$1").toLowerCase()),po=fo(e=>e.charAt(0).toUpperCase()+e.slice(1)),xs=fo(e=>e?`on${po(e)}`:""),xt=(e,t)=>!Object.is(e,t),Ts=(e,...t)=>{for(let n=0;n<e.length;n++)e[n](...t)},ei=(e,t,n,o=!1)=>{Object.defineProperty(e,t,{configurable:!0,enumerable:!1,writable:o,value:n})},kc=e=>{const t=parseFloat(e);return isNaN(t)?e:t};let ti;const ho=()=>ti||(ti=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:typeof global<"u"?global:{});function Ne(e){if(Y(e)){const t={};for(let n=0;n<e.length;n++){const o=e[n],s=me(o)?$c(o):Ne(o);if(s)for(const r in s)t[r]=s[r]}return t}else if(me(e)||de(e))return e}const Nc=/;(?![^(]*\))/g,Vc=/:([^]+)/,Lc=/\/\*[^]*?\*\//g;function $c(e){const t={};return e.replace(Lc,"").split(Nc).forEach(n=>{if(n){const o=n.split(Vc);o.length>1&&(t[o[0].trim()]=o[1].trim())}}),t}function ht(e){let t="";if(me(e))t=e;else if(Y(e))for(let n=0;n<e.length;n++){const o=ht(e[n]);o&&(t+=o+" ")}else if(de(e))for(const n in e)e[n]&&(t+=n+" ");return t.trim()}function Mc(e){if(!e)return null;let{class:t,style:n}=e;return t&&!me(t)&&(e.class=ht(t)),n&&(e.style=Ne(n)),e}const Uc=Es("itemscope,allowfullscreen,formnovalidate,ismap,nomodule,novalidate,readonly");function ni(e){return!!e||e===""}var Fc={TERM_PROGRAM:"iTerm.app",NODE:"/Users/<USER>/Library/Application Support/fnm/node-versions/v20.17.0/installation/bin/node",INIT_CWD:"/Users/<USER>/g/devtools-next/packages/overlay",SHELL:"/bin/zsh",TERM:"xterm-256color",npm_config_shamefully_hoist:"true",npm_config_registry:"https://registry.npmjs.org/",USER:"arlo",PNPM_SCRIPT_SRC_DIR:"/Users/<USER>/g/devtools-next/packages/overlay",npm_config_strict_peer_dependencies:"",__CF_USER_TEXT_ENCODING:"0x1F5:0x19:0x34",npm_execpath:"/Users/<USER>/g/devtools-next/node_modules/.pnpm/pnpm@10.7.0/node_modules/pnpm/bin/pnpm.cjs",npm_config_verify_deps_before_run:"false",npm_config_frozen_lockfile:"",npm_config_catalog:'{"@iconify/json":"^2.2.321","@types/node":"^22.13.14","@unocss/reset":"^66.0.0","@vitejs/plugin-vue":"^5.2.3","@vueuse/core":"^12.8.2","@vueuse/integrations":"^12.8.2","colord":"^2.9.3","execa":"^9.5.2","floating-vue":"5.2.2","mitt":"^3.0.1","pathe":"^2.0.3","perfect-debounce":"^1.0.0","pinia":"^3.0.1","sass-embedded":"^1.86.0","serve":"^14.2.4","shiki":"^3.2.1","splitpanes":"^4.0.3","typescript":"^5.8.2","unocss":"^66.0.0","unplugin-auto-import":"^19.1.2","vite":"^6.2.1","vite-hot-client":"^2.0.4","vite-plugin-dts":"^4.5.3","vite-plugin-inspect":"0.8.9","vue":"^3.5.13","vue-router":"^4.5.0","vue-virtual-scroller":"2.0.0-beta.8"}',PATH:"/Users/<USER>/g/devtools-next/packages/overlay/node_modules/.bin:/Users/<USER>/g/devtools-next/node_modules/.pnpm/pnpm@10.7.0/node_modules/pnpm/dist/node-gyp-bin:/Users/<USER>/g/devtools-next/node_modules/.bin:/Users/<USER>/g/devtools-next/node_modules/.bin:/Users/<USER>/g/devtools-next/node_modules/.pnpm/pnpm@10.7.0/node_modules/pnpm/dist/node-gyp-bin:/Users/<USER>/g/devtools-next/node_modules/.bin:/Users/<USER>/g/devtools-next/node_modules/.bin:/Users/<USER>/.cache/node/corepack/v1/pnpm/10.7.0/dist/node-gyp-bin:/Users/<USER>/g/devtools-next/node_modules/.bin:/Users/<USER>/.local/bin:/Users/<USER>/Library/pnpm:/Users/<USER>/.local/state/fnm_multishells/24509_1745808020460/bin:/opt/homebrew/bin:/Users/<USER>/.cargo/bin:/libexec/bin:/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/Library/Apple/usr/bin:/Applications/WebStorm.app/Contents/MacOS:/Users/<USER>/g/devtools-next/node_modules/.bin:/Users/<USER>/g/node_modules/.bin:/Users/<USER>/node_modules/.bin:/Users/<USER>/.bin:/node_modules/.bin:/Users/<USER>/g/devtools-next/node_modules/.bin:/Users/<USER>/g/node_modules/.bin:/Users/<USER>/node_modules/.bin:/Users/<USER>/.bin:/node_modules/.bin",npm_command:"run-script",PWD:"/Users/<USER>/g/devtools-next/packages/overlay",npm_lifecycle_event:"build",npm_package_name:"@vue/devtools-overlay",LANG:"zh_CN.UTF-8",NODE_PATH:"/Users/<USER>/g/devtools-next/node_modules/.pnpm/vite@6.2.1_@types+node@22.13.14_jiti@2.4.2_sass-embedded@1.86.0_terser@5.37.0_tsx@4.19.3_yaml@2.7.0/node_modules/vite/bin/node_modules:/Users/<USER>/g/devtools-next/node_modules/.pnpm/vite@6.2.1_@types+node@22.13.14_jiti@2.4.2_sass-embedded@1.86.0_terser@5.37.0_tsx@4.19.3_yaml@2.7.0/node_modules/vite/node_modules:/Users/<USER>/g/devtools-next/node_modules/.pnpm/vite@6.2.1_@types+node@22.13.14_jiti@2.4.2_sass-embedded@1.86.0_terser@5.37.0_tsx@4.19.3_yaml@2.7.0/node_modules:/Users/<USER>/g/devtools-next/node_modules/.pnpm/node_modules:/Users/<USER>/g/devtools-next/node_modules/.pnpm/pnpm@10.7.0/node_modules/pnpm/bin/node_modules:/Users/<USER>/g/devtools-next/node_modules/.pnpm/pnpm@10.7.0/node_modules/pnpm/node_modules:/Users/<USER>/g/devtools-next/node_modules/.pnpm/pnpm@10.7.0/node_modules:/Users/<USER>/g/devtools-next/node_modules/.pnpm/node_modules",TURBO_HASH:"b5de7635207ba6ab",npm_package_engines_node:">=v14.21.3",npm_config_node_gyp:"/Users/<USER>/g/devtools-next/node_modules/.pnpm/pnpm@10.7.0/node_modules/pnpm/dist/node_modules/node-gyp/bin/node-gyp.js",npm_config_side_effects_cache:"",npm_package_version:"7.7.6",HOME:"/Users/<USER>",SHLVL:"0",npm_lifecycle_script:"vite build",npm_config_user_agent:"pnpm/10.7.0 npm/? node/v20.17.0 darwin arm64",npm_node_execpath:"/Users/<USER>/Library/Application Support/fnm/node-versions/v20.17.0/installation/bin/node",npm_config_shell_emulator:"true",COLORTERM:"truecolor",NODE_ENV:"production"};let Te;class Bc{constructor(t=!1){this.detached=t,this._active=!0,this.effects=[],this.cleanups=[],this._isPaused=!1,this.parent=Te,!t&&Te&&(this.index=(Te.scopes||(Te.scopes=[])).push(this)-1)}get active(){return this._active}pause(){if(this._active){this._isPaused=!0;let t,n;if(this.scopes)for(t=0,n=this.scopes.length;t<n;t++)this.scopes[t].pause();for(t=0,n=this.effects.length;t<n;t++)this.effects[t].pause()}}resume(){if(this._active&&this._isPaused){this._isPaused=!1;let t,n;if(this.scopes)for(t=0,n=this.scopes.length;t<n;t++)this.scopes[t].resume();for(t=0,n=this.effects.length;t<n;t++)this.effects[t].resume()}}run(t){if(this._active){const n=Te;try{return Te=this,t()}finally{Te=n}}}on(){Te=this}off(){Te=this.parent}stop(t){if(this._active){this._active=!1;let n,o;for(n=0,o=this.effects.length;n<o;n++)this.effects[n].stop();for(this.effects.length=0,n=0,o=this.cleanups.length;n<o;n++)this.cleanups[n]();if(this.cleanups.length=0,this.scopes){for(n=0,o=this.scopes.length;n<o;n++)this.scopes[n].stop(!0);this.scopes.length=0}if(!this.detached&&this.parent&&!t){const s=this.parent.scopes.pop();s&&s!==this&&(this.parent.scopes[this.index]=s,s.index=this.index)}this.parent=void 0}}}function oi(){return Te}function Hc(e,t=!1){Te&&Te.cleanups.push(e)}let ae;const Os=new WeakSet;class si{constructor(t){this.fn=t,this.deps=void 0,this.depsTail=void 0,this.flags=5,this.next=void 0,this.cleanup=void 0,this.scheduler=void 0,Te&&Te.active&&Te.effects.push(this)}pause(){this.flags|=64}resume(){this.flags&64&&(this.flags&=-65,Os.has(this)&&(Os.delete(this),this.trigger()))}notify(){this.flags&2&&!(this.flags&32)||this.flags&8||ii(this)}run(){if(!(this.flags&1))return this.fn();this.flags|=2,fi(this),li(this);const t=ae,n=We;ae=this,We=!0;try{return this.fn()}finally{ui(this),ae=t,We=n,this.flags&=-3}}stop(){if(this.flags&1){for(let t=this.deps;t;t=t.nextDep)Ps(t);this.deps=this.depsTail=void 0,fi(this),this.onStop&&this.onStop(),this.flags&=-2}}trigger(){this.flags&64?Os.add(this):this.scheduler?this.scheduler():this.runIfDirty()}runIfDirty(){Ds(this)&&this.run()}get dirty(){return Ds(this)}}let ri=0,wn,Sn;function ii(e,t=!1){if(e.flags|=8,t){e.next=Sn,Sn=e;return}e.next=wn,wn=e}function As(){ri++}function Cs(){if(--ri>0)return;if(Sn){let t=Sn;for(Sn=void 0;t;){const n=t.next;t.next=void 0,t.flags&=-9,t=n}}let e;for(;wn;){let t=wn;for(wn=void 0;t;){const n=t.next;if(t.next=void 0,t.flags&=-9,t.flags&1)try{t.trigger()}catch(o){e||(e=o)}t=n}}if(e)throw e}function li(e){for(let t=e.deps;t;t=t.nextDep)t.version=-1,t.prevActiveLink=t.dep.activeLink,t.dep.activeLink=t}function ui(e){let t,n=e.depsTail,o=n;for(;o;){const s=o.prevDep;o.version===-1?(o===n&&(n=s),Ps(o),zc(o)):t=o,o.dep.activeLink=o.prevActiveLink,o.prevActiveLink=void 0,o=s}e.deps=t,e.depsTail=n}function Ds(e){for(let t=e.deps;t;t=t.nextDep)if(t.dep.version!==t.version||t.dep.computed&&(ai(t.dep.computed)||t.dep.version!==t.version))return!0;return!!e._dirty}function ai(e){if(e.flags&4&&!(e.flags&16)||(e.flags&=-17,e.globalVersion===xn))return;e.globalVersion=xn;const t=e.dep;if(e.flags|=2,t.version>0&&!e.isSSR&&e.deps&&!Ds(e)){e.flags&=-3;return}const n=ae,o=We;ae=e,We=!0;try{li(e);const s=e.fn(e._value);(t.version===0||xt(s,e._value))&&(e._value=s,t.version++)}catch(s){throw t.version++,s}finally{ae=n,We=o,ui(e),e.flags&=-3}}function Ps(e,t=!1){const{dep:n,prevSub:o,nextSub:s}=e;if(o&&(o.nextSub=s,e.prevSub=void 0),s&&(s.prevSub=o,e.nextSub=void 0),n.subs===e&&(n.subs=o,!o&&n.computed)){n.computed.flags&=-5;for(let r=n.computed.deps;r;r=r.nextDep)Ps(r,!0)}!t&&!--n.sc&&n.map&&n.map.delete(n.key)}function zc(e){const{prevDep:t,nextDep:n}=e;t&&(t.nextDep=n,e.prevDep=void 0),n&&(n.prevDep=t,e.nextDep=void 0)}let We=!0;const ci=[];function _t(){ci.push(We),We=!1}function mt(){const e=ci.pop();We=e===void 0?!0:e}function fi(e){const{cleanup:t}=e;if(e.cleanup=void 0,t){const n=ae;ae=void 0;try{t()}finally{ae=n}}}let xn=0;class jc{constructor(t,n){this.sub=t,this.dep=n,this.version=n.version,this.nextDep=this.prevDep=this.nextSub=this.prevSub=this.prevActiveLink=void 0}}class _o{constructor(t){this.computed=t,this.version=0,this.activeLink=void 0,this.subs=void 0,this.map=void 0,this.key=void 0,this.sc=0}track(t){if(!ae||!We||ae===this.computed)return;let n=this.activeLink;if(n===void 0||n.sub!==ae)n=this.activeLink=new jc(ae,this),ae.deps?(n.prevDep=ae.depsTail,ae.depsTail.nextDep=n,ae.depsTail=n):ae.deps=ae.depsTail=n,di(n);else if(n.version===-1&&(n.version=this.version,n.nextDep)){const o=n.nextDep;o.prevDep=n.prevDep,n.prevDep&&(n.prevDep.nextDep=o),n.prevDep=ae.depsTail,n.nextDep=void 0,ae.depsTail.nextDep=n,ae.depsTail=n,ae.deps===n&&(ae.deps=o)}return n}trigger(t){this.version++,xn++,this.notify(t)}notify(t){As();try{Fc.NODE_ENV;for(let n=this.subs;n;n=n.prevSub)n.sub.notify()&&n.sub.dep.notify()}finally{Cs()}}}function di(e){if(e.dep.sc++,e.sub.flags&4){const t=e.dep.computed;if(t&&!e.dep.subs){t.flags|=20;for(let o=t.deps;o;o=o.nextDep)di(o)}const n=e.dep.subs;n!==e&&(e.prevSub=n,n&&(n.nextSub=e)),e.dep.subs=e}}const mo=new WeakMap,zt=Symbol(""),Is=Symbol(""),Tn=Symbol("");function we(e,t,n){if(We&&ae){let o=mo.get(e);o||mo.set(e,o=new Map);let s=o.get(n);s||(o.set(n,s=new _o),s.map=o,s.key=n),s.track()}}function gt(e,t,n,o,s,r){const i=mo.get(e);if(!i){xn++;return}const l=u=>{u&&u.trigger()};if(As(),t==="clear")i.forEach(l);else{const u=Y(e),a=u&&Ss(n);if(u&&n==="length"){const c=Number(o);i.forEach((f,h)=>{(h==="length"||h===Tn||!Ht(h)&&h>=c)&&l(f)})}else switch((n!==void 0||i.has(void 0))&&l(i.get(n)),a&&l(i.get(Tn)),t){case"add":u?a&&l(i.get("length")):(l(i.get(zt)),En(e)&&l(i.get(Is)));break;case"delete":u||(l(i.get(zt)),En(e)&&l(i.get(Is)));break;case"set":En(e)&&l(i.get(zt));break}}Cs()}function Kc(e,t){const n=mo.get(e);return n&&n.get(t)}function sn(e){const t=ne(e);return t===e?t:(we(t,"iterate",Tn),Ge(e)?t:t.map(Oe))}function Rs(e){return we(e=ne(e),"iterate",Tn),e}const Wc={__proto__:null,[Symbol.iterator](){return ks(this,Symbol.iterator,Oe)},concat(...e){return sn(this).concat(...e.map(t=>Y(t)?sn(t):t))},entries(){return ks(this,"entries",e=>(e[1]=Oe(e[1]),e))},every(e,t){return vt(this,"every",e,t,void 0,arguments)},filter(e,t){return vt(this,"filter",e,t,n=>n.map(Oe),arguments)},find(e,t){return vt(this,"find",e,t,Oe,arguments)},findIndex(e,t){return vt(this,"findIndex",e,t,void 0,arguments)},findLast(e,t){return vt(this,"findLast",e,t,Oe,arguments)},findLastIndex(e,t){return vt(this,"findLastIndex",e,t,void 0,arguments)},forEach(e,t){return vt(this,"forEach",e,t,void 0,arguments)},includes(...e){return Ns(this,"includes",e)},indexOf(...e){return Ns(this,"indexOf",e)},join(e){return sn(this).join(e)},lastIndexOf(...e){return Ns(this,"lastIndexOf",e)},map(e,t){return vt(this,"map",e,t,void 0,arguments)},pop(){return On(this,"pop")},push(...e){return On(this,"push",e)},reduce(e,...t){return pi(this,"reduce",e,t)},reduceRight(e,...t){return pi(this,"reduceRight",e,t)},shift(){return On(this,"shift")},some(e,t){return vt(this,"some",e,t,void 0,arguments)},splice(...e){return On(this,"splice",e)},toReversed(){return sn(this).toReversed()},toSorted(e){return sn(this).toSorted(e)},toSpliced(...e){return sn(this).toSpliced(...e)},unshift(...e){return On(this,"unshift",e)},values(){return ks(this,"values",Oe)}};function ks(e,t,n){const o=Rs(e),s=o[t]();return o!==e&&!Ge(e)&&(s._next=s.next,s.next=()=>{const r=s._next();return r.value&&(r.value=n(r.value)),r}),s}const Gc=Array.prototype;function vt(e,t,n,o,s,r){const i=Rs(e),l=i!==e&&!Ge(e),u=i[t];if(u!==Gc[t]){const f=u.apply(e,r);return l?Oe(f):f}let a=n;i!==e&&(l?a=function(f,h){return n.call(this,Oe(f),h,e)}:n.length>2&&(a=function(f,h){return n.call(this,f,h,e)}));const c=u.call(i,a,o);return l&&s?s(c):c}function pi(e,t,n,o){const s=Rs(e);let r=n;return s!==e&&(Ge(e)?n.length>3&&(r=function(i,l,u){return n.call(this,i,l,u,e)}):r=function(i,l,u){return n.call(this,i,Oe(l),u,e)}),s[t](r,...o)}function Ns(e,t,n){const o=ne(e);we(o,"iterate",Tn);const s=o[t](...n);return(s===-1||s===!1)&&Ls(n[0])?(n[0]=ne(n[0]),o[t](...n)):s}function On(e,t,n=[]){_t(),As();const o=ne(e)[t].apply(e,n);return Cs(),mt(),o}const Yc=Es("__proto__,__v_isRef,__isVue"),hi=new Set(Object.getOwnPropertyNames(Symbol).filter(e=>e!=="arguments"&&e!=="caller").map(e=>Symbol[e]).filter(Ht));function qc(e){Ht(e)||(e=String(e));const t=ne(this);return we(t,"has",e),t.hasOwnProperty(e)}class _i{constructor(t=!1,n=!1){this._isReadonly=t,this._isShallow=n}get(t,n,o){if(n==="__v_skip")return t.__v_skip;const s=this._isReadonly,r=this._isShallow;if(n==="__v_isReactive")return!s;if(n==="__v_isReadonly")return s;if(n==="__v_isShallow")return r;if(n==="__v_raw")return o===(s?r?bi:Ei:r?yi:vi).get(t)||Object.getPrototypeOf(t)===Object.getPrototypeOf(o)?t:void 0;const i=Y(t);if(!s){let u;if(i&&(u=Wc[n]))return u;if(n==="hasOwnProperty")return qc}const l=Reflect.get(t,n,ve(t)?t:o);return(Ht(n)?hi.has(n):Yc(n))||(s||we(t,"get",n),r)?l:ve(l)?i&&Ss(n)?l:l.value:de(l)?s?An(l):rn(l):l}}class mi extends _i{constructor(t=!1){super(!1,t)}set(t,n,o,s){let r=t[n];if(!this._isShallow){const u=jt(r);if(!Ge(o)&&!jt(o)&&(r=ne(r),o=ne(o)),!Y(t)&&ve(r)&&!ve(o))return u?!1:(r.value=o,!0)}const i=Y(t)&&Ss(n)?Number(n)<t.length:oe(t,n),l=Reflect.set(t,n,o,ve(t)?t:s);return t===ne(s)&&(i?xt(o,r)&&gt(t,"set",n,o):gt(t,"add",n,o)),l}deleteProperty(t,n){const o=oe(t,n);t[n];const s=Reflect.deleteProperty(t,n);return s&&o&&gt(t,"delete",n,void 0),s}has(t,n){const o=Reflect.has(t,n);return(!Ht(n)||!hi.has(n))&&we(t,"has",n),o}ownKeys(t){return we(t,"iterate",Y(t)?"length":zt),Reflect.ownKeys(t)}}class gi extends _i{constructor(t=!1){super(!0,t)}set(t,n){return!0}deleteProperty(t,n){return!0}}const Xc=new mi,Zc=new gi,Jc=new mi(!0),Qc=new gi(!0),Vs=e=>e,go=e=>Reflect.getPrototypeOf(e);function ef(e,t,n){return function(...o){const s=this.__v_raw,r=ne(s),i=En(r),l=e==="entries"||e===Symbol.iterator&&i,u=e==="keys"&&i,a=s[e](...o),c=n?Vs:t?$s:Oe;return!t&&we(r,"iterate",u?Is:zt),{next(){const{value:f,done:h}=a.next();return h?{value:f,done:h}:{value:l?[c(f[0]),c(f[1])]:c(f),done:h}},[Symbol.iterator](){return this}}}}function vo(e){return function(...t){return e==="delete"?!1:e==="clear"?void 0:this}}function tf(e,t){const n={get(s){const r=this.__v_raw,i=ne(r),l=ne(s);e||(xt(s,l)&&we(i,"get",s),we(i,"get",l));const{has:u}=go(i),a=t?Vs:e?$s:Oe;if(u.call(i,s))return a(r.get(s));if(u.call(i,l))return a(r.get(l));r!==i&&r.get(s)},get size(){const s=this.__v_raw;return!e&&we(ne(s),"iterate",zt),Reflect.get(s,"size",s)},has(s){const r=this.__v_raw,i=ne(r),l=ne(s);return e||(xt(s,l)&&we(i,"has",s),we(i,"has",l)),s===l?r.has(s):r.has(s)||r.has(l)},forEach(s,r){const i=this,l=i.__v_raw,u=ne(l),a=t?Vs:e?$s:Oe;return!e&&we(u,"iterate",zt),l.forEach((c,f)=>s.call(r,a(c),a(f),i))}};return be(n,e?{add:vo("add"),set:vo("set"),delete:vo("delete"),clear:vo("clear")}:{add(s){!t&&!Ge(s)&&!jt(s)&&(s=ne(s));const r=ne(this);return go(r).has.call(r,s)||(r.add(s),gt(r,"add",s,s)),this},set(s,r){!t&&!Ge(r)&&!jt(r)&&(r=ne(r));const i=ne(this),{has:l,get:u}=go(i);let a=l.call(i,s);a||(s=ne(s),a=l.call(i,s));const c=u.call(i,s);return i.set(s,r),a?xt(r,c)&&gt(i,"set",s,r):gt(i,"add",s,r),this},delete(s){const r=ne(this),{has:i,get:l}=go(r);let u=i.call(r,s);u||(s=ne(s),u=i.call(r,s)),l&&l.call(r,s);const a=r.delete(s);return u&&gt(r,"delete",s,void 0),a},clear(){const s=ne(this),r=s.size!==0,i=s.clear();return r&&gt(s,"clear",void 0,void 0),i}}),["keys","values","entries",Symbol.iterator].forEach(s=>{n[s]=ef(s,e,t)}),n}function yo(e,t){const n=tf(e,t);return(o,s,r)=>s==="__v_isReactive"?!e:s==="__v_isReadonly"?e:s==="__v_raw"?o:Reflect.get(oe(n,s)&&s in o?n:o,s,r)}const nf={get:yo(!1,!1)},of={get:yo(!1,!0)},sf={get:yo(!0,!1)},rf={get:yo(!0,!0)},vi=new WeakMap,yi=new WeakMap,Ei=new WeakMap,bi=new WeakMap;function lf(e){switch(e){case"Object":case"Array":return 1;case"Map":case"Set":case"WeakMap":case"WeakSet":return 2;default:return 0}}function uf(e){return e.__v_skip||!Object.isExtensible(e)?0:lf(Dc(e))}function rn(e){return jt(e)?e:bo(e,!1,Xc,nf,vi)}function af(e){return bo(e,!1,Jc,of,yi)}function An(e){return bo(e,!0,Zc,sf,Ei)}function Eo(e){return bo(e,!0,Qc,rf,bi)}function bo(e,t,n,o,s){if(!de(e)||e.__v_raw&&!(t&&e.__v_isReactive))return e;const r=s.get(e);if(r)return r;const i=uf(e);if(i===0)return e;const l=new Proxy(e,i===2?o:n);return s.set(e,l),l}function Cn(e){return jt(e)?Cn(e.__v_raw):!!(e&&e.__v_isReactive)}function jt(e){return!!(e&&e.__v_isReadonly)}function Ge(e){return!!(e&&e.__v_isShallow)}function Ls(e){return e?!!e.__v_raw:!1}function ne(e){const t=e&&e.__v_raw;return t?ne(t):e}function cf(e){return!oe(e,"__v_skip")&&Object.isExtensible(e)&&ei(e,"__v_skip",!0),e}const Oe=e=>de(e)?rn(e):e,$s=e=>de(e)?An(e):e;function ve(e){return e?e.__v_isRef===!0:!1}function Ve(e){return wi(e,!1)}function Le(e){return wi(e,!0)}function wi(e,t){return ve(e)?e:new ff(e,t)}class ff{constructor(t,n){this.dep=new _o,this.__v_isRef=!0,this.__v_isShallow=!1,this._rawValue=n?t:ne(t),this._value=n?t:Oe(t),this.__v_isShallow=n}get value(){return this.dep.track(),this._value}set value(t){const n=this._rawValue,o=this.__v_isShallow||Ge(t)||jt(t);t=o?t:ne(t),xt(t,n)&&(this._rawValue=t,this._value=o?t:Oe(t),this.dep.trigger())}}function J(e){return ve(e)?e.value:e}function Ae(e){return K(e)?e():J(e)}const df={get:(e,t,n)=>t==="__v_raw"?e:J(Reflect.get(e,t,n)),set:(e,t,n,o)=>{const s=e[t];return ve(s)&&!ve(n)?(s.value=n,!0):Reflect.set(e,t,n,o)}};function Si(e){return Cn(e)?e:new Proxy(e,df)}class pf{constructor(t){this.__v_isRef=!0,this._value=void 0;const n=this.dep=new _o,{get:o,set:s}=t(n.track.bind(n),n.trigger.bind(n));this._get=o,this._set=s}get value(){return this._value=this._get()}set value(t){this._set(t)}}function hf(e){return new pf(e)}class _f{constructor(t,n,o){this._object=t,this._key=n,this._defaultValue=o,this.__v_isRef=!0,this._value=void 0}get value(){const t=this._object[this._key];return this._value=t===void 0?this._defaultValue:t}set value(t){this._object[this._key]=t}get dep(){return Kc(ne(this._object),this._key)}}class mf{constructor(t){this._getter=t,this.__v_isRef=!0,this.__v_isReadonly=!0,this._value=void 0}get value(){return this._value=this._getter()}}function gf(e,t,n){return ve(e)?e:K(e)?new mf(e):de(e)&&arguments.length>1?vf(e,t,n):Ve(e)}function vf(e,t,n){const o=e[t];return ve(o)?o:new _f(e,t,n)}class yf{constructor(t,n,o){this.fn=t,this.setter=n,this._value=void 0,this.dep=new _o(this),this.__v_isRef=!0,this.deps=void 0,this.depsTail=void 0,this.flags=16,this.globalVersion=xn-1,this.next=void 0,this.effect=this,this.__v_isReadonly=!n,this.isSSR=o}notify(){if(this.flags|=16,!(this.flags&8)&&ae!==this)return ii(this,!0),!0}get value(){const t=this.dep.track();return ai(this),t&&(t.version=this.dep.version),this._value}set value(t){this.setter&&this.setter(t)}}function Ef(e,t,n=!1){let o,s;return K(e)?o=e:(o=e.get,s=e.set),new yf(o,s,n)}const wo={},So=new WeakMap;let Kt;function bf(e,t=!1,n=Kt){if(n){let o=So.get(n);o||So.set(n,o=[]),o.push(e)}}function wf(e,t,n=le){const{immediate:o,deep:s,once:r,scheduler:i,augmentJob:l,call:u}=n,a=b=>s?b:Ge(b)||s===!1||s===0?yt(b,1):yt(b);let c,f,h,d,_=!1,v=!1;if(ve(e)?(f=()=>e.value,_=Ge(e)):Cn(e)?(f=()=>a(e),_=!0):Y(e)?(v=!0,_=e.some(b=>Cn(b)||Ge(b)),f=()=>e.map(b=>{if(ve(b))return b.value;if(Cn(b))return a(b);if(K(b))return u?u(b,2):b()})):K(e)?t?f=u?()=>u(e,2):e:f=()=>{if(h){_t();try{h()}finally{mt()}}const b=Kt;Kt=c;try{return u?u(e,3,[d]):e(d)}finally{Kt=b}}:f=Ke,t&&s){const b=f,P=s===!0?1/0:s;f=()=>yt(b(),P)}const y=oi(),g=()=>{c.stop(),y&&y.active&&ws(y.effects,c)};if(r&&t){const b=t;t=(...P)=>{b(...P),g()}}let x=v?new Array(e.length).fill(wo):wo;const D=b=>{if(!(!(c.flags&1)||!c.dirty&&!b))if(t){const P=c.run();if(s||_||(v?P.some((U,H)=>xt(U,x[H])):xt(P,x))){h&&h();const U=Kt;Kt=c;try{const H=[P,x===wo?void 0:v&&x[0]===wo?[]:x,d];u?u(t,3,H):t(...H),x=P}finally{Kt=U}}}else c.run()};return l&&l(D),c=new si(f),c.scheduler=i?()=>i(D,!1):D,d=b=>bf(b,!1,c),h=c.onStop=()=>{const b=So.get(c);if(b){if(u)u(b,4);else for(const P of b)P();So.delete(c)}},t?o?D(!0):x=c.run():i?i(D.bind(null,!0),!0):c.run(),g.pause=c.pause.bind(c),g.resume=c.resume.bind(c),g.stop=g,g}function yt(e,t=1/0,n){if(t<=0||!de(e)||e.__v_skip||(n=n||new Set,n.has(e)))return e;if(n.add(e),t--,ve(e))yt(e.value,t,n);else if(Y(e))for(let o=0;o<e.length;o++)yt(e[o],t,n);else if(Ac(e)||En(e))e.forEach(o=>{yt(o,t,n)});else if(Pc(e)){for(const o in e)yt(e[o],t,n);for(const o of Object.getOwnPropertySymbols(e))Object.prototype.propertyIsEnumerable.call(e,o)&&yt(e[o],t,n)}return e}var Tt={TERM_PROGRAM:"iTerm.app",NODE:"/Users/<USER>/Library/Application Support/fnm/node-versions/v20.17.0/installation/bin/node",INIT_CWD:"/Users/<USER>/g/devtools-next/packages/overlay",SHELL:"/bin/zsh",TERM:"xterm-256color",npm_config_shamefully_hoist:"true",npm_config_registry:"https://registry.npmjs.org/",USER:"arlo",PNPM_SCRIPT_SRC_DIR:"/Users/<USER>/g/devtools-next/packages/overlay",npm_config_strict_peer_dependencies:"",__CF_USER_TEXT_ENCODING:"0x1F5:0x19:0x34",npm_execpath:"/Users/<USER>/g/devtools-next/node_modules/.pnpm/pnpm@10.7.0/node_modules/pnpm/bin/pnpm.cjs",npm_config_verify_deps_before_run:"false",npm_config_frozen_lockfile:"",npm_config_catalog:'{"@iconify/json":"^2.2.321","@types/node":"^22.13.14","@unocss/reset":"^66.0.0","@vitejs/plugin-vue":"^5.2.3","@vueuse/core":"^12.8.2","@vueuse/integrations":"^12.8.2","colord":"^2.9.3","execa":"^9.5.2","floating-vue":"5.2.2","mitt":"^3.0.1","pathe":"^2.0.3","perfect-debounce":"^1.0.0","pinia":"^3.0.1","sass-embedded":"^1.86.0","serve":"^14.2.4","shiki":"^3.2.1","splitpanes":"^4.0.3","typescript":"^5.8.2","unocss":"^66.0.0","unplugin-auto-import":"^19.1.2","vite":"^6.2.1","vite-hot-client":"^2.0.4","vite-plugin-dts":"^4.5.3","vite-plugin-inspect":"0.8.9","vue":"^3.5.13","vue-router":"^4.5.0","vue-virtual-scroller":"2.0.0-beta.8"}',PATH:"/Users/<USER>/g/devtools-next/packages/overlay/node_modules/.bin:/Users/<USER>/g/devtools-next/node_modules/.pnpm/pnpm@10.7.0/node_modules/pnpm/dist/node-gyp-bin:/Users/<USER>/g/devtools-next/node_modules/.bin:/Users/<USER>/g/devtools-next/node_modules/.bin:/Users/<USER>/g/devtools-next/node_modules/.pnpm/pnpm@10.7.0/node_modules/pnpm/dist/node-gyp-bin:/Users/<USER>/g/devtools-next/node_modules/.bin:/Users/<USER>/g/devtools-next/node_modules/.bin:/Users/<USER>/.cache/node/corepack/v1/pnpm/10.7.0/dist/node-gyp-bin:/Users/<USER>/g/devtools-next/node_modules/.bin:/Users/<USER>/.local/bin:/Users/<USER>/Library/pnpm:/Users/<USER>/.local/state/fnm_multishells/24509_1745808020460/bin:/opt/homebrew/bin:/Users/<USER>/.cargo/bin:/libexec/bin:/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/Library/Apple/usr/bin:/Applications/WebStorm.app/Contents/MacOS:/Users/<USER>/g/devtools-next/node_modules/.bin:/Users/<USER>/g/node_modules/.bin:/Users/<USER>/node_modules/.bin:/Users/<USER>/.bin:/node_modules/.bin:/Users/<USER>/g/devtools-next/node_modules/.bin:/Users/<USER>/g/node_modules/.bin:/Users/<USER>/node_modules/.bin:/Users/<USER>/.bin:/node_modules/.bin",npm_command:"run-script",PWD:"/Users/<USER>/g/devtools-next/packages/overlay",npm_lifecycle_event:"build",npm_package_name:"@vue/devtools-overlay",LANG:"zh_CN.UTF-8",NODE_PATH:"/Users/<USER>/g/devtools-next/node_modules/.pnpm/vite@6.2.1_@types+node@22.13.14_jiti@2.4.2_sass-embedded@1.86.0_terser@5.37.0_tsx@4.19.3_yaml@2.7.0/node_modules/vite/bin/node_modules:/Users/<USER>/g/devtools-next/node_modules/.pnpm/vite@6.2.1_@types+node@22.13.14_jiti@2.4.2_sass-embedded@1.86.0_terser@5.37.0_tsx@4.19.3_yaml@2.7.0/node_modules/vite/node_modules:/Users/<USER>/g/devtools-next/node_modules/.pnpm/vite@6.2.1_@types+node@22.13.14_jiti@2.4.2_sass-embedded@1.86.0_terser@5.37.0_tsx@4.19.3_yaml@2.7.0/node_modules:/Users/<USER>/g/devtools-next/node_modules/.pnpm/node_modules:/Users/<USER>/g/devtools-next/node_modules/.pnpm/pnpm@10.7.0/node_modules/pnpm/bin/node_modules:/Users/<USER>/g/devtools-next/node_modules/.pnpm/pnpm@10.7.0/node_modules/pnpm/node_modules:/Users/<USER>/g/devtools-next/node_modules/.pnpm/pnpm@10.7.0/node_modules:/Users/<USER>/g/devtools-next/node_modules/.pnpm/node_modules",TURBO_HASH:"b5de7635207ba6ab",npm_package_engines_node:">=v14.21.3",npm_config_node_gyp:"/Users/<USER>/g/devtools-next/node_modules/.pnpm/pnpm@10.7.0/node_modules/pnpm/dist/node_modules/node-gyp/bin/node-gyp.js",npm_config_side_effects_cache:"",npm_package_version:"7.7.6",HOME:"/Users/<USER>",SHLVL:"0",npm_lifecycle_script:"vite build",npm_config_user_agent:"pnpm/10.7.0 npm/? node/v20.17.0 darwin arm64",npm_node_execpath:"/Users/<USER>/Library/Application Support/fnm/node-versions/v20.17.0/installation/bin/node",npm_config_shell_emulator:"true",COLORTERM:"truecolor",NODE_ENV:"production"};const Dn=[];let Ms=!1;function Sf(e,...t){if(Ms)return;Ms=!0,_t();const n=Dn.length?Dn[Dn.length-1].component:null,o=n&&n.appContext.config.warnHandler,s=xf();if(o)ln(o,n,11,[e+t.map(r=>{var i,l;return(l=(i=r.toString)==null?void 0:i.call(r))!=null?l:JSON.stringify(r)}).join(""),n&&n.proxy,s.map(({vnode:r})=>`at <${vl(n,r.type)}>`).join(`
`),s]);else{const r=[`[Vue warn]: ${e}`,...t];s.length&&r.push(`
`,...Tf(s)),console.warn(...r)}mt(),Ms=!1}function xf(){let e=Dn[Dn.length-1];if(!e)return[];const t=[];for(;e;){const n=t[0];n&&n.vnode===e?n.recurseCount++:t.push({vnode:e,recurseCount:0});const o=e.component&&e.component.parent;e=o&&o.vnode}return t}function Tf(e){const t=[];return e.forEach((n,o)=>{t.push(...o===0?[]:[`
`],...Of(n))}),t}function Of({vnode:e,recurseCount:t}){const n=t>0?`... (${t} recursive calls)`:"",o=e.component?e.component.parent==null:!1,s=` at <${vl(e.component,e.type,o)}`,r=">"+n;return e.props?[s,...Af(e.props),r]:[s+r]}function Af(e){const t=[],n=Object.keys(e);return n.slice(0,3).forEach(o=>{t.push(...xi(o,e[o]))}),n.length>3&&t.push(" ..."),t}function xi(e,t,n){return me(t)?(t=JSON.stringify(t),n?t:[`${e}=${t}`]):typeof t=="number"||typeof t=="boolean"||t==null?n?t:[`${e}=${t}`]:ve(t)?(t=xi(e,ne(t.value),!0),n?t:[`${e}=Ref<`,t,">"]):K(t)?[`${e}=fn${t.name?`<${t.name}>`:""}`]:(t=ne(t),n?t:[`${e}=`,t])}function ln(e,t,n,o){try{return o?e(...o):e()}catch(s){xo(s,t,n)}}function Qe(e,t,n,o){if(K(e)){const s=ln(e,t,n,o);return s&&Qr(s)&&s.catch(r=>{xo(r,t,n)}),s}if(Y(e)){const s=[];for(let r=0;r<e.length;r++)s.push(Qe(e[r],t,n,o));return s}}function xo(e,t,n,o=!0){const s=t?t.vnode:null,{errorHandler:r,throwUnhandledErrorInProduction:i}=t&&t.appContext.config||le;if(t){let l=t.parent;const u=t.proxy,a=`https://vuejs.org/error-reference/#runtime-${n}`;for(;l;){const c=l.ec;if(c){for(let f=0;f<c.length;f++)if(c[f](e,u,a)===!1)return}l=l.parent}if(r){_t(),ln(r,null,10,[e,u,a]),mt();return}}Cf(e,n,s,o,i)}function Cf(e,t,n,o=!0,s=!1){if(s)throw e;console.error(e)}const Ce=[];let et=-1;const un=[];let Ot=null,an=0;const Ti=Promise.resolve();let To=null;function Oo(e){const t=To||Ti;return e?t.then(this?e.bind(this):e):t}function Df(e){let t=et+1,n=Ce.length;for(;t<n;){const o=t+n>>>1,s=Ce[o],r=Pn(s);r<e||r===e&&s.flags&2?t=o+1:n=o}return t}function Us(e){if(!(e.flags&1)){const t=Pn(e),n=Ce[Ce.length-1];!n||!(e.flags&2)&&t>=Pn(n)?Ce.push(e):Ce.splice(Df(t),0,e),e.flags|=1,Oi()}}function Oi(){To||(To=Ti.then(Di))}function Pf(e){Y(e)?un.push(...e):Ot&&e.id===-1?Ot.splice(an+1,0,e):e.flags&1||(un.push(e),e.flags|=1),Oi()}function Ai(e,t,n=et+1){for(;n<Ce.length;n++){const o=Ce[n];if(o&&o.flags&2){if(e&&o.id!==e.uid)continue;Ce.splice(n,1),n--,o.flags&4&&(o.flags&=-2),o(),o.flags&4||(o.flags&=-2)}}}function Ci(e){if(un.length){const t=[...new Set(un)].sort((n,o)=>Pn(n)-Pn(o));if(un.length=0,Ot){Ot.push(...t);return}for(Ot=t,an=0;an<Ot.length;an++){const n=Ot[an];n.flags&4&&(n.flags&=-2),n.flags&8||n(),n.flags&=-2}Ot=null,an=0}}const Pn=e=>e.id==null?e.flags&2?-1:1/0:e.id;function Di(e){const t=Ke;try{for(et=0;et<Ce.length;et++){const n=Ce[et];n&&!(n.flags&8)&&(Tt.NODE_ENV!=="production"&&t(n),n.flags&4&&(n.flags&=-2),ln(n,n.i,n.i?15:14),n.flags&4||(n.flags&=-2))}}finally{for(;et<Ce.length;et++){const n=Ce[et];n&&(n.flags&=-2)}et=-1,Ce.length=0,Ci(),To=null,(Ce.length||un.length)&&Di()}}let ye=null,Ao=null;function Co(e){const t=ye;return ye=e,Ao=e&&e.type.__scopeId||null,t}function If(e){Ao=e}function Rf(){Ao=null}const kf=e=>Do;function Do(e,t=ye,n){if(!t||e._n)return e;const o=(...s)=>{o._d&&ul(-1);const r=Co(t);let i;try{i=e(...s)}finally{Co(r),o._d&&ul(1)}return i};return o._n=!0,o._c=!0,o._d=!0,o}function tt(e,t){if(ye===null)return e;const n=Uo(ye),o=e.dirs||(e.dirs=[]);for(let s=0;s<t.length;s++){let[r,i,l,u=le]=t[s];r&&(K(r)&&(r={mounted:r,updated:r}),r.deep&&yt(i),o.push({dir:r,instance:n,value:i,oldValue:void 0,arg:l,modifiers:u}))}return e}function Wt(e,t,n,o){const s=e.dirs,r=t&&t.dirs;for(let i=0;i<s.length;i++){const l=s[i];r&&(l.oldValue=r[i].value);let u=l.dir[o];u&&(_t(),Qe(u,n,8,[e.el,l,e,t]),mt())}}const Nf=Symbol("_vte"),Vf=e=>e.__isTeleport;function Fs(e,t){e.shapeFlag&6&&e.component?(e.transition=t,Fs(e.component.subTree,t)):e.shapeFlag&128?(e.ssContent.transition=t.clone(e.ssContent),e.ssFallback.transition=t.clone(e.ssFallback)):e.transition=t}/*! #__NO_SIDE_EFFECTS__ */function cn(e,t){return K(e)?be({name:e.name},t,{setup:e}):e}function Pi(e){e.ids=[e.ids[0]+e.ids[2]+++"-",0,0]}function Po(e,t,n,o,s=!1){if(Y(e)){e.forEach((_,v)=>Po(_,t&&(Y(t)?t[v]:t),n,o,s));return}if(fn(o)&&!s){o.shapeFlag&512&&o.type.__asyncResolved&&o.component.subTree.component&&Po(e,t,n,o.component.subTree);return}const r=o.shapeFlag&4?Uo(o.component):o.el,i=s?null:r,{i:l,r:u}=e,a=t&&t.r,c=l.refs===le?l.refs={}:l.refs,f=l.setupState,h=ne(f),d=f===le?()=>!1:_=>oe(h,_);if(a!=null&&a!==u&&(me(a)?(c[a]=null,d(a)&&(f[a]=null)):ve(a)&&(a.value=null)),K(u))ln(u,l,12,[i,c]);else{const _=me(u),v=ve(u);if(_||v){const y=()=>{if(e.f){const g=_?d(u)?f[u]:c[u]:u.value;s?Y(g)&&ws(g,r):Y(g)?g.includes(r)||g.push(r):_?(c[u]=[r],d(u)&&(f[u]=c[u])):(u.value=[r],e.k&&(c[e.k]=u.value))}else _?(c[u]=i,d(u)&&(f[u]=i)):v&&(u.value=i,e.k&&(c[e.k]=i))};i?(y.id=-1,$e(y,n)):y()}}}ho().requestIdleCallback,ho().cancelIdleCallback;const fn=e=>!!e.type.__asyncLoader,Ii=e=>e.type.__isKeepAlive;function Lf(e,t){Ri(e,"a",t)}function $f(e,t){Ri(e,"da",t)}function Ri(e,t,n=ge){const o=e.__wdc||(e.__wdc=()=>{let s=n;for(;s;){if(s.isDeactivated)return;s=s.parent}return e()});if(Io(t,o,n),n){let s=n.parent;for(;s&&s.parent;)Ii(s.parent.vnode)&&Mf(o,t,n,s),s=s.parent}}function Mf(e,t,n,o){const s=Io(t,e,o,!0);ki(()=>{ws(o[t],s)},n)}function Io(e,t,n=ge,o=!1){if(n){const s=n[e]||(n[e]=[]),r=t.__weh||(t.__weh=(...i)=>{_t();const l=Un(n),u=Qe(t,n,e,i);return l(),mt(),u});return o?s.unshift(r):s.push(r),r}}const Et=e=>(t,n=ge)=>{(!Fn||e==="sp")&&Io(e,(...o)=>t(...o),n)},Uf=Et("bm"),In=Et("m"),Ff=Et("bu"),Bf=Et("u"),Hf=Et("bum"),ki=Et("um"),zf=Et("sp"),jf=Et("rtg"),Kf=Et("rtc");function Wf(e,t=ge){Io("ec",e,t)}const Gf="components";function Bs(e,t){return qf(Gf,e,!0,t)||e}const Yf=Symbol.for("v-ndc");function qf(e,t,n=!0,o=!1){const s=ye||ge;if(s){const r=s.type;{const l=gl(r,!1);if(l&&(l===t||l===He(t)||l===po(He(t))))return r}const i=Ni(s[e]||r[e],t)||Ni(s.appContext[e],t);return!i&&o?r:i}}function Ni(e,t){return e&&(e[t]||e[He(t)]||e[po(He(t))])}function Ro(e,t,n={},o,s){if(ye.ce||ye.parent&&fn(ye.parent)&&ye.parent.ce)return t!=="default"&&(n.name=t),Re(),dn(Ie,null,[Se("slot",n,o)],64);let r=e[t];r&&r._c&&(r._d=!1),Re();const i=r&&Vi(r(n)),l=n.key||i&&i.key,u=dn(Ie,{key:(l&&!Ht(l)?l:`_${t}`)+""},i||[],i&&e._===1?64:-2);return u.scopeId&&(u.slotScopeIds=[u.scopeId+"-s"]),r&&r._c&&(r._d=!0),u}function Vi(e){return e.some(t=>$n(t)?!(t.type===At||t.type===Ie&&!Vi(t.children)):!0)?e:null}const Hs=e=>e?hl(e)?Uo(e):Hs(e.parent):null,Rn=be(Object.create(null),{$:e=>e,$el:e=>e.vnode.el,$data:e=>e.data,$props:e=>e.props,$attrs:e=>e.attrs,$slots:e=>e.slots,$refs:e=>e.refs,$parent:e=>Hs(e.parent),$root:e=>Hs(e.root),$host:e=>e.ce,$emit:e=>e.emit,$options:e=>Ui(e),$forceUpdate:e=>e.f||(e.f=()=>{Us(e.update)}),$nextTick:e=>e.n||(e.n=Oo.bind(e.proxy)),$watch:e=>gd.bind(e)}),zs=(e,t)=>e!==le&&!e.__isScriptSetup&&oe(e,t),Xf={get({_:e},t){if(t==="__v_skip")return!0;const{ctx:n,setupState:o,data:s,props:r,accessCache:i,type:l,appContext:u}=e;let a;if(t[0]!=="$"){const d=i[t];if(d!==void 0)switch(d){case 1:return o[t];case 2:return s[t];case 4:return n[t];case 3:return r[t]}else{if(zs(o,t))return i[t]=1,o[t];if(s!==le&&oe(s,t))return i[t]=2,s[t];if((a=e.propsOptions[0])&&oe(a,t))return i[t]=3,r[t];if(n!==le&&oe(n,t))return i[t]=4,n[t];js&&(i[t]=0)}}const c=Rn[t];let f,h;if(c)return t==="$attrs"&&we(e.attrs,"get",""),c(e);if((f=l.__cssModules)&&(f=f[t]))return f;if(n!==le&&oe(n,t))return i[t]=4,n[t];if(h=u.config.globalProperties,oe(h,t))return h[t]},set({_:e},t,n){const{data:o,setupState:s,ctx:r}=e;return zs(s,t)?(s[t]=n,!0):o!==le&&oe(o,t)?(o[t]=n,!0):oe(e.props,t)||t[0]==="$"&&t.slice(1)in e?!1:(r[t]=n,!0)},has({_:{data:e,setupState:t,accessCache:n,ctx:o,appContext:s,propsOptions:r}},i){let l;return!!n[i]||e!==le&&oe(e,i)||zs(t,i)||(l=r[0])&&oe(l,i)||oe(o,i)||oe(Rn,i)||oe(s.config.globalProperties,i)},defineProperty(e,t,n){return n.get!=null?e._.accessCache[t]=0:oe(n,"value")&&this.set(e,t,n.value,null),Reflect.defineProperty(e,t,n)}};function Li(e){return Y(e)?e.reduce((t,n)=>(t[n]=null,t),{}):e}let js=!0;function Zf(e){const t=Ui(e),n=e.proxy,o=e.ctx;js=!1,t.beforeCreate&&$i(t.beforeCreate,e,"bc");const{data:s,computed:r,methods:i,watch:l,provide:u,inject:a,created:c,beforeMount:f,mounted:h,beforeUpdate:d,updated:_,activated:v,deactivated:y,beforeDestroy:g,beforeUnmount:x,destroyed:D,unmounted:b,render:P,renderTracked:U,renderTriggered:H,errorCaptured:W,serverPrefetch:M,expose:I,inheritAttrs:R,components:F,directives:z,filters:Q}=t;if(a&&Jf(a,o,null),i)for(const L in i){const X=i[L];K(X)&&(o[L]=X.bind(n))}if(s){const L=s.call(n,n);de(L)&&(e.data=rn(L))}if(js=!0,r)for(const L in r){const X=r[L],ue=K(X)?X.bind(n,n):K(X.get)?X.get.bind(n,n):Ke,Ue=!K(X)&&K(X.set)?X.set.bind(n):Ke,xe=_e({get:ue,set:Ue});Object.defineProperty(o,L,{enumerable:!0,configurable:!0,get:()=>xe.value,set:he=>xe.value=he})}if(l)for(const L in l)Mi(l[L],o,n,L);if(u){const L=K(u)?u.call(n):u;Reflect.ownKeys(L).forEach(X=>{sd(X,L[X])})}c&&$i(c,e,"c");function Z(L,X){Y(X)?X.forEach(ue=>L(ue.bind(n))):X&&L(X.bind(n))}if(Z(Uf,f),Z(In,h),Z(Ff,d),Z(Bf,_),Z(Lf,v),Z($f,y),Z(Wf,W),Z(Kf,U),Z(jf,H),Z(Hf,x),Z(ki,b),Z(zf,M),Y(I))if(I.length){const L=e.exposed||(e.exposed={});I.forEach(X=>{Object.defineProperty(L,X,{get:()=>n[X],set:ue=>n[X]=ue})})}else e.exposed||(e.exposed={});P&&e.render===Ke&&(e.render=P),R!=null&&(e.inheritAttrs=R),F&&(e.components=F),z&&(e.directives=z),M&&Pi(e)}function Jf(e,t,n=Ke){Y(e)&&(e=Ks(e));for(const o in e){const s=e[o];let r;de(s)?"default"in s?r=Nn(s.from||o,s.default,!0):r=Nn(s.from||o):r=Nn(s),ve(r)?Object.defineProperty(t,o,{enumerable:!0,configurable:!0,get:()=>r.value,set:i=>r.value=i}):t[o]=r}}function $i(e,t,n){Qe(Y(e)?e.map(o=>o.bind(t.proxy)):e.bind(t.proxy),t,n)}function Mi(e,t,n,o){let s=o.includes(".")?ol(n,o):()=>n[o];if(me(e)){const r=t[e];K(r)&&Ye(s,r)}else if(K(e))Ye(s,e.bind(n));else if(de(e))if(Y(e))e.forEach(r=>Mi(r,t,n,o));else{const r=K(e.handler)?e.handler.bind(n):t[e.handler];K(r)&&Ye(s,r,e)}}function Ui(e){const t=e.type,{mixins:n,extends:o}=t,{mixins:s,optionsCache:r,config:{optionMergeStrategies:i}}=e.appContext,l=r.get(t);let u;return l?u=l:!s.length&&!n&&!o?u=t:(u={},s.length&&s.forEach(a=>ko(u,a,i,!0)),ko(u,t,i)),de(t)&&r.set(t,u),u}function ko(e,t,n,o=!1){const{mixins:s,extends:r}=t;r&&ko(e,r,n,!0),s&&s.forEach(i=>ko(e,i,n,!0));for(const i in t)if(!(o&&i==="expose")){const l=Qf[i]||n&&n[i];e[i]=l?l(e[i],t[i]):t[i]}return e}const Qf={data:Fi,props:Bi,emits:Bi,methods:kn,computed:kn,beforeCreate:De,created:De,beforeMount:De,mounted:De,beforeUpdate:De,updated:De,beforeDestroy:De,beforeUnmount:De,destroyed:De,unmounted:De,activated:De,deactivated:De,errorCaptured:De,serverPrefetch:De,components:kn,directives:kn,watch:td,provide:Fi,inject:ed};function Fi(e,t){return t?e?function(){return be(K(e)?e.call(this,this):e,K(t)?t.call(this,this):t)}:t:e}function ed(e,t){return kn(Ks(e),Ks(t))}function Ks(e){if(Y(e)){const t={};for(let n=0;n<e.length;n++)t[e[n]]=e[n];return t}return e}function De(e,t){return e?[...new Set([].concat(e,t))]:t}function kn(e,t){return e?be(Object.create(null),e,t):t}function Bi(e,t){return e?Y(e)&&Y(t)?[...new Set([...e,...t])]:be(Object.create(null),Li(e),Li(t??{})):t}function td(e,t){if(!e)return t;if(!t)return e;const n=be(Object.create(null),e);for(const o in t)n[o]=De(e[o],t[o]);return n}function Hi(){return{app:null,config:{isNativeTag:Tc,performance:!1,globalProperties:{},optionMergeStrategies:{},errorHandler:void 0,warnHandler:void 0,compilerOptions:{}},mixins:[],components:{},directives:{},provides:Object.create(null),optionsCache:new WeakMap,propsCache:new WeakMap,emitsCache:new WeakMap}}let nd=0;function od(e,t){return function(o,s=null){K(o)||(o=be({},o)),s!=null&&!de(s)&&(s=null);const r=Hi(),i=new WeakSet,l=[];let u=!1;const a=r.app={_uid:nd++,_component:o,_props:s,_container:null,_context:r,_instance:null,version:Ud,get config(){return r.config},set config(c){},use(c,...f){return i.has(c)||(c&&K(c.install)?(i.add(c),c.install(a,...f)):K(c)&&(i.add(c),c(a,...f))),a},mixin(c){return r.mixins.includes(c)||r.mixins.push(c),a},component(c,f){return f?(r.components[c]=f,a):r.components[c]},directive(c,f){return f?(r.directives[c]=f,a):r.directives[c]},mount(c,f,h){if(!u){const d=a._ceVNode||Se(o,s);return d.appContext=r,h===!0?h="svg":h===!1&&(h=void 0),e(d,c,h),u=!0,a._container=c,c.__vue_app__=a,Uo(d.component)}},onUnmount(c){l.push(c)},unmount(){u&&(Qe(l,a._instance,16),e(null,a._container),delete a._container.__vue_app__)},provide(c,f){return r.provides[c]=f,a},runWithContext(c){const f=Gt;Gt=a;try{return c()}finally{Gt=f}}};return a}}let Gt=null;function sd(e,t){if(ge){let n=ge.provides;const o=ge.parent&&ge.parent.provides;o===n&&(n=ge.provides=Object.create(o)),n[e]=t}}function Nn(e,t,n=!1){const o=ge||ye;if(o||Gt){const s=Gt?Gt._context.provides:o?o.parent==null?o.vnode.appContext&&o.vnode.appContext.provides:o.parent.provides:void 0;if(s&&e in s)return s[e];if(arguments.length>1)return n&&K(t)?t.call(o&&o.proxy):t}}function zi(){return!!(ge||ye||Gt)}const ji={},Ki=()=>Object.create(ji),Wi=e=>Object.getPrototypeOf(e)===ji;function rd(e,t,n,o=!1){const s={},r=Ki();e.propsDefaults=Object.create(null),Gi(e,t,s,r);for(const i in e.propsOptions[0])i in s||(s[i]=void 0);n?e.props=o?s:af(s):e.type.props?e.props=s:e.props=r,e.attrs=r}function id(e,t,n,o){const{props:s,attrs:r,vnode:{patchFlag:i}}=e,l=ne(s),[u]=e.propsOptions;let a=!1;if((o||i>0)&&!(i&16)){if(i&8){const c=e.vnode.dynamicProps;for(let f=0;f<c.length;f++){let h=c[f];if(No(e.emitsOptions,h))continue;const d=t[h];if(u)if(oe(r,h))d!==r[h]&&(r[h]=d,a=!0);else{const _=He(h);s[_]=Ws(u,l,_,d,e,!1)}else d!==r[h]&&(r[h]=d,a=!0)}}}else{Gi(e,t,s,r)&&(a=!0);let c;for(const f in l)(!t||!oe(t,f)&&((c=St(f))===f||!oe(t,c)))&&(u?n&&(n[f]!==void 0||n[c]!==void 0)&&(s[f]=Ws(u,l,f,void 0,e,!0)):delete s[f]);if(r!==l)for(const f in r)(!t||!oe(t,f))&&(delete r[f],a=!0)}a&&gt(e.attrs,"set","")}function Gi(e,t,n,o){const[s,r]=e.propsOptions;let i=!1,l;if(t)for(let u in t){if(bn(u))continue;const a=t[u];let c;s&&oe(s,c=He(u))?!r||!r.includes(c)?n[c]=a:(l||(l={}))[c]=a:No(e.emitsOptions,u)||(!(u in o)||a!==o[u])&&(o[u]=a,i=!0)}if(r){const u=ne(n),a=l||le;for(let c=0;c<r.length;c++){const f=r[c];n[f]=Ws(s,u,f,a[f],e,!oe(a,f))}}return i}function Ws(e,t,n,o,s,r){const i=e[n];if(i!=null){const l=oe(i,"default");if(l&&o===void 0){const u=i.default;if(i.type!==Function&&!i.skipFactory&&K(u)){const{propsDefaults:a}=s;if(n in a)o=a[n];else{const c=Un(s);o=a[n]=u.call(null,t),c()}}else o=u;s.ce&&s.ce._setProp(n,o)}i[0]&&(r&&!l?o=!1:i[1]&&(o===""||o===St(n))&&(o=!0))}return o}const ld=new WeakMap;function Yi(e,t,n=!1){const o=n?ld:t.propsCache,s=o.get(e);if(s)return s;const r=e.props,i={},l=[];let u=!1;if(!K(e)){const c=f=>{u=!0;const[h,d]=Yi(f,t,!0);be(i,h),d&&l.push(...d)};!n&&t.mixins.length&&t.mixins.forEach(c),e.extends&&c(e.extends),e.mixins&&e.mixins.forEach(c)}if(!r&&!u)return de(e)&&o.set(e,on),on;if(Y(r))for(let c=0;c<r.length;c++){const f=He(r[c]);qi(f)&&(i[f]=le)}else if(r)for(const c in r){const f=He(c);if(qi(f)){const h=r[c],d=i[f]=Y(h)||K(h)?{type:h}:be({},h),_=d.type;let v=!1,y=!0;if(Y(_))for(let g=0;g<_.length;++g){const x=_[g],D=K(x)&&x.name;if(D==="Boolean"){v=!0;break}else D==="String"&&(y=!1)}else v=K(_)&&_.name==="Boolean";d[0]=v,d[1]=y,(v||oe(d,"default"))&&l.push(f)}}const a=[i,l];return de(e)&&o.set(e,a),a}function qi(e){return e[0]!=="$"&&!bn(e)}const Xi=e=>e[0]==="_"||e==="$stable",Gs=e=>Y(e)?e.map(nt):[nt(e)],ud=(e,t,n)=>{if(t._n)return t;const o=Do((...s)=>(Tt.NODE_ENV!=="production"&&ge&&(!n||(n.root,ge.root)),Gs(t(...s))),n);return o._c=!1,o},Zi=(e,t,n)=>{const o=e._ctx;for(const s in e){if(Xi(s))continue;const r=e[s];if(K(r))t[s]=ud(s,r,o);else if(r!=null){const i=Gs(r);t[s]=()=>i}}},Ji=(e,t)=>{const n=Gs(t);e.slots.default=()=>n},Qi=(e,t,n)=>{for(const o in t)(n||o!=="_")&&(e[o]=t[o])},ad=(e,t,n)=>{const o=e.slots=Ki();if(e.vnode.shapeFlag&32){const s=t._;s?(Qi(o,t,n),n&&ei(o,"_",s,!0)):Zi(t,o)}else t&&Ji(e,t)},cd=(e,t,n)=>{const{vnode:o,slots:s}=e;let r=!0,i=le;if(o.shapeFlag&32){const l=t._;l?n&&l===1?r=!1:Qi(s,t,n):(r=!t.$stable,Zi(t,s)),i=t}else t&&(Ji(e,t),i={default:1});if(r)for(const l in s)!Xi(l)&&i[l]==null&&delete s[l]},$e=xd;function fd(e){return dd(e)}function dd(e,t){const n=ho();n.__VUE__=!0;const{insert:o,remove:s,patchProp:r,createElement:i,createText:l,createComment:u,setText:a,setElementText:c,parentNode:f,nextSibling:h,setScopeId:d=Ke,insertStaticContent:_}=e,v=(p,m,E,A=null,S=null,T=null,V=void 0,N=null,k=!!m.dynamicChildren)=>{if(p===m)return;p&&!Mn(p,m)&&(A=ys(p),he(p,S,T,!0),p=null),m.patchFlag===-2&&(k=!1,m.dynamicChildren=null);const{type:C,ref:j,shapeFlag:$}=m;switch(C){case Vo:y(p,m,E,A);break;case At:g(p,m,E,A);break;case Zs:p==null&&x(m,E,A,V);break;case Ie:F(p,m,E,A,S,T,V,N,k);break;default:$&1?P(p,m,E,A,S,T,V,N,k):$&6?z(p,m,E,A,S,T,V,N,k):($&64||$&128)&&C.process(p,m,E,A,S,T,V,N,k,lo)}j!=null&&S&&Po(j,p&&p.ref,T,m||p,!m)},y=(p,m,E,A)=>{if(p==null)o(m.el=l(m.children),E,A);else{const S=m.el=p.el;m.children!==p.children&&a(S,m.children)}},g=(p,m,E,A)=>{p==null?o(m.el=u(m.children||""),E,A):m.el=p.el},x=(p,m,E,A)=>{[p.el,p.anchor]=_(p.children,m,E,A,p.el,p.anchor)},D=({el:p,anchor:m},E,A)=>{let S;for(;p&&p!==m;)S=h(p),o(p,E,A),p=S;o(m,E,A)},b=({el:p,anchor:m})=>{let E;for(;p&&p!==m;)E=h(p),s(p),p=E;s(m)},P=(p,m,E,A,S,T,V,N,k)=>{m.type==="svg"?V="svg":m.type==="math"&&(V="mathml"),p==null?U(m,E,A,S,T,V,N,k):M(p,m,S,T,V,N,k)},U=(p,m,E,A,S,T,V,N)=>{let k,C;const{props:j,shapeFlag:$,transition:B,dirs:G}=p;if(k=p.el=i(p.type,T,j&&j.is,j),$&8?c(k,p.children):$&16&&W(p.children,k,null,A,S,Ys(p,T),V,N),G&&Wt(p,null,A,"created"),H(k,p,p.scopeId,V,A),j){for(const fe in j)fe!=="value"&&!bn(fe)&&r(k,fe,null,j[fe],T,A);"value"in j&&r(k,"value",null,j.value,T),(C=j.onVnodeBeforeMount)&&ot(C,A,p)}G&&Wt(p,null,A,"beforeMount");const te=pd(S,B);te&&B.beforeEnter(k),o(k,m,E),((C=j&&j.onVnodeMounted)||te||G)&&$e(()=>{C&&ot(C,A,p),te&&B.enter(k),G&&Wt(p,null,A,"mounted")},S)},H=(p,m,E,A,S)=>{if(E&&d(p,E),A)for(let T=0;T<A.length;T++)d(p,A[T]);if(S){let T=S.subTree;if(m===T||ll(T.type)&&(T.ssContent===m||T.ssFallback===m)){const V=S.vnode;H(p,V,V.scopeId,V.slotScopeIds,S.parent)}}},W=(p,m,E,A,S,T,V,N,k=0)=>{for(let C=k;C<p.length;C++){const j=p[C]=N?Dt(p[C]):nt(p[C]);v(null,j,m,E,A,S,T,V,N)}},M=(p,m,E,A,S,T,V)=>{const N=m.el=p.el;let{patchFlag:k,dynamicChildren:C,dirs:j}=m;k|=p.patchFlag&16;const $=p.props||le,B=m.props||le;let G;if(E&&Yt(E,!1),(G=B.onVnodeBeforeUpdate)&&ot(G,E,m,p),j&&Wt(m,p,E,"beforeUpdate"),E&&Yt(E,!0),($.innerHTML&&B.innerHTML==null||$.textContent&&B.textContent==null)&&c(N,""),C?I(p.dynamicChildren,C,N,E,A,Ys(m,S),T):V||X(p,m,N,null,E,A,Ys(m,S),T,!1),k>0){if(k&16)R(N,$,B,E,S);else if(k&2&&$.class!==B.class&&r(N,"class",null,B.class,S),k&4&&r(N,"style",$.style,B.style,S),k&8){const te=m.dynamicProps;for(let fe=0;fe<te.length;fe++){const re=te[fe],Fe=$[re],ke=B[re];(ke!==Fe||re==="value")&&r(N,re,Fe,ke,S,E)}}k&1&&p.children!==m.children&&c(N,m.children)}else!V&&C==null&&R(N,$,B,E,S);((G=B.onVnodeUpdated)||j)&&$e(()=>{G&&ot(G,E,m,p),j&&Wt(m,p,E,"updated")},A)},I=(p,m,E,A,S,T,V)=>{for(let N=0;N<m.length;N++){const k=p[N],C=m[N],j=k.el&&(k.type===Ie||!Mn(k,C)||k.shapeFlag&70)?f(k.el):E;v(k,C,j,null,A,S,T,V,!0)}},R=(p,m,E,A,S)=>{if(m!==E){if(m!==le)for(const T in m)!bn(T)&&!(T in E)&&r(p,T,m[T],null,S,A);for(const T in E){if(bn(T))continue;const V=E[T],N=m[T];V!==N&&T!=="value"&&r(p,T,N,V,S,A)}"value"in E&&r(p,"value",m.value,E.value,S)}},F=(p,m,E,A,S,T,V,N,k)=>{const C=m.el=p?p.el:l(""),j=m.anchor=p?p.anchor:l("");let{patchFlag:$,dynamicChildren:B,slotScopeIds:G}=m;G&&(N=N?N.concat(G):G),p==null?(o(C,E,A),o(j,E,A),W(m.children||[],E,j,S,T,V,N,k)):$>0&&$&64&&B&&p.dynamicChildren?(I(p.dynamicChildren,B,E,S,T,V,N),(m.key!=null||S&&m===S.subTree)&&el(p,m,!0)):X(p,m,E,j,S,T,V,N,k)},z=(p,m,E,A,S,T,V,N,k)=>{m.slotScopeIds=N,p==null?m.shapeFlag&512?S.ctx.activate(m,E,A,V,k):Q(m,E,A,S,T,V,k):Ee(p,m,k)},Q=(p,m,E,A,S,T,V)=>{const N=p.component=Pd(p,A,S);if(Ii(p)&&(N.ctx.renderer=lo),Id(N,!1,V),N.asyncDep){if(S&&S.registerDep(N,Z,V),!p.el){const k=N.subTree=Se(At);g(null,k,m,E)}}else Z(N,p,m,E,S,T,V)},Ee=(p,m,E)=>{const A=m.component=p.component;if(wd(p,m,E))if(A.asyncDep&&!A.asyncResolved){L(A,m,E);return}else A.next=m,A.update();else m.el=p.el,A.vnode=m},Z=(p,m,E,A,S,T,V)=>{const N=()=>{if(p.isMounted){let{next:$,bu:B,u:G,parent:te,vnode:fe}=p;{const dt=tl(p);if(dt){$&&($.el=fe.el,L(p,$,V)),dt.asyncDep.then(()=>{p.isUnmounted||N()});return}}let re=$,Fe;Yt(p,!1),$?($.el=fe.el,L(p,$,V)):$=fe,B&&Ts(B),(Fe=$.props&&$.props.onVnodeBeforeUpdate)&&ot(Fe,te,$,fe),Yt(p,!0);const ke=rl(p),ft=p.subTree;p.subTree=ke,v(ft,ke,f(ft.el),ys(ft),p,S,T),$.el=ke.el,re===null&&Sd(p,ke.el),G&&$e(G,S),(Fe=$.props&&$.props.onVnodeUpdated)&&$e(()=>ot(Fe,te,$,fe),S)}else{let $;const{el:B,props:G}=m,{bm:te,m:fe,parent:re,root:Fe,type:ke}=p,ft=fn(m);Yt(p,!1),te&&Ts(te),!ft&&($=G&&G.onVnodeBeforeMount)&&ot($,re,m),Yt(p,!0);{Fe.ce&&Fe.ce._injectChildStyle(ke);const dt=p.subTree=rl(p);v(null,dt,E,A,p,S,T),m.el=dt.el}if(fe&&$e(fe,S),!ft&&($=G&&G.onVnodeMounted)){const dt=m;$e(()=>ot($,re,dt),S)}(m.shapeFlag&256||re&&fn(re.vnode)&&re.vnode.shapeFlag&256)&&p.a&&$e(p.a,S),p.isMounted=!0,m=E=A=null}};p.scope.on();const k=p.effect=new si(N);p.scope.off();const C=p.update=k.run.bind(k),j=p.job=k.runIfDirty.bind(k);j.i=p,j.id=p.uid,k.scheduler=()=>Us(j),Yt(p,!0),C()},L=(p,m,E)=>{m.component=p;const A=p.vnode.props;p.vnode=m,p.next=null,id(p,m.props,A,E),cd(p,m.children,E),_t(),Ai(p),mt()},X=(p,m,E,A,S,T,V,N,k=!1)=>{const C=p&&p.children,j=p?p.shapeFlag:0,$=m.children,{patchFlag:B,shapeFlag:G}=m;if(B>0){if(B&128){Ue(C,$,E,A,S,T,V,N,k);return}else if(B&256){ue(C,$,E,A,S,T,V,N,k);return}}G&8?(j&16&&io(C,S,T),$!==C&&c(E,$)):j&16?G&16?Ue(C,$,E,A,S,T,V,N,k):io(C,S,T,!0):(j&8&&c(E,""),G&16&&W($,E,A,S,T,V,N,k))},ue=(p,m,E,A,S,T,V,N,k)=>{p=p||on,m=m||on;const C=p.length,j=m.length,$=Math.min(C,j);let B;for(B=0;B<$;B++){const G=m[B]=k?Dt(m[B]):nt(m[B]);v(p[B],G,E,null,S,T,V,N,k)}C>j?io(p,S,T,!0,!1,$):W(m,E,A,S,T,V,N,k,$)},Ue=(p,m,E,A,S,T,V,N,k)=>{let C=0;const j=m.length;let $=p.length-1,B=j-1;for(;C<=$&&C<=B;){const G=p[C],te=m[C]=k?Dt(m[C]):nt(m[C]);if(Mn(G,te))v(G,te,E,null,S,T,V,N,k);else break;C++}for(;C<=$&&C<=B;){const G=p[$],te=m[B]=k?Dt(m[B]):nt(m[B]);if(Mn(G,te))v(G,te,E,null,S,T,V,N,k);else break;$--,B--}if(C>$){if(C<=B){const G=B+1,te=G<j?m[G].el:A;for(;C<=B;)v(null,m[C]=k?Dt(m[C]):nt(m[C]),E,te,S,T,V,N,k),C++}}else if(C>B)for(;C<=$;)he(p[C],S,T,!0),C++;else{const G=C,te=C,fe=new Map;for(C=te;C<=B;C++){const Be=m[C]=k?Dt(m[C]):nt(m[C]);Be.key!=null&&fe.set(Be.key,C)}let re,Fe=0;const ke=B-te+1;let ft=!1,dt=0;const uo=new Array(ke);for(C=0;C<ke;C++)uo[C]=0;for(C=G;C<=$;C++){const Be=p[C];if(Fe>=ke){he(Be,S,T,!0);continue}let pt;if(Be.key!=null)pt=fe.get(Be.key);else for(re=te;re<=B;re++)if(uo[re-te]===0&&Mn(Be,m[re])){pt=re;break}pt===void 0?he(Be,S,T,!0):(uo[pt-te]=C+1,pt>=dt?dt=pt:ft=!0,v(Be,m[pt],E,null,S,T,V,N,k),Fe++)}const Sc=ft?hd(uo):on;for(re=Sc.length-1,C=ke-1;C>=0;C--){const Be=te+C,pt=m[Be],xc=Be+1<j?m[Be+1].el:A;uo[C]===0?v(null,pt,E,xc,S,T,V,N,k):ft&&(re<0||C!==Sc[re]?xe(pt,E,xc,2):re--)}}},xe=(p,m,E,A,S=null)=>{const{el:T,type:V,transition:N,children:k,shapeFlag:C}=p;if(C&6){xe(p.component.subTree,m,E,A);return}if(C&128){p.suspense.move(m,E,A);return}if(C&64){V.move(p,m,E,lo);return}if(V===Ie){o(T,m,E);for(let $=0;$<k.length;$++)xe(k[$],m,E,A);o(p.anchor,m,E);return}if(V===Zs){D(p,m,E);return}if(A!==2&&C&1&&N)if(A===0)N.beforeEnter(T),o(T,m,E),$e(()=>N.enter(T),S);else{const{leave:$,delayLeave:B,afterLeave:G}=N,te=()=>o(T,m,E),fe=()=>{$(T,()=>{te(),G&&G()})};B?B(T,te,fe):fe()}else o(T,m,E)},he=(p,m,E,A=!1,S=!1)=>{const{type:T,props:V,ref:N,children:k,dynamicChildren:C,shapeFlag:j,patchFlag:$,dirs:B,cacheIndex:G}=p;if($===-2&&(S=!1),N!=null&&Po(N,null,E,p,!0),G!=null&&(m.renderCache[G]=void 0),j&256){m.ctx.deactivate(p);return}const te=j&1&&B,fe=!fn(p);let re;if(fe&&(re=V&&V.onVnodeBeforeUnmount)&&ot(re,m,p),j&6)ro(p.component,E,A);else{if(j&128){p.suspense.unmount(E,A);return}te&&Wt(p,null,m,"beforeUnmount"),j&64?p.type.remove(p,m,E,lo,A):C&&!C.hasOnce&&(T!==Ie||$>0&&$&64)?io(C,m,E,!1,!0):(T===Ie&&$&384||!S&&j&16)&&io(k,m,E),A&&Bt(p)}(fe&&(re=V&&V.onVnodeUnmounted)||te)&&$e(()=>{re&&ot(re,m,p),te&&Wt(p,null,m,"unmounted")},E)},Bt=p=>{const{type:m,el:E,anchor:A,transition:S}=p;if(m===Ie){vs(E,A);return}if(m===Zs){b(p);return}const T=()=>{s(E),S&&!S.persisted&&S.afterLeave&&S.afterLeave()};if(p.shapeFlag&1&&S&&!S.persisted){const{leave:V,delayLeave:N}=S,k=()=>V(E,T);N?N(p.el,T,k):k()}else T()},vs=(p,m)=>{let E;for(;p!==m;)E=h(p),s(p),p=E;s(m)},ro=(p,m,E)=>{const{bum:A,scope:S,job:T,subTree:V,um:N,m:k,a:C}=p;nl(k),nl(C),A&&Ts(A),S.stop(),T&&(T.flags|=8,he(V,p,m,E)),N&&$e(N,m),$e(()=>{p.isUnmounted=!0},m),m&&m.pendingBranch&&!m.isUnmounted&&p.asyncDep&&!p.asyncResolved&&p.suspenseId===m.pendingId&&(m.deps--,m.deps===0&&m.resolve())},io=(p,m,E,A=!1,S=!1,T=0)=>{for(let V=T;V<p.length;V++)he(p[V],m,E,A,S)},ys=p=>{if(p.shapeFlag&6)return ys(p.component.subTree);if(p.shapeFlag&128)return p.suspense.next();const m=h(p.anchor||p.el),E=m&&m[Nf];return E?h(E):m};let Jr=!1;const wc=(p,m,E)=>{p==null?m._vnode&&he(m._vnode,null,null,!0):v(m._vnode||null,p,m,null,null,null,E),m._vnode=p,Jr||(Jr=!0,Ai(),Ci(),Jr=!1)},lo={p:v,um:he,m:xe,r:Bt,mt:Q,mc:W,pc:X,pbc:I,n:ys,o:e};return{render:wc,hydrate:void 0,createApp:od(wc)}}function Ys({type:e,props:t},n){return n==="svg"&&e==="foreignObject"||n==="mathml"&&e==="annotation-xml"&&t&&t.encoding&&t.encoding.includes("html")?void 0:n}function Yt({effect:e,job:t},n){n?(e.flags|=32,t.flags|=4):(e.flags&=-33,t.flags&=-5)}function pd(e,t){return(!e||e&&!e.pendingBranch)&&t&&!t.persisted}function el(e,t,n=!1){const o=e.children,s=t.children;if(Y(o)&&Y(s))for(let r=0;r<o.length;r++){const i=o[r];let l=s[r];l.shapeFlag&1&&!l.dynamicChildren&&((l.patchFlag<=0||l.patchFlag===32)&&(l=s[r]=Dt(s[r]),l.el=i.el),!n&&l.patchFlag!==-2&&el(i,l)),l.type===Vo&&(l.el=i.el)}}function hd(e){const t=e.slice(),n=[0];let o,s,r,i,l;const u=e.length;for(o=0;o<u;o++){const a=e[o];if(a!==0){if(s=n[n.length-1],e[s]<a){t[o]=s,n.push(o);continue}for(r=0,i=n.length-1;r<i;)l=r+i>>1,e[n[l]]<a?r=l+1:i=l;a<e[n[r]]&&(r>0&&(t[o]=n[r-1]),n[r]=o)}}for(r=n.length,i=n[r-1];r-- >0;)n[r]=i,i=t[i];return n}function tl(e){const t=e.subTree.component;if(t)return t.asyncDep&&!t.asyncResolved?t:tl(t)}function nl(e){if(e)for(let t=0;t<e.length;t++)e[t].flags|=8}const _d=Symbol.for("v-scx"),md=()=>Nn(_d);function qs(e,t){return Xs(e,null,t)}function Ye(e,t,n){return Xs(e,t,n)}function Xs(e,t,n=le){const{immediate:o,deep:s,flush:r,once:i}=n,l=be({},n),u=t&&o||!t&&r!=="post";let a;if(Fn){if(r==="sync"){const d=md();a=d.__watcherHandles||(d.__watcherHandles=[])}else if(!u){const d=()=>{};return d.stop=Ke,d.resume=Ke,d.pause=Ke,d}}const c=ge;l.call=(d,_,v)=>Qe(d,c,_,v);let f=!1;r==="post"?l.scheduler=d=>{$e(d,c&&c.suspense)}:r!=="sync"&&(f=!0,l.scheduler=(d,_)=>{_?d():Us(d)}),l.augmentJob=d=>{t&&(d.flags|=4),f&&(d.flags|=2,c&&(d.id=c.uid,d.i=c))};const h=wf(e,t,l);return Fn&&(a?a.push(h):u&&h()),h}function gd(e,t,n){const o=this.proxy,s=me(e)?e.includes(".")?ol(o,e):()=>o[e]:e.bind(o,o);let r;K(t)?r=t:(r=t.handler,n=t);const i=Un(this),l=Xs(s,r.bind(o),n);return i(),l}function ol(e,t){const n=t.split(".");return()=>{let o=e;for(let s=0;s<n.length&&o;s++)o=o[n[s]];return o}}const vd=(e,t)=>t==="modelValue"||t==="model-value"?e.modelModifiers:e[`${t}Modifiers`]||e[`${He(t)}Modifiers`]||e[`${St(t)}Modifiers`];function yd(e,t,...n){if(e.isUnmounted)return;const o=e.vnode.props||le;let s=n;const r=t.startsWith("update:"),i=r&&vd(o,t.slice(7));i&&(i.trim&&(s=n.map(c=>me(c)?c.trim():c)),i.number&&(s=n.map(kc)));let l,u=o[l=xs(t)]||o[l=xs(He(t))];!u&&r&&(u=o[l=xs(St(t))]),u&&Qe(u,e,6,s);const a=o[l+"Once"];if(a){if(!e.emitted)e.emitted={};else if(e.emitted[l])return;e.emitted[l]=!0,Qe(a,e,6,s)}}function sl(e,t,n=!1){const o=t.emitsCache,s=o.get(e);if(s!==void 0)return s;const r=e.emits;let i={},l=!1;if(!K(e)){const u=a=>{const c=sl(a,t,!0);c&&(l=!0,be(i,c))};!n&&t.mixins.length&&t.mixins.forEach(u),e.extends&&u(e.extends),e.mixins&&e.mixins.forEach(u)}return!r&&!l?(de(e)&&o.set(e,null),null):(Y(r)?r.forEach(u=>i[u]=null):be(i,r),de(e)&&o.set(e,i),i)}function No(e,t){return!e||!ao(t)?!1:(t=t.slice(2).replace(/Once$/,""),oe(e,t[0].toLowerCase()+t.slice(1))||oe(e,St(t))||oe(e,t))}function jg(){}function rl(e){const{type:t,vnode:n,proxy:o,withProxy:s,propsOptions:[r],slots:i,attrs:l,emit:u,render:a,renderCache:c,props:f,data:h,setupState:d,ctx:_,inheritAttrs:v}=e,y=Co(e);let g,x;try{if(n.shapeFlag&4){const b=s||o,P=Tt.NODE_ENV!=="production"&&d.__isScriptSetup?new Proxy(b,{get(U,H,W){return Sf(`Property '${String(H)}' was accessed via 'this'. Avoid using 'this' in templates.`),Reflect.get(U,H,W)}}):b;g=nt(a.call(P,b,c,Tt.NODE_ENV!=="production"?Eo(f):f,d,h,_)),x=l}else{const b=t;Tt.NODE_ENV,g=nt(b.length>1?b(Tt.NODE_ENV!=="production"?Eo(f):f,Tt.NODE_ENV!=="production"?{get attrs(){return Eo(l)},slots:i,emit:u}:{attrs:l,slots:i,emit:u}):b(Tt.NODE_ENV!=="production"?Eo(f):f,null)),x=t.props?l:Ed(l)}}catch(b){Vn.length=0,xo(b,e,1),g=Se(At)}let D=g;if(x&&v!==!1){const b=Object.keys(x),{shapeFlag:P}=D;b.length&&P&7&&(r&&b.some(bs)&&(x=bd(x,r)),D=pn(D,x,!1,!0))}return n.dirs&&(D=pn(D,null,!1,!0),D.dirs=D.dirs?D.dirs.concat(n.dirs):n.dirs),n.transition&&Fs(D,n.transition),g=D,Co(y),g}const Ed=e=>{let t;for(const n in e)(n==="class"||n==="style"||ao(n))&&((t||(t={}))[n]=e[n]);return t},bd=(e,t)=>{const n={};for(const o in e)(!bs(o)||!(o.slice(9)in t))&&(n[o]=e[o]);return n};function wd(e,t,n){const{props:o,children:s,component:r}=e,{props:i,children:l,patchFlag:u}=t,a=r.emitsOptions;if(t.dirs||t.transition)return!0;if(n&&u>=0){if(u&1024)return!0;if(u&16)return o?il(o,i,a):!!i;if(u&8){const c=t.dynamicProps;for(let f=0;f<c.length;f++){const h=c[f];if(i[h]!==o[h]&&!No(a,h))return!0}}}else return(s||l)&&(!l||!l.$stable)?!0:o===i?!1:o?i?il(o,i,a):!0:!!i;return!1}function il(e,t,n){const o=Object.keys(t);if(o.length!==Object.keys(e).length)return!0;for(let s=0;s<o.length;s++){const r=o[s];if(t[r]!==e[r]&&!No(n,r))return!0}return!1}function Sd({vnode:e,parent:t},n){for(;t;){const o=t.subTree;if(o.suspense&&o.suspense.activeBranch===e&&(o.el=e.el),o===e)(e=t.vnode).el=n,t=t.parent;else break}}const ll=e=>e.__isSuspense;function xd(e,t){t&&t.pendingBranch?Y(e)?t.effects.push(...e):t.effects.push(e):Pf(e)}const Ie=Symbol.for("v-fgt"),Vo=Symbol.for("v-txt"),At=Symbol.for("v-cmt"),Zs=Symbol.for("v-stc"),Vn=[];let Me=null;function Re(e=!1){Vn.push(Me=e?null:[])}function Td(){Vn.pop(),Me=Vn[Vn.length-1]||null}let Ln=1;function ul(e,t=!1){Ln+=e,e<0&&Me&&t&&(Me.hasOnce=!0)}function al(e){return e.dynamicChildren=Ln>0?Me||on:null,Td(),Ln>0&&Me&&Me.push(e),e}function Ct(e,t,n,o,s,r){return al(ie(e,t,n,o,s,r,!0))}function dn(e,t,n,o,s){return al(Se(e,t,n,o,s,!0))}function $n(e){return e?e.__v_isVNode===!0:!1}function Mn(e,t){return e.type===t.type&&e.key===t.key}const cl=({key:e})=>e??null,Lo=({ref:e,ref_key:t,ref_for:n})=>(typeof e=="number"&&(e=""+e),e!=null?me(e)||ve(e)||K(e)?{i:ye,r:e,k:t,f:!!n}:e:null);function ie(e,t=null,n=null,o=0,s=null,r=e===Ie?0:1,i=!1,l=!1){const u={__v_isVNode:!0,__v_skip:!0,type:e,props:t,key:t&&cl(t),ref:t&&Lo(t),scopeId:Ao,slotScopeIds:null,children:n,component:null,suspense:null,ssContent:null,ssFallback:null,dirs:null,transition:null,el:null,anchor:null,target:null,targetStart:null,targetAnchor:null,staticCount:0,shapeFlag:r,patchFlag:o,dynamicProps:s,dynamicChildren:null,appContext:null,ctx:ye};return l?(Js(u,n),r&128&&e.normalize(u)):n&&(u.shapeFlag|=me(n)?8:16),Ln>0&&!i&&Me&&(u.patchFlag>0||r&6)&&u.patchFlag!==32&&Me.push(u),u}const Se=Od;function Od(e,t=null,n=null,o=0,s=null,r=!1){if((!e||e===Yf)&&(e=At),$n(e)){const l=pn(e,t,!0);return n&&Js(l,n),Ln>0&&!r&&Me&&(l.shapeFlag&6?Me[Me.indexOf(e)]=l:Me.push(l)),l.patchFlag=-2,l}if($d(e)&&(e=e.__vccOpts),t){t=fl(t);let{class:l,style:u}=t;l&&!me(l)&&(t.class=ht(l)),de(u)&&(Ls(u)&&!Y(u)&&(u=be({},u)),t.style=Ne(u))}const i=me(e)?1:ll(e)?128:Vf(e)?64:de(e)?4:K(e)?2:0;return ie(e,t,n,o,s,i,r,!0)}function fl(e){return e?Ls(e)||Wi(e)?be({},e):e:null}function pn(e,t,n=!1,o=!1){const{props:s,ref:r,patchFlag:i,children:l,transition:u}=e,a=t?dl(s||{},t):s,c={__v_isVNode:!0,__v_skip:!0,type:e.type,props:a,key:a&&cl(a),ref:t&&t.ref?n&&r?Y(r)?r.concat(Lo(t)):[r,Lo(t)]:Lo(t):r,scopeId:e.scopeId,slotScopeIds:e.slotScopeIds,children:l,target:e.target,targetStart:e.targetStart,targetAnchor:e.targetAnchor,staticCount:e.staticCount,shapeFlag:e.shapeFlag,patchFlag:t&&e.type!==Ie?i===-1?16:i|16:i,dynamicProps:e.dynamicProps,dynamicChildren:e.dynamicChildren,appContext:e.appContext,dirs:e.dirs,transition:u,component:e.component,suspense:e.suspense,ssContent:e.ssContent&&pn(e.ssContent),ssFallback:e.ssFallback&&pn(e.ssFallback),el:e.el,anchor:e.anchor,ctx:e.ctx,ce:e.ce};return u&&o&&Fs(c,u.clone(c)),c}function Ad(e=" ",t=0){return Se(Vo,null,e,t)}function $o(e="",t=!1){return t?(Re(),dn(At,null,e)):Se(At,null,e)}function nt(e){return e==null||typeof e=="boolean"?Se(At):Y(e)?Se(Ie,null,e.slice()):$n(e)?Dt(e):Se(Vo,null,String(e))}function Dt(e){return e.el===null&&e.patchFlag!==-1||e.memo?e:pn(e)}function Js(e,t){let n=0;const{shapeFlag:o}=e;if(t==null)t=null;else if(Y(t))n=16;else if(typeof t=="object")if(o&65){const s=t.default;s&&(s._c&&(s._d=!1),Js(e,s()),s._c&&(s._d=!0));return}else{n=32;const s=t._;!s&&!Wi(t)?t._ctx=ye:s===3&&ye&&(ye.slots._===1?t._=1:(t._=2,e.patchFlag|=1024))}else K(t)?(t={default:t,_ctx:ye},n=32):(t=String(t),o&64?(n=16,t=[Ad(t)]):n=8);e.children=t,e.shapeFlag|=n}function dl(...e){const t={};for(let n=0;n<e.length;n++){const o=e[n];for(const s in o)if(s==="class")t.class!==o.class&&(t.class=ht([t.class,o.class]));else if(s==="style")t.style=Ne([t.style,o.style]);else if(ao(s)){const r=t[s],i=o[s];i&&r!==i&&!(Y(r)&&r.includes(i))&&(t[s]=r?[].concat(r,i):i)}else s!==""&&(t[s]=o[s])}return t}function ot(e,t,n,o=null){Qe(e,t,7,[n,o])}const Cd=Hi();let Dd=0;function Pd(e,t,n){const o=e.type,s=(t?t.appContext:e.appContext)||Cd,r={uid:Dd++,vnode:e,type:o,parent:t,appContext:s,root:null,next:null,subTree:null,effect:null,update:null,job:null,scope:new Bc(!0),render:null,proxy:null,exposed:null,exposeProxy:null,withProxy:null,provides:t?t.provides:Object.create(s.provides),ids:t?t.ids:["",0,0],accessCache:null,renderCache:[],components:null,directives:null,propsOptions:Yi(o,s),emitsOptions:sl(o,s),emit:null,emitted:null,propsDefaults:le,inheritAttrs:o.inheritAttrs,ctx:le,data:le,props:le,attrs:le,slots:le,refs:le,setupState:le,setupContext:null,suspense:n,suspenseId:n?n.pendingId:0,asyncDep:null,asyncResolved:!1,isMounted:!1,isUnmounted:!1,isDeactivated:!1,bc:null,c:null,bm:null,m:null,bu:null,u:null,um:null,bum:null,da:null,a:null,rtg:null,rtc:null,ec:null,sp:null};return r.ctx={_:r},r.root=t?t.root:r,r.emit=yd.bind(null,r),e.ce&&e.ce(r),r}let ge=null;const Qs=()=>ge||ye;let Mo,er;{const e=ho(),t=(n,o)=>{let s;return(s=e[n])||(s=e[n]=[]),s.push(o),r=>{s.length>1?s.forEach(i=>i(r)):s[0](r)}};Mo=t("__VUE_INSTANCE_SETTERS__",n=>ge=n),er=t("__VUE_SSR_SETTERS__",n=>Fn=n)}const Un=e=>{const t=ge;return Mo(e),e.scope.on(),()=>{e.scope.off(),Mo(t)}},pl=()=>{ge&&ge.scope.off(),Mo(null)};function hl(e){return e.vnode.shapeFlag&4}let Fn=!1;function Id(e,t=!1,n=!1){t&&er(t);const{props:o,children:s}=e.vnode,r=hl(e);rd(e,o,r,t),ad(e,s,n);const i=r?Rd(e,t):void 0;return t&&er(!1),i}function Rd(e,t){const n=e.type;e.accessCache=Object.create(null),e.proxy=new Proxy(e.ctx,Xf);const{setup:o}=n;if(o){_t();const s=e.setupContext=o.length>1?Nd(e):null,r=Un(e),i=ln(o,e,0,[e.props,s]),l=Qr(i);if(mt(),r(),(l||e.sp)&&!fn(e)&&Pi(e),l){if(i.then(pl,pl),t)return i.then(u=>{_l(e,u)}).catch(u=>{xo(u,e,0)});e.asyncDep=i}else _l(e,i)}else ml(e)}function _l(e,t,n){K(t)?e.type.__ssrInlineRender?e.ssrRender=t:e.render=t:de(t)&&(e.setupState=Si(t)),ml(e)}function ml(e,t,n){const o=e.type;e.render||(e.render=o.render||Ke);{const s=Un(e);_t();try{Zf(e)}finally{mt(),s()}}}const kd={get(e,t){return we(e,"get",""),e[t]}};function Nd(e){const t=n=>{e.exposed=n||{}};return{attrs:new Proxy(e.attrs,kd),slots:e.slots,emit:e.emit,expose:t}}function Uo(e){return e.exposed?e.exposeProxy||(e.exposeProxy=new Proxy(Si(cf(e.exposed)),{get(t,n){if(n in t)return t[n];if(n in Rn)return Rn[n](e)},has(t,n){return n in t||n in Rn}})):e.proxy}const Vd=/(?:^|[-_])(\w)/g,Ld=e=>e.replace(Vd,t=>t.toUpperCase()).replace(/[-_]/g,"");function gl(e,t=!0){return K(e)?e.displayName||e.name:e.name||t&&e.__name}function vl(e,t,n=!1){let o=gl(t);if(!o&&t.__file){const s=t.__file.match(/([^/\\]+)\.\w+$/);s&&(o=s[1])}if(!o&&e&&e.parent){const s=r=>{for(const i in r)if(r[i]===t)return i};o=s(e.components||e.parent.type.components)||s(e.appContext.components)}return o?Ld(o):n?"App":"Anonymous"}function $d(e){return K(e)&&"__vccOpts"in e}const _e=(e,t)=>Ef(e,t,Fn);function Md(e,t,n){const o=arguments.length;return o===2?de(t)&&!Y(t)?$n(t)?Se(e,null,[t]):Se(e,t):Se(e,null,t):(o>3?n=Array.prototype.slice.call(arguments,2):o===3&&$n(n)&&(n=[n]),Se(e,t,n))}const Ud="3.5.13";let tr;const yl=typeof window<"u"&&window.trustedTypes;if(yl)try{tr=yl.createPolicy("vue",{createHTML:e=>e})}catch{}const El=tr?e=>tr.createHTML(e):e=>e,Fd="http://www.w3.org/2000/svg",Bd="http://www.w3.org/1998/Math/MathML",bt=typeof document<"u"?document:null,bl=bt&&bt.createElement("template"),Hd={insert:(e,t,n)=>{t.insertBefore(e,n||null)},remove:e=>{const t=e.parentNode;t&&t.removeChild(e)},createElement:(e,t,n,o)=>{const s=t==="svg"?bt.createElementNS(Fd,e):t==="mathml"?bt.createElementNS(Bd,e):n?bt.createElement(e,{is:n}):bt.createElement(e);return e==="select"&&o&&o.multiple!=null&&s.setAttribute("multiple",o.multiple),s},createText:e=>bt.createTextNode(e),createComment:e=>bt.createComment(e),setText:(e,t)=>{e.nodeValue=t},setElementText:(e,t)=>{e.textContent=t},parentNode:e=>e.parentNode,nextSibling:e=>e.nextSibling,querySelector:e=>bt.querySelector(e),setScopeId(e,t){e.setAttribute(t,"")},insertStaticContent(e,t,n,o,s,r){const i=n?n.previousSibling:t.lastChild;if(s&&(s===r||s.nextSibling))for(;t.insertBefore(s.cloneNode(!0),n),!(s===r||!(s=s.nextSibling)););else{bl.innerHTML=El(o==="svg"?`<svg>${e}</svg>`:o==="mathml"?`<math>${e}</math>`:e);const l=bl.content;if(o==="svg"||o==="mathml"){const u=l.firstChild;for(;u.firstChild;)l.appendChild(u.firstChild);l.removeChild(u)}t.insertBefore(l,n)}return[i?i.nextSibling:t.firstChild,n?n.previousSibling:t.lastChild]}},zd=Symbol("_vtc");function jd(e,t,n){const o=e[zd];o&&(t=(t?[t,...o]:[...o]).join(" ")),t==null?e.removeAttribute("class"):n?e.setAttribute("class",t):e.className=t}const Fo=Symbol("_vod"),wl=Symbol("_vsh"),st={beforeMount(e,{value:t},{transition:n}){e[Fo]=e.style.display==="none"?"":e.style.display,n&&t?n.beforeEnter(e):Bn(e,t)},mounted(e,{value:t},{transition:n}){n&&t&&n.enter(e)},updated(e,{value:t,oldValue:n},{transition:o}){!t!=!n&&(o?t?(o.beforeEnter(e),Bn(e,!0),o.enter(e)):o.leave(e,()=>{Bn(e,!1)}):Bn(e,t))},beforeUnmount(e,{value:t}){Bn(e,t)}};function Bn(e,t){e.style.display=t?e[Fo]:"none",e[wl]=!t}const Kd=Symbol(""),Wd=/(^|;)\s*display\s*:/;function Gd(e,t,n){const o=e.style,s=me(n);let r=!1;if(n&&!s){if(t)if(me(t))for(const i of t.split(";")){const l=i.slice(0,i.indexOf(":")).trim();n[l]==null&&Bo(o,l,"")}else for(const i in t)n[i]==null&&Bo(o,i,"");for(const i in n)i==="display"&&(r=!0),Bo(o,i,n[i])}else if(s){if(t!==n){const i=o[Kd];i&&(n+=";"+i),o.cssText=n,r=Wd.test(n)}}else t&&e.removeAttribute("style");Fo in e&&(e[Fo]=r?o.display:"",e[wl]&&(o.display="none"))}const Sl=/\s*!important$/;function Bo(e,t,n){if(Y(n))n.forEach(o=>Bo(e,t,o));else if(n==null&&(n=""),t.startsWith("--"))e.setProperty(t,n);else{const o=Yd(e,t);Sl.test(n)?e.setProperty(St(o),n.replace(Sl,""),"important"):e[o]=n}}const xl=["Webkit","Moz","ms"],nr={};function Yd(e,t){const n=nr[t];if(n)return n;let o=He(t);if(o!=="filter"&&o in e)return nr[t]=o;o=po(o);for(let s=0;s<xl.length;s++){const r=xl[s]+o;if(r in e)return nr[t]=r}return t}const Tl="http://www.w3.org/1999/xlink";function Ol(e,t,n,o,s,r=Uc(t)){o&&t.startsWith("xlink:")?n==null?e.removeAttributeNS(Tl,t.slice(6,t.length)):e.setAttributeNS(Tl,t,n):n==null||r&&!ni(n)?e.removeAttribute(t):e.setAttribute(t,r?"":Ht(n)?String(n):n)}function Al(e,t,n,o,s){if(t==="innerHTML"||t==="textContent"){n!=null&&(e[t]=t==="innerHTML"?El(n):n);return}const r=e.tagName;if(t==="value"&&r!=="PROGRESS"&&!r.includes("-")){const l=r==="OPTION"?e.getAttribute("value")||"":e.value,u=n==null?e.type==="checkbox"?"on":"":String(n);(l!==u||!("_value"in e))&&(e.value=u),n==null&&e.removeAttribute(t),e._value=n;return}let i=!1;if(n===""||n==null){const l=typeof e[t];l==="boolean"?n=ni(n):n==null&&l==="string"?(n="",i=!0):l==="number"&&(n=0,i=!0)}try{e[t]=n}catch{}i&&e.removeAttribute(s||t)}function qd(e,t,n,o){e.addEventListener(t,n,o)}function Xd(e,t,n,o){e.removeEventListener(t,n,o)}const Cl=Symbol("_vei");function Zd(e,t,n,o,s=null){const r=e[Cl]||(e[Cl]={}),i=r[t];if(o&&i)i.value=o;else{const[l,u]=Jd(t);if(o){const a=r[t]=tp(o,s);qd(e,l,a,u)}else i&&(Xd(e,l,i,u),r[t]=void 0)}}const Dl=/(?:Once|Passive|Capture)$/;function Jd(e){let t;if(Dl.test(e)){t={};let o;for(;o=e.match(Dl);)e=e.slice(0,e.length-o[0].length),t[o[0].toLowerCase()]=!0}return[e[2]===":"?e.slice(3):St(e.slice(2)),t]}let or=0;const Qd=Promise.resolve(),ep=()=>or||(Qd.then(()=>or=0),or=Date.now());function tp(e,t){const n=o=>{if(!o._vts)o._vts=Date.now();else if(o._vts<=n.attached)return;Qe(np(o,n.value),t,5,[o])};return n.value=e,n.attached=ep(),n}function np(e,t){if(Y(t)){const n=e.stopImmediatePropagation;return e.stopImmediatePropagation=()=>{n.call(e),e._stopped=!0},t.map(o=>s=>!s._stopped&&o&&o(s))}else return t}const Pl=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&e.charCodeAt(2)>96&&e.charCodeAt(2)<123,op=(e,t,n,o,s,r)=>{const i=s==="svg";t==="class"?jd(e,o,i):t==="style"?Gd(e,n,o):ao(t)?bs(t)||Zd(e,t,n,o,r):(t[0]==="."?(t=t.slice(1),!0):t[0]==="^"?(t=t.slice(1),!1):sp(e,t,o,i))?(Al(e,t,o),!e.tagName.includes("-")&&(t==="value"||t==="checked"||t==="selected")&&Ol(e,t,o,i,r,t!=="value")):e._isVueCE&&(/[A-Z]/.test(t)||!me(o))?Al(e,He(t),o,r,t):(t==="true-value"?e._trueValue=o:t==="false-value"&&(e._falseValue=o),Ol(e,t,o,i))};function sp(e,t,n,o){if(o)return!!(t==="innerHTML"||t==="textContent"||t in e&&Pl(t)&&K(n));if(t==="spellcheck"||t==="draggable"||t==="translate"||t==="form"||t==="list"&&e.tagName==="INPUT"||t==="type"&&e.tagName==="TEXTAREA")return!1;if(t==="width"||t==="height"){const s=e.tagName;if(s==="IMG"||s==="VIDEO"||s==="CANVAS"||s==="SOURCE")return!1}return Pl(t)&&me(n)?!1:t in e}const rp=["ctrl","shift","alt","meta"],ip={stop:e=>e.stopPropagation(),prevent:e=>e.preventDefault(),self:e=>e.target!==e.currentTarget,ctrl:e=>!e.ctrlKey,shift:e=>!e.shiftKey,alt:e=>!e.altKey,meta:e=>!e.metaKey,left:e=>"button"in e&&e.button!==0,middle:e=>"button"in e&&e.button!==1,right:e=>"button"in e&&e.button!==2,exact:(e,t)=>rp.some(n=>e[`${n}Key`]&&!t.includes(n))},Pt=(e,t)=>{const n=e._withMods||(e._withMods={}),o=t.join(".");return n[o]||(n[o]=(s,...r)=>{for(let i=0;i<t.length;i++){const l=ip[t[i]];if(l&&l(s,t))return}return e(s,...r)})},lp={esc:"escape",space:" ",up:"arrow-up",left:"arrow-left",right:"arrow-right",down:"arrow-down",delete:"backspace"},up=(e,t)=>{const n=e._withKeys||(e._withKeys={}),o=t.join(".");return n[o]||(n[o]=s=>{if(!("key"in s))return;const r=St(s.key);if(t.some(i=>i===r||lp[i]===r))return e(s)})},ap=be({patchProp:op},Hd);let Il;function cp(){return Il||(Il=fd(ap))}const fp=(...e)=>{const t=cp().createApp(...e),{mount:n}=t;return t.mount=o=>{const s=pp(o);if(!s)return;const r=t._component;!K(r)&&!r.render&&!r.template&&(r.template=s.innerHTML),s.nodeType===1&&(s.textContent="");const i=n(s,!1,dp(s));return s instanceof Element&&(s.removeAttribute("v-cloak"),s.setAttribute("data-v-app","")),i},t};function dp(e){if(e instanceof SVGElement)return"svg";if(typeof MathMLElement=="function"&&e instanceof MathMLElement)return"mathml"}function pp(e){return me(e)?document.querySelector(e):e}var hp=Object.create,Rl=Object.defineProperty,_p=Object.getOwnPropertyDescriptor,sr=Object.getOwnPropertyNames,mp=Object.getPrototypeOf,gp=Object.prototype.hasOwnProperty,vp=(e,t)=>function(){return e&&(t=(0,e[sr(e)[0]])(e=0)),t},yp=(e,t)=>function(){return t||(0,e[sr(e)[0]])((t={exports:{}}).exports,t),t.exports},Ep=(e,t,n,o)=>{if(t&&typeof t=="object"||typeof t=="function")for(let s of sr(t))!gp.call(e,s)&&s!==n&&Rl(e,s,{get:()=>t[s],enumerable:!(o=_p(t,s))||o.enumerable});return e},bp=(e,t,n)=>(n=e!=null?hp(mp(e)):{},Ep(Rl(n,"default",{value:e,enumerable:!0}),e)),Hn=vp({"../../node_modules/.pnpm/tsup@8.4.0_@microsoft+api-extractor@7.51.1_@types+node@22.13.14__jiti@2.4.2_postcss@8.5_96eb05a9d65343021e53791dd83f3773/node_modules/tsup/assets/esm_shims.js"(){}}),wp=yp({"../../node_modules/.pnpm/rfdc@1.4.1/node_modules/rfdc/index.js"(e,t){Hn(),t.exports=o;function n(r){return r instanceof Buffer?Buffer.from(r):new r.constructor(r.buffer.slice(),r.byteOffset,r.length)}function o(r){if(r=r||{},r.circles)return s(r);const i=new Map;if(i.set(Date,f=>new Date(f)),i.set(Map,(f,h)=>new Map(u(Array.from(f),h))),i.set(Set,(f,h)=>new Set(u(Array.from(f),h))),r.constructorHandlers)for(const f of r.constructorHandlers)i.set(f[0],f[1]);let l=null;return r.proto?c:a;function u(f,h){const d=Object.keys(f),_=new Array(d.length);for(let v=0;v<d.length;v++){const y=d[v],g=f[y];typeof g!="object"||g===null?_[y]=g:g.constructor!==Object&&(l=i.get(g.constructor))?_[y]=l(g,h):ArrayBuffer.isView(g)?_[y]=n(g):_[y]=h(g)}return _}function a(f){if(typeof f!="object"||f===null)return f;if(Array.isArray(f))return u(f,a);if(f.constructor!==Object&&(l=i.get(f.constructor)))return l(f,a);const h={};for(const d in f){if(Object.hasOwnProperty.call(f,d)===!1)continue;const _=f[d];typeof _!="object"||_===null?h[d]=_:_.constructor!==Object&&(l=i.get(_.constructor))?h[d]=l(_,a):ArrayBuffer.isView(_)?h[d]=n(_):h[d]=a(_)}return h}function c(f){if(typeof f!="object"||f===null)return f;if(Array.isArray(f))return u(f,c);if(f.constructor!==Object&&(l=i.get(f.constructor)))return l(f,c);const h={};for(const d in f){const _=f[d];typeof _!="object"||_===null?h[d]=_:_.constructor!==Object&&(l=i.get(_.constructor))?h[d]=l(_,c):ArrayBuffer.isView(_)?h[d]=n(_):h[d]=c(_)}return h}}function s(r){const i=[],l=[],u=new Map;if(u.set(Date,d=>new Date(d)),u.set(Map,(d,_)=>new Map(c(Array.from(d),_))),u.set(Set,(d,_)=>new Set(c(Array.from(d),_))),r.constructorHandlers)for(const d of r.constructorHandlers)u.set(d[0],d[1]);let a=null;return r.proto?h:f;function c(d,_){const v=Object.keys(d),y=new Array(v.length);for(let g=0;g<v.length;g++){const x=v[g],D=d[x];if(typeof D!="object"||D===null)y[x]=D;else if(D.constructor!==Object&&(a=u.get(D.constructor)))y[x]=a(D,_);else if(ArrayBuffer.isView(D))y[x]=n(D);else{const b=i.indexOf(D);b!==-1?y[x]=l[b]:y[x]=_(D)}}return y}function f(d){if(typeof d!="object"||d===null)return d;if(Array.isArray(d))return c(d,f);if(d.constructor!==Object&&(a=u.get(d.constructor)))return a(d,f);const _={};i.push(d),l.push(_);for(const v in d){if(Object.hasOwnProperty.call(d,v)===!1)continue;const y=d[v];if(typeof y!="object"||y===null)_[v]=y;else if(y.constructor!==Object&&(a=u.get(y.constructor)))_[v]=a(y,f);else if(ArrayBuffer.isView(y))_[v]=n(y);else{const g=i.indexOf(y);g!==-1?_[v]=l[g]:_[v]=f(y)}}return i.pop(),l.pop(),_}function h(d){if(typeof d!="object"||d===null)return d;if(Array.isArray(d))return c(d,h);if(d.constructor!==Object&&(a=u.get(d.constructor)))return a(d,h);const _={};i.push(d),l.push(_);for(const v in d){const y=d[v];if(typeof y!="object"||y===null)_[v]=y;else if(y.constructor!==Object&&(a=u.get(y.constructor)))_[v]=a(y,h);else if(ArrayBuffer.isView(y))_[v]=n(y);else{const g=i.indexOf(y);g!==-1?_[v]=l[g]:_[v]=h(y)}}return i.pop(),l.pop(),_}}}});Hn(),Hn(),Hn();var Ho=typeof navigator<"u",O=typeof window<"u"?window:typeof globalThis<"u"?globalThis:typeof global<"u"?global:{};typeof O.chrome<"u"&&O.chrome.devtools,Ho&&(O.self,O.top);var kl;typeof navigator<"u"&&((kl=navigator.userAgent)==null||kl.toLowerCase().includes("electron"));var Sp=typeof window<"u"&&!!window.__NUXT__;Hn();var xp=bp(wp()),Tp=/(?:^|[-_/])(\w)/g,Op=/-(\w)/g,Ap=/([a-z0-9])([A-Z])/g;function Nl(e,t){return t?t.toUpperCase():""}function Vl(e){return e&&`${e}`.replace(Tp,Nl)}function Cp(e){return e&&e.replace(Op,Nl)}function Dp(e){return e&&e.replace(Ap,(t,n,o)=>`${n}-${o}`).toLowerCase()}function Pp(e,t){let n=e.replace(/^[a-z]:/i,"").replace(/\\/g,"/");n.endsWith(`index${t}`)&&(n=n.replace(`/index${t}`,t));const o=n.lastIndexOf("/"),s=n.substring(o+1);{const r=s.lastIndexOf(t);return s.substring(0,r)}}var Ll=(0,xp.default)({circles:!0});const Ip={trailing:!0};function It(e,t=25,n={}){if(n={...Ip,...n},!Number.isFinite(t))throw new TypeError("Expected `wait` to be a finite number");let o,s,r=[],i,l;const u=(a,c)=>(i=Rp(e,a,c),i.finally(()=>{if(i=null,n.trailing&&l&&!s){const f=u(a,l);return l=null,f}}),i);return function(...a){return i?(n.trailing&&(l=a),i):new Promise(c=>{const f=!s&&n.leading;clearTimeout(s),s=setTimeout(()=>{s=null;const h=n.leading?o:u(this,a);for(const d of r)d(h);r=[]},t),f?(o=u(this,a),c(o)):r.push(c)})}}async function Rp(e,t,n){return await e.apply(t,n)}function rr(e,t={},n){for(const o in e){const s=e[o],r=n?`${n}:${o}`:o;typeof s=="object"&&s!==null?rr(s,t,r):typeof s=="function"&&(t[r]=s)}return t}const kp={run:e=>e()},Np=()=>kp,$l=typeof console.createTask<"u"?console.createTask:Np;function Vp(e,t){const n=t.shift(),o=$l(n);return e.reduce((s,r)=>s.then(()=>o.run(()=>r(...t))),Promise.resolve())}function Lp(e,t){const n=t.shift(),o=$l(n);return Promise.all(e.map(s=>o.run(()=>s(...t))))}function ir(e,t){for(const n of[...e])n(t)}let $p=class{constructor(){this._hooks={},this._before=void 0,this._after=void 0,this._deprecatedMessages=void 0,this._deprecatedHooks={},this.hook=this.hook.bind(this),this.callHook=this.callHook.bind(this),this.callHookWith=this.callHookWith.bind(this)}hook(t,n,o={}){if(!t||typeof n!="function")return()=>{};const s=t;let r;for(;this._deprecatedHooks[t];)r=this._deprecatedHooks[t],t=r.to;if(r&&!o.allowDeprecated){let i=r.message;i||(i=`${s} hook has been deprecated`+(r.to?`, please use ${r.to}`:"")),this._deprecatedMessages||(this._deprecatedMessages=new Set),this._deprecatedMessages.has(i)||(console.warn(i),this._deprecatedMessages.add(i))}if(!n.name)try{Object.defineProperty(n,"name",{get:()=>"_"+t.replace(/\W+/g,"_")+"_hook_cb",configurable:!0})}catch{}return this._hooks[t]=this._hooks[t]||[],this._hooks[t].push(n),()=>{n&&(this.removeHook(t,n),n=void 0)}}hookOnce(t,n){let o,s=(...r)=>(typeof o=="function"&&o(),o=void 0,s=void 0,n(...r));return o=this.hook(t,s),o}removeHook(t,n){if(this._hooks[t]){const o=this._hooks[t].indexOf(n);o!==-1&&this._hooks[t].splice(o,1),this._hooks[t].length===0&&delete this._hooks[t]}}deprecateHook(t,n){this._deprecatedHooks[t]=typeof n=="string"?{to:n}:n;const o=this._hooks[t]||[];delete this._hooks[t];for(const s of o)this.hook(t,s)}deprecateHooks(t){Object.assign(this._deprecatedHooks,t);for(const n in t)this.deprecateHook(n,t[n])}addHooks(t){const n=rr(t),o=Object.keys(n).map(s=>this.hook(s,n[s]));return()=>{for(const s of o.splice(0,o.length))s()}}removeHooks(t){const n=rr(t);for(const o in n)this.removeHook(o,n[o])}removeAllHooks(){for(const t in this._hooks)delete this._hooks[t]}callHook(t,...n){return n.unshift(t),this.callHookWith(Vp,t,...n)}callHookParallel(t,...n){return n.unshift(t),this.callHookWith(Lp,t,...n)}callHookWith(t,n,...o){const s=this._before||this._after?{name:n,args:o,context:{}}:void 0;this._before&&ir(this._before,s);const r=t(n in this._hooks?[...this._hooks[n]]:[],o);return r instanceof Promise?r.finally(()=>{this._after&&s&&ir(this._after,s)}):(this._after&&s&&ir(this._after,s),r)}beforeEach(t){return this._before=this._before||[],this._before.push(t),()=>{if(this._before!==void 0){const n=this._before.indexOf(t);n!==-1&&this._before.splice(n,1)}}}afterEach(t){return this._after=this._after||[],this._after.push(t),()=>{if(this._after!==void 0){const n=this._after.indexOf(t);n!==-1&&this._after.splice(n,1)}}}};function Ml(){return new $p}var Mp=Object.create,Ul=Object.defineProperty,Up=Object.getOwnPropertyDescriptor,lr=Object.getOwnPropertyNames,Fp=Object.getPrototypeOf,Bp=Object.prototype.hasOwnProperty,Hp=(e,t)=>function(){return e&&(t=(0,e[lr(e)[0]])(e=0)),t},Fl=(e,t)=>function(){return t||(0,e[lr(e)[0]])((t={exports:{}}).exports,t),t.exports},zp=(e,t,n,o)=>{if(t&&typeof t=="object"||typeof t=="function")for(let s of lr(t))!Bp.call(e,s)&&s!==n&&Ul(e,s,{get:()=>t[s],enumerable:!(o=Up(t,s))||o.enumerable});return e},jp=(e,t,n)=>(n=e!=null?Mp(Fp(e)):{},zp(Ul(n,"default",{value:e,enumerable:!0}),e)),w=Hp({"../../node_modules/.pnpm/tsup@8.4.0_@microsoft+api-extractor@7.51.1_@types+node@22.13.14__jiti@2.4.2_postcss@8.5_96eb05a9d65343021e53791dd83f3773/node_modules/tsup/assets/esm_shims.js"(){}}),Kp=Fl({"../../node_modules/.pnpm/speakingurl@14.0.1/node_modules/speakingurl/lib/speakingurl.js"(e,t){w(),function(n){var o={À:"A",Á:"A",Â:"A",Ã:"A",Ä:"Ae",Å:"A",Æ:"AE",Ç:"C",È:"E",É:"E",Ê:"E",Ë:"E",Ì:"I",Í:"I",Î:"I",Ï:"I",Ð:"D",Ñ:"N",Ò:"O",Ó:"O",Ô:"O",Õ:"O",Ö:"Oe",Ő:"O",Ø:"O",Ù:"U",Ú:"U",Û:"U",Ü:"Ue",Ű:"U",Ý:"Y",Þ:"TH",ß:"ss",à:"a",á:"a",â:"a",ã:"a",ä:"ae",å:"a",æ:"ae",ç:"c",è:"e",é:"e",ê:"e",ë:"e",ì:"i",í:"i",î:"i",ï:"i",ð:"d",ñ:"n",ò:"o",ó:"o",ô:"o",õ:"o",ö:"oe",ő:"o",ø:"o",ù:"u",ú:"u",û:"u",ü:"ue",ű:"u",ý:"y",þ:"th",ÿ:"y","ẞ":"SS",ا:"a",أ:"a",إ:"i",آ:"aa",ؤ:"u",ئ:"e",ء:"a",ب:"b",ت:"t",ث:"th",ج:"j",ح:"h",خ:"kh",د:"d",ذ:"th",ر:"r",ز:"z",س:"s",ش:"sh",ص:"s",ض:"dh",ط:"t",ظ:"z",ع:"a",غ:"gh",ف:"f",ق:"q",ك:"k",ل:"l",م:"m",ن:"n",ه:"h",و:"w",ي:"y",ى:"a",ة:"h",ﻻ:"la",ﻷ:"laa",ﻹ:"lai",ﻵ:"laa",گ:"g",چ:"ch",پ:"p",ژ:"zh",ک:"k",ی:"y","َ":"a","ً":"an","ِ":"e","ٍ":"en","ُ":"u","ٌ":"on","ْ":"","٠":"0","١":"1","٢":"2","٣":"3","٤":"4","٥":"5","٦":"6","٧":"7","٨":"8","٩":"9","۰":"0","۱":"1","۲":"2","۳":"3","۴":"4","۵":"5","۶":"6","۷":"7","۸":"8","۹":"9",က:"k",ခ:"kh",ဂ:"g",ဃ:"ga",င:"ng",စ:"s",ဆ:"sa",ဇ:"z","စျ":"za",ည:"ny",ဋ:"t",ဌ:"ta",ဍ:"d",ဎ:"da",ဏ:"na",တ:"t",ထ:"ta",ဒ:"d",ဓ:"da",န:"n",ပ:"p",ဖ:"pa",ဗ:"b",ဘ:"ba",မ:"m",ယ:"y",ရ:"ya",လ:"l",ဝ:"w",သ:"th",ဟ:"h",ဠ:"la",အ:"a","ြ":"y","ျ":"ya","ွ":"w","ြွ":"yw","ျွ":"ywa","ှ":"h",ဧ:"e","၏":"-e",ဣ:"i",ဤ:"-i",ဉ:"u",ဦ:"-u",ဩ:"aw","သြော":"aw",ဪ:"aw","၀":"0","၁":"1","၂":"2","၃":"3","၄":"4","၅":"5","၆":"6","၇":"7","၈":"8","၉":"9","္":"","့":"","း":"",č:"c",ď:"d",ě:"e",ň:"n",ř:"r",š:"s",ť:"t",ů:"u",ž:"z",Č:"C",Ď:"D",Ě:"E",Ň:"N",Ř:"R",Š:"S",Ť:"T",Ů:"U",Ž:"Z",ހ:"h",ށ:"sh",ނ:"n",ރ:"r",ބ:"b",ޅ:"lh",ކ:"k",އ:"a",ވ:"v",މ:"m",ފ:"f",ދ:"dh",ތ:"th",ލ:"l",ގ:"g",ޏ:"gn",ސ:"s",ޑ:"d",ޒ:"z",ޓ:"t",ޔ:"y",ޕ:"p",ޖ:"j",ޗ:"ch",ޘ:"tt",ޙ:"hh",ޚ:"kh",ޛ:"th",ޜ:"z",ޝ:"sh",ޞ:"s",ޟ:"d",ޠ:"t",ޡ:"z",ޢ:"a",ޣ:"gh",ޤ:"q",ޥ:"w","ަ":"a","ާ":"aa","ި":"i","ީ":"ee","ު":"u","ޫ":"oo","ެ":"e","ޭ":"ey","ޮ":"o","ޯ":"oa","ް":"",ა:"a",ბ:"b",გ:"g",დ:"d",ე:"e",ვ:"v",ზ:"z",თ:"t",ი:"i",კ:"k",ლ:"l",მ:"m",ნ:"n",ო:"o",პ:"p",ჟ:"zh",რ:"r",ს:"s",ტ:"t",უ:"u",ფ:"p",ქ:"k",ღ:"gh",ყ:"q",შ:"sh",ჩ:"ch",ც:"ts",ძ:"dz",წ:"ts",ჭ:"ch",ხ:"kh",ჯ:"j",ჰ:"h",α:"a",β:"v",γ:"g",δ:"d",ε:"e",ζ:"z",η:"i",θ:"th",ι:"i",κ:"k",λ:"l",μ:"m",ν:"n",ξ:"ks",ο:"o",π:"p",ρ:"r",σ:"s",τ:"t",υ:"y",φ:"f",χ:"x",ψ:"ps",ω:"o",ά:"a",έ:"e",ί:"i",ό:"o",ύ:"y",ή:"i",ώ:"o",ς:"s",ϊ:"i",ΰ:"y",ϋ:"y",ΐ:"i",Α:"A",Β:"B",Γ:"G",Δ:"D",Ε:"E",Ζ:"Z",Η:"I",Θ:"TH",Ι:"I",Κ:"K",Λ:"L",Μ:"M",Ν:"N",Ξ:"KS",Ο:"O",Π:"P",Ρ:"R",Σ:"S",Τ:"T",Υ:"Y",Φ:"F",Χ:"X",Ψ:"PS",Ω:"O",Ά:"A",Έ:"E",Ί:"I",Ό:"O",Ύ:"Y",Ή:"I",Ώ:"O",Ϊ:"I",Ϋ:"Y",ā:"a",ē:"e",ģ:"g",ī:"i",ķ:"k",ļ:"l",ņ:"n",ū:"u",Ā:"A",Ē:"E",Ģ:"G",Ī:"I",Ķ:"k",Ļ:"L",Ņ:"N",Ū:"U",Ќ:"Kj",ќ:"kj",Љ:"Lj",љ:"lj",Њ:"Nj",њ:"nj",Тс:"Ts",тс:"ts",ą:"a",ć:"c",ę:"e",ł:"l",ń:"n",ś:"s",ź:"z",ż:"z",Ą:"A",Ć:"C",Ę:"E",Ł:"L",Ń:"N",Ś:"S",Ź:"Z",Ż:"Z",Є:"Ye",І:"I",Ї:"Yi",Ґ:"G",є:"ye",і:"i",ї:"yi",ґ:"g",ă:"a",Ă:"A",ș:"s",Ș:"S",ț:"t",Ț:"T",ţ:"t",Ţ:"T",а:"a",б:"b",в:"v",г:"g",д:"d",е:"e",ё:"yo",ж:"zh",з:"z",и:"i",й:"i",к:"k",л:"l",м:"m",н:"n",о:"o",п:"p",р:"r",с:"s",т:"t",у:"u",ф:"f",х:"kh",ц:"c",ч:"ch",ш:"sh",щ:"sh",ъ:"",ы:"y",ь:"",э:"e",ю:"yu",я:"ya",А:"A",Б:"B",В:"V",Г:"G",Д:"D",Е:"E",Ё:"Yo",Ж:"Zh",З:"Z",И:"I",Й:"I",К:"K",Л:"L",М:"M",Н:"N",О:"O",П:"P",Р:"R",С:"S",Т:"T",У:"U",Ф:"F",Х:"Kh",Ц:"C",Ч:"Ch",Ш:"Sh",Щ:"Sh",Ъ:"",Ы:"Y",Ь:"",Э:"E",Ю:"Yu",Я:"Ya",ђ:"dj",ј:"j",ћ:"c",џ:"dz",Ђ:"Dj",Ј:"j",Ћ:"C",Џ:"Dz",ľ:"l",ĺ:"l",ŕ:"r",Ľ:"L",Ĺ:"L",Ŕ:"R",ş:"s",Ş:"S",ı:"i",İ:"I",ğ:"g",Ğ:"G",ả:"a",Ả:"A",ẳ:"a",Ẳ:"A",ẩ:"a",Ẩ:"A",đ:"d",Đ:"D",ẹ:"e",Ẹ:"E",ẽ:"e",Ẽ:"E",ẻ:"e",Ẻ:"E",ế:"e",Ế:"E",ề:"e",Ề:"E",ệ:"e",Ệ:"E",ễ:"e",Ễ:"E",ể:"e",Ể:"E",ỏ:"o",ọ:"o",Ọ:"o",ố:"o",Ố:"O",ồ:"o",Ồ:"O",ổ:"o",Ổ:"O",ộ:"o",Ộ:"O",ỗ:"o",Ỗ:"O",ơ:"o",Ơ:"O",ớ:"o",Ớ:"O",ờ:"o",Ờ:"O",ợ:"o",Ợ:"O",ỡ:"o",Ỡ:"O",Ở:"o",ở:"o",ị:"i",Ị:"I",ĩ:"i",Ĩ:"I",ỉ:"i",Ỉ:"i",ủ:"u",Ủ:"U",ụ:"u",Ụ:"U",ũ:"u",Ũ:"U",ư:"u",Ư:"U",ứ:"u",Ứ:"U",ừ:"u",Ừ:"U",ự:"u",Ự:"U",ữ:"u",Ữ:"U",ử:"u",Ử:"ư",ỷ:"y",Ỷ:"y",ỳ:"y",Ỳ:"Y",ỵ:"y",Ỵ:"Y",ỹ:"y",Ỹ:"Y",ạ:"a",Ạ:"A",ấ:"a",Ấ:"A",ầ:"a",Ầ:"A",ậ:"a",Ậ:"A",ẫ:"a",Ẫ:"A",ắ:"a",Ắ:"A",ằ:"a",Ằ:"A",ặ:"a",Ặ:"A",ẵ:"a",Ẵ:"A","⓪":"0","①":"1","②":"2","③":"3","④":"4","⑤":"5","⑥":"6","⑦":"7","⑧":"8","⑨":"9","⑩":"10","⑪":"11","⑫":"12","⑬":"13","⑭":"14","⑮":"15","⑯":"16","⑰":"17","⑱":"18","⑲":"18","⑳":"18","⓵":"1","⓶":"2","⓷":"3","⓸":"4","⓹":"5","⓺":"6","⓻":"7","⓼":"8","⓽":"9","⓾":"10","⓿":"0","⓫":"11","⓬":"12","⓭":"13","⓮":"14","⓯":"15","⓰":"16","⓱":"17","⓲":"18","⓳":"19","⓴":"20","Ⓐ":"A","Ⓑ":"B","Ⓒ":"C","Ⓓ":"D","Ⓔ":"E","Ⓕ":"F","Ⓖ":"G","Ⓗ":"H","Ⓘ":"I","Ⓙ":"J","Ⓚ":"K","Ⓛ":"L","Ⓜ":"M","Ⓝ":"N","Ⓞ":"O","Ⓟ":"P","Ⓠ":"Q","Ⓡ":"R","Ⓢ":"S","Ⓣ":"T","Ⓤ":"U","Ⓥ":"V","Ⓦ":"W","Ⓧ":"X","Ⓨ":"Y","Ⓩ":"Z","ⓐ":"a","ⓑ":"b","ⓒ":"c","ⓓ":"d","ⓔ":"e","ⓕ":"f","ⓖ":"g","ⓗ":"h","ⓘ":"i","ⓙ":"j","ⓚ":"k","ⓛ":"l","ⓜ":"m","ⓝ":"n","ⓞ":"o","ⓟ":"p","ⓠ":"q","ⓡ":"r","ⓢ":"s","ⓣ":"t","ⓤ":"u","ⓦ":"v","ⓥ":"w","ⓧ":"x","ⓨ":"y","ⓩ":"z","“":'"',"”":'"',"‘":"'","’":"'","∂":"d",ƒ:"f","™":"(TM)","©":"(C)",œ:"oe",Œ:"OE","®":"(R)","†":"+","℠":"(SM)","…":"...","˚":"o",º:"o",ª:"a","•":"*","၊":",","။":".",$:"USD","€":"EUR","₢":"BRN","₣":"FRF","£":"GBP","₤":"ITL","₦":"NGN","₧":"ESP","₩":"KRW","₪":"ILS","₫":"VND","₭":"LAK","₮":"MNT","₯":"GRD","₱":"ARS","₲":"PYG","₳":"ARA","₴":"UAH","₵":"GHS","¢":"cent","¥":"CNY",元:"CNY",円:"YEN","﷼":"IRR","₠":"EWE","฿":"THB","₨":"INR","₹":"INR","₰":"PF","₺":"TRY","؋":"AFN","₼":"AZN",лв:"BGN","៛":"KHR","₡":"CRC","₸":"KZT",ден:"MKD",zł:"PLN","₽":"RUB","₾":"GEL"},s=["်","ް"],r={"ာ":"a","ါ":"a","ေ":"e","ဲ":"e","ိ":"i","ီ":"i","ို":"o","ု":"u","ူ":"u","ေါင်":"aung","ော":"aw","ော်":"aw","ေါ":"aw","ေါ်":"aw","်":"်","က်":"et","ိုက်":"aik","ောက်":"auk","င်":"in","ိုင်":"aing","ောင်":"aung","စ်":"it","ည်":"i","တ်":"at","ိတ်":"eik","ုတ်":"ok","ွတ်":"ut","ေတ်":"it","ဒ်":"d","ိုဒ်":"ok","ုဒ်":"ait","န်":"an","ာန်":"an","ိန်":"ein","ုန်":"on","ွန်":"un","ပ်":"at","ိပ်":"eik","ုပ်":"ok","ွပ်":"ut","န်ုပ်":"nub","မ်":"an","ိမ်":"ein","ုမ်":"on","ွမ်":"un","ယ်":"e","ိုလ်":"ol","ဉ်":"in","ံ":"an","ိံ":"ein","ုံ":"on","ައް":"ah","ަށް":"ah"},i={en:{},az:{ç:"c",ə:"e",ğ:"g",ı:"i",ö:"o",ş:"s",ü:"u",Ç:"C",Ə:"E",Ğ:"G",İ:"I",Ö:"O",Ş:"S",Ü:"U"},cs:{č:"c",ď:"d",ě:"e",ň:"n",ř:"r",š:"s",ť:"t",ů:"u",ž:"z",Č:"C",Ď:"D",Ě:"E",Ň:"N",Ř:"R",Š:"S",Ť:"T",Ů:"U",Ž:"Z"},fi:{ä:"a",Ä:"A",ö:"o",Ö:"O"},hu:{ä:"a",Ä:"A",ö:"o",Ö:"O",ü:"u",Ü:"U",ű:"u",Ű:"U"},lt:{ą:"a",č:"c",ę:"e",ė:"e",į:"i",š:"s",ų:"u",ū:"u",ž:"z",Ą:"A",Č:"C",Ę:"E",Ė:"E",Į:"I",Š:"S",Ų:"U",Ū:"U"},lv:{ā:"a",č:"c",ē:"e",ģ:"g",ī:"i",ķ:"k",ļ:"l",ņ:"n",š:"s",ū:"u",ž:"z",Ā:"A",Č:"C",Ē:"E",Ģ:"G",Ī:"i",Ķ:"k",Ļ:"L",Ņ:"N",Š:"S",Ū:"u",Ž:"Z"},pl:{ą:"a",ć:"c",ę:"e",ł:"l",ń:"n",ó:"o",ś:"s",ź:"z",ż:"z",Ą:"A",Ć:"C",Ę:"e",Ł:"L",Ń:"N",Ó:"O",Ś:"S",Ź:"Z",Ż:"Z"},sv:{ä:"a",Ä:"A",ö:"o",Ö:"O"},sk:{ä:"a",Ä:"A"},sr:{љ:"lj",њ:"nj",Љ:"Lj",Њ:"Nj",đ:"dj",Đ:"Dj"},tr:{Ü:"U",Ö:"O",ü:"u",ö:"o"}},l={ar:{"∆":"delta","∞":"la-nihaya","♥":"hob","&":"wa","|":"aw","<":"aqal-men",">":"akbar-men","∑":"majmou","¤":"omla"},az:{},ca:{"∆":"delta","∞":"infinit","♥":"amor","&":"i","|":"o","<":"menys que",">":"mes que","∑":"suma dels","¤":"moneda"},cs:{"∆":"delta","∞":"nekonecno","♥":"laska","&":"a","|":"nebo","<":"mensi nez",">":"vetsi nez","∑":"soucet","¤":"mena"},de:{"∆":"delta","∞":"unendlich","♥":"Liebe","&":"und","|":"oder","<":"kleiner als",">":"groesser als","∑":"Summe von","¤":"Waehrung"},dv:{"∆":"delta","∞":"kolunulaa","♥":"loabi","&":"aai","|":"noonee","<":"ah vure kuda",">":"ah vure bodu","∑":"jumula","¤":"faisaa"},en:{"∆":"delta","∞":"infinity","♥":"love","&":"and","|":"or","<":"less than",">":"greater than","∑":"sum","¤":"currency"},es:{"∆":"delta","∞":"infinito","♥":"amor","&":"y","|":"u","<":"menos que",">":"mas que","∑":"suma de los","¤":"moneda"},fa:{"∆":"delta","∞":"bi-nahayat","♥":"eshgh","&":"va","|":"ya","<":"kamtar-az",">":"bishtar-az","∑":"majmooe","¤":"vahed"},fi:{"∆":"delta","∞":"aarettomyys","♥":"rakkaus","&":"ja","|":"tai","<":"pienempi kuin",">":"suurempi kuin","∑":"summa","¤":"valuutta"},fr:{"∆":"delta","∞":"infiniment","♥":"Amour","&":"et","|":"ou","<":"moins que",">":"superieure a","∑":"somme des","¤":"monnaie"},ge:{"∆":"delta","∞":"usasruloba","♥":"siqvaruli","&":"da","|":"an","<":"naklebi",">":"meti","∑":"jami","¤":"valuta"},gr:{},hu:{"∆":"delta","∞":"vegtelen","♥":"szerelem","&":"es","|":"vagy","<":"kisebb mint",">":"nagyobb mint","∑":"szumma","¤":"penznem"},it:{"∆":"delta","∞":"infinito","♥":"amore","&":"e","|":"o","<":"minore di",">":"maggiore di","∑":"somma","¤":"moneta"},lt:{"∆":"delta","∞":"begalybe","♥":"meile","&":"ir","|":"ar","<":"maziau nei",">":"daugiau nei","∑":"suma","¤":"valiuta"},lv:{"∆":"delta","∞":"bezgaliba","♥":"milestiba","&":"un","|":"vai","<":"mazak neka",">":"lielaks neka","∑":"summa","¤":"valuta"},my:{"∆":"kwahkhyaet","∞":"asaonasme","♥":"akhyait","&":"nhin","|":"tho","<":"ngethaw",">":"kyithaw","∑":"paungld","¤":"ngwekye"},mk:{},nl:{"∆":"delta","∞":"oneindig","♥":"liefde","&":"en","|":"of","<":"kleiner dan",">":"groter dan","∑":"som","¤":"valuta"},pl:{"∆":"delta","∞":"nieskonczonosc","♥":"milosc","&":"i","|":"lub","<":"mniejsze niz",">":"wieksze niz","∑":"suma","¤":"waluta"},pt:{"∆":"delta","∞":"infinito","♥":"amor","&":"e","|":"ou","<":"menor que",">":"maior que","∑":"soma","¤":"moeda"},ro:{"∆":"delta","∞":"infinit","♥":"dragoste","&":"si","|":"sau","<":"mai mic ca",">":"mai mare ca","∑":"suma","¤":"valuta"},ru:{"∆":"delta","∞":"beskonechno","♥":"lubov","&":"i","|":"ili","<":"menshe",">":"bolshe","∑":"summa","¤":"valjuta"},sk:{"∆":"delta","∞":"nekonecno","♥":"laska","&":"a","|":"alebo","<":"menej ako",">":"viac ako","∑":"sucet","¤":"mena"},sr:{},tr:{"∆":"delta","∞":"sonsuzluk","♥":"ask","&":"ve","|":"veya","<":"kucuktur",">":"buyuktur","∑":"toplam","¤":"para birimi"},uk:{"∆":"delta","∞":"bezkinechnist","♥":"lubov","&":"i","|":"abo","<":"menshe",">":"bilshe","∑":"suma","¤":"valjuta"},vn:{"∆":"delta","∞":"vo cuc","♥":"yeu","&":"va","|":"hoac","<":"nho hon",">":"lon hon","∑":"tong","¤":"tien te"}},u=[";","?",":","@","&","=","+","$",",","/"].join(""),a=[";","?",":","@","&","=","+","$",","].join(""),c=[".","!","~","*","'","(",")"].join(""),f=function(y,g){var x="-",D="",b="",P=!0,U={},H,W,M,I,R,F,z,Q,Ee,Z,L,X,ue,Ue,xe="";if(typeof y!="string")return"";if(typeof g=="string"&&(x=g),z=l.en,Q=i.en,typeof g=="object"){H=g.maintainCase||!1,U=g.custom&&typeof g.custom=="object"?g.custom:U,M=+g.truncate>1&&g.truncate||!1,I=g.uric||!1,R=g.uricNoSlash||!1,F=g.mark||!1,P=!(g.symbols===!1||g.lang===!1),x=g.separator||x,I&&(xe+=u),R&&(xe+=a),F&&(xe+=c),z=g.lang&&l[g.lang]&&P?l[g.lang]:P?l.en:{},Q=g.lang&&i[g.lang]?i[g.lang]:g.lang===!1||g.lang===!0?{}:i.en,g.titleCase&&typeof g.titleCase.length=="number"&&Array.prototype.toString.call(g.titleCase)?(g.titleCase.forEach(function(he){U[he+""]=he+""}),W=!0):W=!!g.titleCase,g.custom&&typeof g.custom.length=="number"&&Array.prototype.toString.call(g.custom)&&g.custom.forEach(function(he){U[he+""]=he+""}),Object.keys(U).forEach(function(he){var Bt;he.length>1?Bt=new RegExp("\\b"+d(he)+"\\b","gi"):Bt=new RegExp(d(he),"gi"),y=y.replace(Bt,U[he])});for(L in U)xe+=L}for(xe+=x,xe=d(xe),y=y.replace(/(^\s+|\s+$)/g,""),ue=!1,Ue=!1,Z=0,X=y.length;Z<X;Z++)L=y[Z],_(L,U)?ue=!1:Q[L]?(L=ue&&Q[L].match(/[A-Za-z0-9]/)?" "+Q[L]:Q[L],ue=!1):L in o?(Z+1<X&&s.indexOf(y[Z+1])>=0?(b+=L,L=""):Ue===!0?(L=r[b]+o[L],b=""):L=ue&&o[L].match(/[A-Za-z0-9]/)?" "+o[L]:o[L],ue=!1,Ue=!1):L in r?(b+=L,L="",Z===X-1&&(L=r[b]),Ue=!0):z[L]&&!(I&&u.indexOf(L)!==-1)&&!(R&&a.indexOf(L)!==-1)?(L=ue||D.substr(-1).match(/[A-Za-z0-9]/)?x+z[L]:z[L],L+=y[Z+1]!==void 0&&y[Z+1].match(/[A-Za-z0-9]/)?x:"",ue=!0):(Ue===!0?(L=r[b]+L,b="",Ue=!1):ue&&(/[A-Za-z0-9]/.test(L)||D.substr(-1).match(/A-Za-z0-9]/))&&(L=" "+L),ue=!1),D+=L.replace(new RegExp("[^\\w\\s"+xe+"_-]","g"),x);return W&&(D=D.replace(/(\w)(\S*)/g,function(he,Bt,vs){var ro=Bt.toUpperCase()+(vs!==null?vs:"");return Object.keys(U).indexOf(ro.toLowerCase())<0?ro:ro.toLowerCase()})),D=D.replace(/\s+/g,x).replace(new RegExp("\\"+x+"+","g"),x).replace(new RegExp("(^\\"+x+"+|\\"+x+"+$)","g"),""),M&&D.length>M&&(Ee=D.charAt(M)===x,D=D.slice(0,M),Ee||(D=D.slice(0,D.lastIndexOf(x)))),!H&&!W&&(D=D.toLowerCase()),D},h=function(y){return function(x){return f(x,y)}},d=function(y){return y.replace(/[-\\^$*+?.()|[\]{}\/]/g,"\\$&")},_=function(v,y){for(var g in y)if(y[g]===v)return!0};if(typeof t<"u"&&t.exports)t.exports=f,t.exports.createSlug=h;else if(typeof define<"u"&&define.amd)define([],function(){return f});else try{if(n.getSlug||n.createSlug)throw"speakingurl: globals exists /(getSlug|createSlug)/";n.getSlug=f,n.createSlug=h}catch{}}(e)}}),Wp=Fl({"../../node_modules/.pnpm/speakingurl@14.0.1/node_modules/speakingurl/index.js"(e,t){w(),t.exports=Kp()}});w(),w(),w();function Gp(e){if(O.__VUE_DEVTOOLS_PLUGIN_API_AVAILABLE__){e();return}Object.defineProperty(O,"__VUE_DEVTOOLS_PLUGIN_API_AVAILABLE__",{set(t){t&&e()},configurable:!0})}w(),w(),w(),w(),w();function Yp(e){var t;const n=e.name||e._componentTag||e.__VUE_DEVTOOLS_COMPONENT_GUSSED_NAME__||e.__name;return n==="index"&&((t=e.__file)!=null&&t.endsWith("index.vue"))?"":n}function qp(e){const t=e.__file;if(t)return Vl(Pp(t,".vue"))}function Bl(e,t){return e.type.__VUE_DEVTOOLS_COMPONENT_GUSSED_NAME__=t,t}function qe(e){if(e.__VUE_DEVTOOLS_NEXT_APP_RECORD__)return e.__VUE_DEVTOOLS_NEXT_APP_RECORD__;if(e.root)return e.appContext.app.__VUE_DEVTOOLS_NEXT_APP_RECORD__}async function ur(e){const{app:t,uid:n,instance:o}=e;try{if(o.__VUE_DEVTOOLS_NEXT_UID__)return o.__VUE_DEVTOOLS_NEXT_UID__;const s=await qe(t);if(!s)return null;const r=s.rootInstance===o;return`${s.id}:${r?"root":n}`}catch{}}function ar(e){var t,n;const o=(t=e.subTree)==null?void 0:t.type,s=qe(e);return s?((n=s==null?void 0:s.types)==null?void 0:n.Fragment)===o:!1}function cr(e){return e._isBeingDestroyed||e.isUnmounted}function rt(e){var t,n,o;const s=Yp((e==null?void 0:e.type)||{});if(s)return s;if((e==null?void 0:e.root)===e)return"Root";for(const i in(n=(t=e.parent)==null?void 0:t.type)==null?void 0:n.components)if(e.parent.type.components[i]===(e==null?void 0:e.type))return Bl(e,i);for(const i in(o=e.appContext)==null?void 0:o.components)if(e.appContext.components[i]===(e==null?void 0:e.type))return Bl(e,i);const r=qp((e==null?void 0:e.type)||{});return r||"Anonymous Component"}function fr(e){var t,n,o;const s=(o=(n=(t=e==null?void 0:e.appContext)==null?void 0:t.app)==null?void 0:n.__VUE_DEVTOOLS_NEXT_APP_RECORD_ID__)!=null?o:0,r=e===(e==null?void 0:e.root)?"root":e.uid;return`${s}:${r}`}function Xp(e){return e==null?"":typeof e=="number"?e:typeof e=="string"?`'${e}'`:Array.isArray(e)?"Array":"Object"}function Rt(e){try{return e()}catch(t){return t}}function hn(e,t){return t=t||`${e.id}:root`,e.instanceMap.get(t)||e.instanceMap.get(":root")}function dr(e,t,n=!1){return n||typeof e=="object"&&e!==null?t in e:!1}function Zp(){const e={top:0,bottom:0,left:0,right:0,get width(){return e.right-e.left},get height(){return e.bottom-e.top}};return e}var zo;function Jp(e){return zo||(zo=document.createRange()),zo.selectNode(e),zo.getBoundingClientRect()}function Qp(e){const t=Zp();if(!e.children)return t;for(let n=0,o=e.children.length;n<o;n++){const s=e.children[n];let r;if(s.component)r=qt(s.component);else if(s.el){const i=s.el;i.nodeType===1||i.getBoundingClientRect?r=i.getBoundingClientRect():i.nodeType===3&&i.data.trim()&&(r=Jp(i))}r&&eh(t,r)}return t}function eh(e,t){return(!e.top||t.top<e.top)&&(e.top=t.top),(!e.bottom||t.bottom>e.bottom)&&(e.bottom=t.bottom),(!e.left||t.left<e.left)&&(e.left=t.left),(!e.right||t.right>e.right)&&(e.right=t.right),e}var Hl={top:0,left:0,right:0,bottom:0,width:0,height:0};function qt(e){const t=e.subTree.el;return typeof window>"u"?Hl:ar(e)?Qp(e.subTree):(t==null?void 0:t.nodeType)===1?t==null?void 0:t.getBoundingClientRect():e.subTree.component?qt(e.subTree.component):Hl}w();function zn(e){return ar(e)?th(e.subTree):e.subTree?[e.subTree.el]:[]}function th(e){if(!e.children)return[];const t=[];return e.children.forEach(n=>{n.component?t.push(...zn(n.component)):n!=null&&n.el&&t.push(n.el)}),t}var zl="__vue-devtools-component-inspector__",jl="__vue-devtools-component-inspector__card__",Kl="__vue-devtools-component-inspector__name__",Wl="__vue-devtools-component-inspector__indicator__",Gl={display:"block",zIndex:2147483640,position:"fixed",backgroundColor:"#42b88325",border:"1px solid #42b88350",borderRadius:"5px",transition:"all 0.1s ease-in",pointerEvents:"none"},nh={fontFamily:"Arial, Helvetica, sans-serif",padding:"5px 8px",borderRadius:"4px",textAlign:"left",position:"absolute",left:0,color:"#e9e9e9",fontSize:"14px",fontWeight:600,lineHeight:"24px",backgroundColor:"#42b883",boxShadow:"0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px -1px rgba(0, 0, 0, 0.1)"},oh={display:"inline-block",fontWeight:400,fontStyle:"normal",fontSize:"12px",opacity:.7};function _n(){return document.getElementById(zl)}function sh(){return document.getElementById(jl)}function rh(){return document.getElementById(Wl)}function ih(){return document.getElementById(Kl)}function pr(e){return{left:`${Math.round(e.left*100)/100}px`,top:`${Math.round(e.top*100)/100}px`,width:`${Math.round(e.width*100)/100}px`,height:`${Math.round(e.height*100)/100}px`}}function hr(e){var t;const n=document.createElement("div");n.id=(t=e.elementId)!=null?t:zl,Object.assign(n.style,{...Gl,...pr(e.bounds),...e.style});const o=document.createElement("span");o.id=jl,Object.assign(o.style,{...nh,top:e.bounds.top<35?0:"-35px"});const s=document.createElement("span");s.id=Kl,s.innerHTML=`&lt;${e.name}&gt;&nbsp;&nbsp;`;const r=document.createElement("i");return r.id=Wl,r.innerHTML=`${Math.round(e.bounds.width*100)/100} x ${Math.round(e.bounds.height*100)/100}`,Object.assign(r.style,oh),o.appendChild(s),o.appendChild(r),n.appendChild(o),document.body.appendChild(n),n}function _r(e){const t=_n(),n=sh(),o=ih(),s=rh();t&&(Object.assign(t.style,{...Gl,...pr(e.bounds)}),Object.assign(n.style,{top:e.bounds.top<35?0:"-35px"}),o.innerHTML=`&lt;${e.name}&gt;&nbsp;&nbsp;`,s.innerHTML=`${Math.round(e.bounds.width*100)/100} x ${Math.round(e.bounds.height*100)/100}`)}function lh(e){const t=qt(e);if(!t.width&&!t.height)return;const n=rt(e);_n()?_r({bounds:t,name:n}):hr({bounds:t,name:n})}function Yl(){const e=_n();e&&(e.style.display="none")}var mr=null;function gr(e){const t=e.target;if(t){const n=t.__vueParentComponent;if(n&&(mr=n,n.vnode.el)){const s=qt(n),r=rt(n);_n()?_r({bounds:s,name:r}):hr({bounds:s,name:r})}}}function uh(e,t){if(e.preventDefault(),e.stopPropagation(),mr){const n=fr(mr);t(n)}}var jo=null;function ah(){Yl(),window.removeEventListener("mouseover",gr),window.removeEventListener("click",jo,!0),jo=null}function ch(){return window.addEventListener("mouseover",gr),new Promise(e=>{function t(n){n.preventDefault(),n.stopPropagation(),uh(n,o=>{window.removeEventListener("click",t,!0),jo=null,window.removeEventListener("mouseover",gr);const s=_n();s&&(s.style.display="none"),e(JSON.stringify({id:o}))})}jo=t,window.addEventListener("click",t,!0)})}function fh(e){const t=hn(se.value,e.id);if(t){const[n]=zn(t);if(typeof n.scrollIntoView=="function")n.scrollIntoView({behavior:"smooth"});else{const o=qt(t),s=document.createElement("div"),r={...pr(o),position:"absolute"};Object.assign(s.style,r),document.body.appendChild(s),s.scrollIntoView({behavior:"smooth"}),setTimeout(()=>{document.body.removeChild(s)},2e3)}setTimeout(()=>{const o=qt(t);if(o.width||o.height){const s=rt(t),r=_n();r?_r({...e,name:s,bounds:o}):hr({...e,name:s,bounds:o}),setTimeout(()=>{r&&(r.style.display="none")},1500)}},1200)}}w();var ql,Xl;(Xl=(ql=O).__VUE_DEVTOOLS_COMPONENT_INSPECTOR_ENABLED__)!=null||(ql.__VUE_DEVTOOLS_COMPONENT_INSPECTOR_ENABLED__=!0);function dh(e){let t=0;const n=setInterval(()=>{O.__VUE_INSPECTOR__&&(clearInterval(n),t+=30,e()),t>=5e3&&clearInterval(n)},30)}function ph(){const e=O.__VUE_INSPECTOR__,t=e.openInEditor;e.openInEditor=async(...n)=>{e.disable(),t(...n)}}function hh(){return new Promise(e=>{function t(){ph(),e(O.__VUE_INSPECTOR__)}O.__VUE_INSPECTOR__?t():dh(()=>{t()})})}w(),w();function _h(e){return!!(e&&e.__v_isReadonly)}function Zl(e){return _h(e)?Zl(e.__v_raw):!!(e&&e.__v_isReactive)}function vr(e){return!!(e&&e.__v_isRef===!0)}function jn(e){const t=e&&e.__v_raw;return t?jn(t):e}var Jl=class{constructor(){this.refEditor=new mh}set(e,t,n,o){const s=Array.isArray(t)?t:t.split(".");for(;s.length>1;){const l=s.shift();e instanceof Map?e=e.get(l):e instanceof Set?e=Array.from(e.values())[l]:e=e[l],this.refEditor.isRef(e)&&(e=this.refEditor.get(e))}const r=s[0],i=this.refEditor.get(e)[r];o?o(e,r,n):this.refEditor.isRef(i)?this.refEditor.set(i,n):e[r]=n}get(e,t){const n=Array.isArray(t)?t:t.split(".");for(let o=0;o<n.length;o++)if(e instanceof Map?e=e.get(n[o]):e=e[n[o]],this.refEditor.isRef(e)&&(e=this.refEditor.get(e)),!e)return;return e}has(e,t,n=!1){if(typeof e>"u")return!1;const o=Array.isArray(t)?t.slice():t.split("."),s=n?2:1;for(;e&&o.length>s;){const r=o.shift();e=e[r],this.refEditor.isRef(e)&&(e=this.refEditor.get(e))}return e!=null&&Object.prototype.hasOwnProperty.call(e,o[0])}createDefaultSetCallback(e){return(t,n,o)=>{if((e.remove||e.newKey)&&(Array.isArray(t)?t.splice(n,1):jn(t)instanceof Map?t.delete(n):jn(t)instanceof Set?t.delete(Array.from(t.values())[n]):Reflect.deleteProperty(t,n)),!e.remove){const s=t[e.newKey||n];this.refEditor.isRef(s)?this.refEditor.set(s,o):jn(t)instanceof Map?t.set(e.newKey||n,o):jn(t)instanceof Set?t.add(o):t[e.newKey||n]=o}}}},mh=class{set(e,t){if(vr(e))e.value=t;else{if(e instanceof Set&&Array.isArray(t)){e.clear(),t.forEach(s=>e.add(s));return}const n=Object.keys(t);if(e instanceof Map){const s=new Set(e.keys());n.forEach(r=>{e.set(r,Reflect.get(t,r)),s.delete(r)}),s.forEach(r=>e.delete(r));return}const o=new Set(Object.keys(e));n.forEach(s=>{Reflect.set(e,s,Reflect.get(t,s)),o.delete(s)}),o.forEach(s=>Reflect.deleteProperty(e,s))}}get(e){return vr(e)?e.value:e}isRef(e){return vr(e)||Zl(e)}};async function gh(e,t){const{path:n,nodeId:o,state:s,type:r}=e,i=hn(se.value,o);if(!i)return;const l=n.slice();let u;Object.keys(i.props).includes(n[0])?u=i.props:i.devtoolsRawSetupState&&Object.keys(i.devtoolsRawSetupState).includes(n[0])?u=i.devtoolsRawSetupState:i.data&&Object.keys(i.data).includes(n[0])?u=i.data:u=i.proxy,u&&l&&(s.type,t.set(u,l,s.value,t.createDefaultSetCallback(s)))}var vh=new Jl;async function yh(e){gh(e,vh)}w(),w(),w();var Eh="__VUE_DEVTOOLS_KIT_TIMELINE_LAYERS_STATE__";function bh(){if(!Ho||typeof localStorage>"u"||localStorage===null)return{recordingState:!1,mouseEventEnabled:!1,keyboardEventEnabled:!1,componentEventEnabled:!1,performanceEventEnabled:!1,selected:""};const e=localStorage.getItem(Eh);return e?JSON.parse(e):{recordingState:!1,mouseEventEnabled:!1,keyboardEventEnabled:!1,componentEventEnabled:!1,performanceEventEnabled:!1,selected:""}}w(),w(),w();var Ql,eu;(eu=(Ql=O).__VUE_DEVTOOLS_KIT_TIMELINE_LAYERS)!=null||(Ql.__VUE_DEVTOOLS_KIT_TIMELINE_LAYERS=[]);var wh=new Proxy(O.__VUE_DEVTOOLS_KIT_TIMELINE_LAYERS,{get(e,t,n){return Reflect.get(e,t,n)}});function Sh(e,t){q.timelineLayersState[t.id]=!1,wh.push({...e,descriptorId:t.id,appRecord:qe(t.app)})}var tu,nu;(nu=(tu=O).__VUE_DEVTOOLS_KIT_INSPECTOR__)!=null||(tu.__VUE_DEVTOOLS_KIT_INSPECTOR__=[]);var yr=new Proxy(O.__VUE_DEVTOOLS_KIT_INSPECTOR__,{get(e,t,n){return Reflect.get(e,t,n)}}),ou=It(()=>{Xe.hooks.callHook("sendInspectorToClient",su())});function xh(e,t){var n,o;yr.push({options:e,descriptor:t,treeFilterPlaceholder:(n=e.treeFilterPlaceholder)!=null?n:"Search tree...",stateFilterPlaceholder:(o=e.stateFilterPlaceholder)!=null?o:"Search state...",treeFilter:"",selectedNodeId:"",appRecord:qe(t.app)}),ou()}function su(){return yr.filter(e=>e.descriptor.app===se.value.app).filter(e=>e.descriptor.id!=="components").map(e=>{var t;const n=e.descriptor,o=e.options;return{id:o.id,label:o.label,logo:n.logo,icon:`custom-ic-baseline-${(t=o==null?void 0:o.icon)==null?void 0:t.replace(/_/g,"-")}`,packageName:n.packageName,homepage:n.homepage,pluginId:n.id}})}function Ko(e,t){return yr.find(n=>n.options.id===e&&(t?n.descriptor.app===t:!0))}function Th(){const e=Ml();e.hook("addInspector",({inspector:o,plugin:s})=>{xh(o,s.descriptor)});const t=It(async({inspectorId:o,plugin:s})=>{var r;if(!o||!((r=s==null?void 0:s.descriptor)!=null&&r.app)||q.highPerfModeEnabled)return;const i=Ko(o,s.descriptor.app),l={app:s.descriptor.app,inspectorId:o,filter:(i==null?void 0:i.treeFilter)||"",rootNodes:[]};await new Promise(u=>{e.callHookWith(async a=>{await Promise.all(a.map(c=>c(l))),u()},"getInspectorTree")}),e.callHookWith(async u=>{await Promise.all(u.map(a=>a({inspectorId:o,rootNodes:l.rootNodes})))},"sendInspectorTreeToClient")},120);e.hook("sendInspectorTree",t);const n=It(async({inspectorId:o,plugin:s})=>{var r;if(!o||!((r=s==null?void 0:s.descriptor)!=null&&r.app)||q.highPerfModeEnabled)return;const i=Ko(o,s.descriptor.app),l={app:s.descriptor.app,inspectorId:o,nodeId:(i==null?void 0:i.selectedNodeId)||"",state:null},u={currentTab:`custom-inspector:${o}`};l.nodeId&&await new Promise(a=>{e.callHookWith(async c=>{await Promise.all(c.map(f=>f(l,u))),a()},"getInspectorState")}),e.callHookWith(async a=>{await Promise.all(a.map(c=>c({inspectorId:o,nodeId:l.nodeId,state:l.state})))},"sendInspectorStateToClient")},120);return e.hook("sendInspectorState",n),e.hook("customInspectorSelectNode",({inspectorId:o,nodeId:s,plugin:r})=>{const i=Ko(o,r.descriptor.app);i&&(i.selectedNodeId=s)}),e.hook("timelineLayerAdded",({options:o,plugin:s})=>{Sh(o,s.descriptor)}),e.hook("timelineEventAdded",({options:o,plugin:s})=>{var r;const i=["performance","component-event","keyboard","mouse"];q.highPerfModeEnabled||!((r=q.timelineLayersState)!=null&&r[s.descriptor.id])&&!i.includes(o.layerId)||e.callHookWith(async l=>{await Promise.all(l.map(u=>u(o)))},"sendTimelineEventToClient")}),e.hook("getComponentInstances",async({app:o})=>{const s=o.__VUE_DEVTOOLS_NEXT_APP_RECORD__;if(!s)return null;const r=s.id.toString();return[...s.instanceMap].filter(([l])=>l.split(":")[0]===r).map(([,l])=>l)}),e.hook("getComponentBounds",async({instance:o})=>qt(o)),e.hook("getComponentName",({instance:o})=>rt(o)),e.hook("componentHighlight",({uid:o})=>{const s=se.value.instanceMap.get(o);s&&lh(s)}),e.hook("componentUnhighlight",()=>{Yl()}),e}var ru,iu;(iu=(ru=O).__VUE_DEVTOOLS_KIT_APP_RECORDS__)!=null||(ru.__VUE_DEVTOOLS_KIT_APP_RECORDS__=[]);var lu,uu;(uu=(lu=O).__VUE_DEVTOOLS_KIT_ACTIVE_APP_RECORD__)!=null||(lu.__VUE_DEVTOOLS_KIT_ACTIVE_APP_RECORD__={});var au,cu;(cu=(au=O).__VUE_DEVTOOLS_KIT_ACTIVE_APP_RECORD_ID__)!=null||(au.__VUE_DEVTOOLS_KIT_ACTIVE_APP_RECORD_ID__="");var fu,du;(du=(fu=O).__VUE_DEVTOOLS_KIT_CUSTOM_TABS__)!=null||(fu.__VUE_DEVTOOLS_KIT_CUSTOM_TABS__=[]);var pu,hu;(hu=(pu=O).__VUE_DEVTOOLS_KIT_CUSTOM_COMMANDS__)!=null||(pu.__VUE_DEVTOOLS_KIT_CUSTOM_COMMANDS__=[]);var it="__VUE_DEVTOOLS_KIT_GLOBAL_STATE__";function Oh(){return{connected:!1,clientConnected:!1,vitePluginDetected:!0,appRecords:[],activeAppRecordId:"",tabs:[],commands:[],highPerfModeEnabled:!0,devtoolsClientDetected:{},perfUniqueGroupId:0,timelineLayersState:bh()}}var _u,mu;(mu=(_u=O)[it])!=null||(_u[it]=Oh());var Ah=It(e=>{Xe.hooks.callHook("devtoolsStateUpdated",{state:e})}),Ch=It((e,t)=>{Xe.hooks.callHook("devtoolsConnectedUpdated",{state:e,oldState:t})}),kt=new Proxy(O.__VUE_DEVTOOLS_KIT_APP_RECORDS__,{get(e,t,n){return t==="value"?O.__VUE_DEVTOOLS_KIT_APP_RECORDS__:O.__VUE_DEVTOOLS_KIT_APP_RECORDS__[t]}}),Dh=e=>{O.__VUE_DEVTOOLS_KIT_APP_RECORDS__=[...O.__VUE_DEVTOOLS_KIT_APP_RECORDS__,e]},Ph=e=>{O.__VUE_DEVTOOLS_KIT_APP_RECORDS__=kt.value.filter(t=>t.app!==e)},se=new Proxy(O.__VUE_DEVTOOLS_KIT_ACTIVE_APP_RECORD__,{get(e,t,n){return t==="value"?O.__VUE_DEVTOOLS_KIT_ACTIVE_APP_RECORD__:t==="id"?O.__VUE_DEVTOOLS_KIT_ACTIVE_APP_RECORD_ID__:O.__VUE_DEVTOOLS_KIT_ACTIVE_APP_RECORD__[t]}});function Er(){Ah({...O[it],appRecords:kt.value,activeAppRecordId:se.id,tabs:O.__VUE_DEVTOOLS_KIT_CUSTOM_TABS__,commands:O.__VUE_DEVTOOLS_KIT_CUSTOM_COMMANDS__})}function br(e){O.__VUE_DEVTOOLS_KIT_ACTIVE_APP_RECORD__=e,Er()}function gu(e){O.__VUE_DEVTOOLS_KIT_ACTIVE_APP_RECORD_ID__=e,Er()}var q=new Proxy(O[it],{get(e,t){return t==="appRecords"?kt:t==="activeAppRecordId"?se.id:t==="tabs"?O.__VUE_DEVTOOLS_KIT_CUSTOM_TABS__:t==="commands"?O.__VUE_DEVTOOLS_KIT_CUSTOM_COMMANDS__:O[it][t]},deleteProperty(e,t){return delete e[t],!0},set(e,t,n){return{...O[it]},e[t]=n,O[it][t]=n,!0}});function wr(e){const t={...O[it],appRecords:kt.value,activeAppRecordId:se.id};(t.connected!==e.connected&&e.connected||t.clientConnected!==e.clientConnected&&e.clientConnected)&&Ch(O[it],t),Object.assign(O[it],e),Er()}function Ih(e){return new Promise(t=>{q.connected&&(e(),t()),Xe.hooks.hook("devtoolsConnectedUpdated",({state:n})=>{n.connected&&(e(),t())})})}function Rh(e={}){var t,n,o;const{file:s,host:r,baseUrl:i=window.location.origin,line:l=0,column:u=0}=e;if(s){if(r==="chrome-extension"){const a=s.replace(/\\/g,"\\\\"),c=(n=(t=window.VUE_DEVTOOLS_CONFIG)==null?void 0:t.openInEditorHost)!=null?n:"/";fetch(`${c}__open-in-editor?file=${encodeURI(s)}`).then(f=>{if(!f.ok){const h=`Opening component ${a} failed`;console.log(`%c${h}`,"color:red")}})}else if(q.vitePluginDetected){const a=(o=O.__VUE_DEVTOOLS_OPEN_IN_EDITOR_BASE_URL__)!=null?o:i;O.__VUE_INSPECTOR__.openInEditor(a,s,l,u)}}}w(),w(),w(),w(),w();var vu,yu;(yu=(vu=O).__VUE_DEVTOOLS_KIT_PLUGIN_BUFFER__)!=null||(vu.__VUE_DEVTOOLS_KIT_PLUGIN_BUFFER__=[]);var Kn=new Proxy(O.__VUE_DEVTOOLS_KIT_PLUGIN_BUFFER__,{get(e,t,n){return Reflect.get(e,t,n)}});function kh(e,t){Kn.push([e,t])}function Sr(e){const t={};return Object.keys(e).forEach(n=>{t[n]=e[n].defaultValue}),t}function xr(e){return`__VUE_DEVTOOLS_NEXT_PLUGIN_SETTINGS__${e}__`}function Nh(e){var t,n,o;const s=(n=(t=Kn.find(r=>{var i;return r[0].id===e&&!!((i=r[0])!=null&&i.settings)}))==null?void 0:t[0])!=null?n:null;return(o=s==null?void 0:s.settings)!=null?o:null}function Eu(e,t){var n,o,s;const r=xr(e);if(r){const i=localStorage.getItem(r);if(i)return JSON.parse(i)}if(e){const i=(o=(n=Kn.find(l=>l[0].id===e))==null?void 0:n[0])!=null?o:null;return Sr((s=i==null?void 0:i.settings)!=null?s:{})}return Sr(t)}function bu(e,t){const n=xr(e);localStorage.getItem(n)||localStorage.setItem(n,JSON.stringify(Sr(t)))}function Vh(e,t,n){const o=xr(e),s=localStorage.getItem(o),r=JSON.parse(s||"{}"),i={...r,[t]:n};localStorage.setItem(o,JSON.stringify(i)),Xe.hooks.callHookWith(l=>{l.forEach(u=>u({pluginId:e,key:t,oldValue:r[t],newValue:n,settings:i}))},"setPluginSettings")}w(),w(),w(),w(),w(),w(),w(),w(),w(),w(),w();var wu,Su,pe=(Su=(wu=O).__VUE_DEVTOOLS_HOOK)!=null?Su:wu.__VUE_DEVTOOLS_HOOK=Ml(),Lh={vueAppInit(e){pe.hook("app:init",e)},vueAppUnmount(e){pe.hook("app:unmount",e)},vueAppConnected(e){pe.hook("app:connected",e)},componentAdded(e){return pe.hook("component:added",e)},componentEmit(e){return pe.hook("component:emit",e)},componentUpdated(e){return pe.hook("component:updated",e)},componentRemoved(e){return pe.hook("component:removed",e)},setupDevtoolsPlugin(e){pe.hook("devtools-plugin:setup",e)},perfStart(e){return pe.hook("perf:start",e)},perfEnd(e){return pe.hook("perf:end",e)}};function $h(){return{id:"vue-devtools-next",devtoolsVersion:"7.0",enabled:!1,appRecords:[],apps:[],events:new Map,on(e,t){var n;return this.events.has(e)||this.events.set(e,[]),(n=this.events.get(e))==null||n.push(t),()=>this.off(e,t)},once(e,t){const n=(...o)=>{this.off(e,n),t(...o)};return this.on(e,n),[e,n]},off(e,t){if(this.events.has(e)){const n=this.events.get(e),o=n.indexOf(t);o!==-1&&n.splice(o,1)}},emit(e,...t){this.events.has(e)&&this.events.get(e).forEach(n=>n(...t))}}}function Mh(e){e.on("app:init",(t,n,o)=>{var s,r,i;(i=(r=(s=t==null?void 0:t._instance)==null?void 0:s.type)==null?void 0:r.devtools)!=null&&i.hide||pe.callHook("app:init",t,n,o)}),e.on("app:unmount",t=>{pe.callHook("app:unmount",t)}),e.on("component:added",async(t,n,o,s)=>{var r,i,l;(l=(i=(r=t==null?void 0:t._instance)==null?void 0:r.type)==null?void 0:i.devtools)!=null&&l.hide||q.highPerfModeEnabled||!t||typeof n!="number"&&!n||!s||pe.callHook("component:added",t,n,o,s)}),e.on("component:updated",(t,n,o,s)=>{!t||typeof n!="number"&&!n||!s||q.highPerfModeEnabled||pe.callHook("component:updated",t,n,o,s)}),e.on("component:removed",async(t,n,o,s)=>{!t||typeof n!="number"&&!n||!s||q.highPerfModeEnabled||pe.callHook("component:removed",t,n,o,s)}),e.on("component:emit",async(t,n,o,s)=>{!t||!n||q.highPerfModeEnabled||pe.callHook("component:emit",t,n,o,s)}),e.on("perf:start",(t,n,o,s,r)=>{!t||q.highPerfModeEnabled||pe.callHook("perf:start",t,n,o,s,r)}),e.on("perf:end",(t,n,o,s,r)=>{!t||q.highPerfModeEnabled||pe.callHook("perf:end",t,n,o,s,r)}),e.on("devtools-plugin:setup",(t,n,o)=>{(o==null?void 0:o.target)!=="legacy"&&pe.callHook("devtools-plugin:setup",t,n)})}var ze={on:Lh,setupDevToolsPlugin(e,t){return pe.callHook("devtools-plugin:setup",e,t)}},Uh=class{constructor({plugin:e,ctx:t}){this.hooks=t.hooks,this.plugin=e}get on(){return{visitComponentTree:e=>{this.hooks.hook("visitComponentTree",e)},inspectComponent:e=>{this.hooks.hook("inspectComponent",e)},editComponentState:e=>{this.hooks.hook("editComponentState",e)},getInspectorTree:e=>{this.hooks.hook("getInspectorTree",e)},getInspectorState:e=>{this.hooks.hook("getInspectorState",e)},editInspectorState:e=>{this.hooks.hook("editInspectorState",e)},inspectTimelineEvent:e=>{this.hooks.hook("inspectTimelineEvent",e)},timelineCleared:e=>{this.hooks.hook("timelineCleared",e)},setPluginSettings:e=>{this.hooks.hook("setPluginSettings",e)}}}notifyComponentUpdate(e){var t;if(q.highPerfModeEnabled)return;const n=su().find(o=>o.packageName===this.plugin.descriptor.packageName);if(n!=null&&n.id){if(e){const o=[e.appContext.app,e.uid,(t=e.parent)==null?void 0:t.uid,e];pe.callHook("component:updated",...o)}else pe.callHook("component:updated");this.hooks.callHook("sendInspectorState",{inspectorId:n.id,plugin:this.plugin})}}addInspector(e){this.hooks.callHook("addInspector",{inspector:e,plugin:this.plugin}),this.plugin.descriptor.settings&&bu(e.id,this.plugin.descriptor.settings)}sendInspectorTree(e){q.highPerfModeEnabled||this.hooks.callHook("sendInspectorTree",{inspectorId:e,plugin:this.plugin})}sendInspectorState(e){q.highPerfModeEnabled||this.hooks.callHook("sendInspectorState",{inspectorId:e,plugin:this.plugin})}selectInspectorNode(e,t){this.hooks.callHook("customInspectorSelectNode",{inspectorId:e,nodeId:t,plugin:this.plugin})}visitComponentTree(e){return this.hooks.callHook("visitComponentTree",e)}now(){return q.highPerfModeEnabled?0:Date.now()}addTimelineLayer(e){this.hooks.callHook("timelineLayerAdded",{options:e,plugin:this.plugin})}addTimelineEvent(e){q.highPerfModeEnabled||this.hooks.callHook("timelineEventAdded",{options:e,plugin:this.plugin})}getSettings(e){return Eu(e??this.plugin.descriptor.id,this.plugin.descriptor.settings)}getComponentInstances(e){return this.hooks.callHook("getComponentInstances",{app:e})}getComponentBounds(e){return this.hooks.callHook("getComponentBounds",{instance:e})}getComponentName(e){return this.hooks.callHook("getComponentName",{instance:e})}highlightElement(e){const t=e.__VUE_DEVTOOLS_NEXT_UID__;return this.hooks.callHook("componentHighlight",{uid:t})}unhighlightElement(){return this.hooks.callHook("componentUnhighlight")}},Fh=Uh;w(),w(),w(),w();var Bh=new Set(["nextTick","defineComponent","defineAsyncComponent","defineCustomElement","ref","computed","reactive","readonly","watchEffect","watchPostEffect","watchSyncEffect","watch","isRef","unref","toRef","toRefs","isProxy","isReactive","isReadonly","shallowRef","triggerRef","customRef","shallowReactive","shallowReadonly","toRaw","markRaw","effectScope","getCurrentScope","onScopeDispose","onMounted","onUpdated","onUnmounted","onBeforeMount","onBeforeUpdate","onBeforeUnmount","onErrorCaptured","onRenderTracked","onRenderTriggered","onActivated","onDeactivated","onServerPrefetch","provide","inject","h","mergeProps","cloneVNode","isVNode","resolveComponent","resolveDirective","withDirectives","withModifiers"]),Hh=/^(?:function|class) (\w+)/,zh="__vue_devtool_undefined__",jh="__vue_devtool_infinity__",Kh="__vue_devtool_negative_infinity__",Wh="__vue_devtool_nan__";w(),w();function xu(e){return!!e.__v_isRef}function Gh(e){return xu(e)&&!!e.effect}function Yh(e){return!!e.__v_isReactive}function qh(e){return!!e.__v_isReadonly}var Xh={[zh]:"undefined",[Wh]:"NaN",[jh]:"Infinity",[Kh]:"-Infinity"};Object.entries(Xh).reduce((e,[t,n])=>(e[n]=t,e),{});function Tu(e){if(Array.isArray(e))return e.map(n=>Tu(n)).join(" or ");if(e==null)return"null";const t=e.toString().match(Hh);return typeof e=="function"&&t&&t[1]||"any"}function Zh(e){try{return{ref:xu(e),computed:Gh(e),reactive:Yh(e),readonly:qh(e)}}catch{return{ref:!1,computed:!1,reactive:!1,readonly:!1}}}function Jh(e){return e!=null&&e.__v_raw?e.__v_raw:e}function Wo(e,t,n){if(typeof t=="function"&&(t=t.options),!t)return e;const{mixins:o,extends:s}=t;s&&Wo(e,s),o&&o.forEach(r=>Wo(e,r));for(const r of["computed","inject"])Object.prototype.hasOwnProperty.call(t,r)&&(e[r]?Object.assign(e[r],t[r]):e[r]=t[r]);return e}function Qh(e){const t=e==null?void 0:e.type;if(!t)return{};const{mixins:n,extends:o}=t,s=e.appContext.mixins;if(!s.length&&!n&&!o)return t;const r={};return s.forEach(i=>Wo(r,i)),Wo(r,t),r}function e_(e){var t;const n=[],o=(t=e==null?void 0:e.type)==null?void 0:t.props;for(const s in e==null?void 0:e.props){const r=o?o[s]:null,i=Cp(s);n.push({type:"props",key:i,value:Rt(()=>e.props[s]),editable:!0,meta:r?{type:r.type?Tu(r.type):"any",required:!!r.required,...r.default?{default:r.default.toString()}:{}}:{type:"invalid"}})}return n}function t_(e){const t=e.type,n=t==null?void 0:t.props,o=t.vuex&&t.vuex.getters,s=t.computed,r={...e.data,...e.renderContext};return Object.keys(r).filter(i=>!(n&&i in n)&&!(o&&i in o)&&!(s&&i in s)).map(i=>({key:i,type:"data",value:Rt(()=>r[i]),editable:!0}))}function n_(e){const t=e.computed?"computed":e.ref?"ref":e.reactive?"reactive":null,n=t?`${t.charAt(0).toUpperCase()}${t.slice(1)}`:null;return{stateType:t,stateTypeName:n}}function o_(e){const t=e.devtoolsRawSetupState||{};return Object.keys(e.setupState).filter(n=>!Bh.has(n)&&n.split(/(?=[A-Z])/)[0]!=="use").map(n=>{var o,s,r,i;const l=Rt(()=>Jh(e.setupState[n])),u=l instanceof Error,a=t[n];let c,f=u||typeof l=="function"||dr(l,"render")&&typeof l.render=="function"||dr(l,"__asyncLoader")&&typeof l.__asyncLoader=="function"||typeof l=="object"&&l&&("setup"in l||"props"in l)||/^v[A-Z]/.test(n);if(a&&!u){const d=Zh(a),{stateType:_,stateTypeName:v}=n_(d),y=d.ref||d.computed||d.reactive,g=dr(a,"effect")?((s=(o=a.effect)==null?void 0:o.raw)==null?void 0:s.toString())||((i=(r=a.effect)==null?void 0:r.fn)==null?void 0:i.toString()):null;_&&(f=!1),c={..._?{stateType:_,stateTypeName:v}:{},...g?{raw:g}:{},editable:y&&!d.readonly}}return{key:n,value:l,type:f?"setup (other)":"setup",...c}})}function s_(e,t){const n=t,o=[],s=n.computed||{};for(const r in s){const i=s[r],l=typeof i=="function"&&i.vuex?"vuex bindings":"computed";o.push({type:l,key:r,value:Rt(()=>{var u;return(u=e==null?void 0:e.proxy)==null?void 0:u[r]}),editable:typeof i.set=="function"})}return o}function r_(e){return Object.keys(e.attrs).map(t=>({type:"attrs",key:t,value:Rt(()=>e.attrs[t])}))}function i_(e){return Reflect.ownKeys(e.provides).map(t=>({type:"provided",key:t.toString(),value:Rt(()=>e.provides[t])}))}function l_(e,t){if(!(t!=null&&t.inject))return[];let n=[],o;return Array.isArray(t.inject)?n=t.inject.map(s=>({key:s,originalKey:s})):n=Reflect.ownKeys(t.inject).map(s=>{const r=t.inject[s];let i;return typeof r=="string"||typeof r=="symbol"?i=r:(i=r.from,o=r.default),{key:s,originalKey:i}}),n.map(({key:s,originalKey:r})=>({type:"injected",key:r&&s!==r?`${r.toString()} ➞ ${s.toString()}`:s.toString(),value:Rt(()=>e.ctx.hasOwnProperty(s)?e.ctx[s]:e.provides.hasOwnProperty(r)?e.provides[r]:o)}))}function u_(e){return Object.keys(e.refs).map(t=>({type:"template refs",key:t,value:Rt(()=>e.refs[t])}))}function a_(e){var t,n;const o=e.type.emits,s=Array.isArray(o)?o:Object.keys(o??{}),r=Object.keys((n=(t=e==null?void 0:e.vnode)==null?void 0:t.props)!=null?n:{}),i=[];for(const l of r){const[u,...a]=l.split(/(?=[A-Z])/);if(u==="on"){const c=a.join("-").toLowerCase(),f=s.includes(c);i.push({type:"event listeners",key:c,value:{_custom:{displayText:f?"✅ Declared":"⚠️ Not declared",key:f?"✅ Declared":"⚠️ Not declared",value:f?"✅ Declared":"⚠️ Not declared",tooltipText:f?null:`The event <code>${c}</code> is not declared in the <code>emits</code> option. It will leak into the component's attributes (<code>$attrs</code>).`}}})}}return i}function c_(e){const t=Qh(e);return e_(e).concat(t_(e),o_(e),s_(e,t),r_(e),i_(e),l_(e,t),u_(e),a_(e))}function f_(e){var t;const n=hn(se.value,e.instanceId),o=fr(n),s=rt(n),r=(t=n==null?void 0:n.type)==null?void 0:t.__file,i=c_(n);return{id:o,name:s,file:r,state:i,instance:n}}w(),w();var d_=class{constructor(e){this.filter=e||""}isQualified(e){const t=rt(e);return Vl(t).toLowerCase().includes(this.filter)||Dp(t).toLowerCase().includes(this.filter)}};function p_(e){return new d_(e)}var h_=class{constructor(e){this.captureIds=new Map;const{filterText:t="",maxDepth:n,recursively:o,api:s}=e;this.componentFilter=p_(t),this.maxDepth=n,this.recursively=o,this.api=s}getComponentTree(e){return this.captureIds=new Map,this.findQualifiedChildren(e,0)}getComponentParents(e){this.captureIds=new Map;const t=[];this.captureId(e);let n=e;for(;n=n.parent;)this.captureId(n),t.push(n);return t}captureId(e){if(!e)return null;const t=e.__VUE_DEVTOOLS_NEXT_UID__!=null?e.__VUE_DEVTOOLS_NEXT_UID__:fr(e);return e.__VUE_DEVTOOLS_NEXT_UID__=t,this.captureIds.has(t)?null:(this.captureIds.set(t,void 0),this.mark(e),t)}async capture(e,t){var n;if(!e)return null;const o=this.captureId(e),s=rt(e),r=this.getInternalInstanceChildren(e.subTree).filter(f=>!cr(f)),i=this.getComponentParents(e)||[],l=!!e.isDeactivated||i.some(f=>f.isDeactivated),u={uid:e.uid,id:o,name:s,renderKey:Xp(e.vnode?e.vnode.key:null),inactive:l,children:[],isFragment:ar(e),tags:typeof e.type!="function"?[]:[{label:"functional",textColor:5592405,backgroundColor:15658734}],autoOpen:this.recursively,file:e.type.__file||""};if((t<this.maxDepth||e.type.__isKeepAlive||i.some(f=>f.type.__isKeepAlive))&&(u.children=await Promise.all(r.map(f=>this.capture(f,t+1)).filter(Boolean))),this.isKeepAlive(e)){const f=this.getKeepAliveCachedInstances(e),h=r.map(d=>d.__VUE_DEVTOOLS_NEXT_UID__);for(const d of f)if(!h.includes(d.__VUE_DEVTOOLS_NEXT_UID__)){const _=await this.capture({...d,isDeactivated:!0},t+1);_&&u.children.push(_)}}const c=zn(e)[0];if(c!=null&&c.parentElement){const f=e.parent,h=f?zn(f):[];let d=c;const _=[];do _.push(Array.from(d.parentElement.childNodes).indexOf(d)),d=d.parentElement;while(d.parentElement&&h.length&&!h.includes(d));u.domOrder=_.reverse()}else u.domOrder=[-1];return(n=e.suspense)!=null&&n.suspenseKey&&(u.tags.push({label:e.suspense.suspenseKey,backgroundColor:14979812,textColor:16777215}),this.mark(e,!0)),this.api.visitComponentTree({treeNode:u,componentInstance:e,app:e.appContext.app,filter:this.componentFilter.filter}),u}async findQualifiedChildren(e,t){var n;if(this.componentFilter.isQualified(e)&&!((n=e.type.devtools)!=null&&n.hide))return[await this.capture(e,t)];if(e.subTree){const o=this.isKeepAlive(e)?this.getKeepAliveCachedInstances(e):this.getInternalInstanceChildren(e.subTree);return this.findQualifiedChildrenFromList(o,t)}else return[]}async findQualifiedChildrenFromList(e,t){return e=e.filter(n=>{var o;return!cr(n)&&!((o=n.type.devtools)!=null&&o.hide)}),this.componentFilter.filter?Array.prototype.concat.apply([],await Promise.all(e.map(n=>this.findQualifiedChildren(n,t)))):Promise.all(e.map(n=>this.capture(n,t)))}getInternalInstanceChildren(e,t=null){const n=[];if(e)if(e.component)t?n.push({...e.component,suspense:t}):n.push(e.component);else if(e.suspense){const o=e.suspense.isInFallback?"suspense fallback":"suspense default";n.push(...this.getInternalInstanceChildren(e.suspense.activeBranch,{...e.suspense,suspenseKey:o}))}else Array.isArray(e.children)&&e.children.forEach(o=>{o.component?t?n.push({...o.component,suspense:t}):n.push(o.component):n.push(...this.getInternalInstanceChildren(o,t))});return n.filter(o=>{var s;return!cr(o)&&!((s=o.type.devtools)!=null&&s.hide)})}mark(e,t=!1){const n=qe(e).instanceMap;(t||!n.has(e.__VUE_DEVTOOLS_NEXT_UID__))&&(n.set(e.__VUE_DEVTOOLS_NEXT_UID__,e),se.value.instanceMap=n)}isKeepAlive(e){return e.type.__isKeepAlive&&e.__v_cache}getKeepAliveCachedInstances(e){return Array.from(e.__v_cache.values()).map(t=>t.component).filter(Boolean)}};w(),w();var Go=new Map,Tr="performance";async function __(e,t,n,o,s,r){const i=await qe(t);if(!i)return;const l=rt(o)||"Unknown Component",u=q.perfUniqueGroupId++,a=`${n}-${s}`;if(i.perfGroupIds.set(a,{groupId:u,time:r}),await e.addTimelineEvent({layerId:Tr,event:{time:Date.now(),data:{component:l,type:s,measure:"start"},title:l,subtitle:s,groupId:u}}),Go.has(a)){const{app:c,uid:f,instance:h,type:d,time:_}=Go.get(a);Go.delete(a),await Ou(e,c,f,h,d,_)}}function Ou(e,t,n,o,s,r){const i=qe(t);if(!i)return;const l=rt(o)||"Unknown Component",u=`${n}-${s}`,a=i.perfGroupIds.get(u);if(a){const c=a.groupId,f=a.time,h=r-f;e.addTimelineEvent({layerId:Tr,event:{time:Date.now(),data:{component:l,type:s,measure:"end",duration:{_custom:{type:"Duration",value:h,display:`${h} ms`}}},title:l,subtitle:s,groupId:c}})}else Go.set(u,{app:t,uid:n,instance:o,type:s,time:r})}var Au="component-event";function m_(e){Ho&&(e.addTimelineLayer({id:"mouse",label:"Mouse",color:10768815}),["mousedown","mouseup","click","dblclick"].forEach(t=>{!q.timelineLayersState.recordingState||!q.timelineLayersState.mouseEventEnabled||window.addEventListener(t,async n=>{await e.addTimelineEvent({layerId:"mouse",event:{time:Date.now(),data:{type:t,x:n.clientX,y:n.clientY},title:t}})},{capture:!0,passive:!0})}),e.addTimelineLayer({id:"keyboard",label:"Keyboard",color:8475055}),["keyup","keydown","keypress"].forEach(t=>{window.addEventListener(t,async n=>{!q.timelineLayersState.recordingState||!q.timelineLayersState.keyboardEventEnabled||await e.addTimelineEvent({layerId:"keyboard",event:{time:Date.now(),data:{type:t,key:n.key,ctrlKey:n.ctrlKey,shiftKey:n.shiftKey,altKey:n.altKey,metaKey:n.metaKey},title:n.key}})},{capture:!0,passive:!0})}),e.addTimelineLayer({id:Au,label:"Component events",color:5226637}),ze.on.componentEmit(async(t,n,o,s)=>{if(!q.timelineLayersState.recordingState||!q.timelineLayersState.componentEventEnabled)return;const r=await qe(t);if(!r)return;const i=`${r.id}:${n.uid}`,l=rt(n)||"Unknown Component";e.addTimelineEvent({layerId:Au,event:{time:Date.now(),data:{component:{_custom:{type:"component-definition",display:l}},event:o,params:s},title:o,subtitle:`by ${l}`,meta:{componentId:i}}})}),e.addTimelineLayer({id:"performance",label:Tr,color:4307050}),ze.on.perfStart((t,n,o,s,r)=>{!q.timelineLayersState.recordingState||!q.timelineLayersState.performanceEventEnabled||__(e,t,n,o,s,r)}),ze.on.perfEnd((t,n,o,s,r)=>{!q.timelineLayersState.recordingState||!q.timelineLayersState.performanceEventEnabled||Ou(e,t,n,o,s,r)}))}w();var g_=10,Xt=[];function v_(e){if(typeof window>"u")return;const t=window;if(e&&(t.$vm=e,Xt[0]!==e)){Xt.length>=g_&&Xt.pop();for(let n=Xt.length;n>0;n--)t[`$vm${n}`]=Xt[n]=Xt[n-1];t.$vm0=Xt[0]=e}}var Zt="components";function y_(e){return[{id:Zt,label:"Components",app:e},o=>{o.addInspector({id:Zt,label:"Components",treeFilterPlaceholder:"Search components"}),m_(o),o.on.getInspectorTree(async i=>{if(i.app===e&&i.inspectorId===Zt){const l=hn(se.value,i.instanceId);if(l){const u=new h_({filterText:i.filter,maxDepth:100,recursively:!1,api:o});i.rootNodes=await u.getComponentTree(l)}}}),o.on.getInspectorState(async i=>{var l;if(i.app===e&&i.inspectorId===Zt){const u=f_({instanceId:i.nodeId}),a=u.instance,c=(l=u.instance)==null?void 0:l.appContext.app,f={componentInstance:a,app:c,instanceData:u};Xe.hooks.callHookWith(h=>{h.forEach(d=>d(f))},"inspectComponent"),i.state=u,v_(a)}}),o.on.editInspectorState(async i=>{i.app===e&&i.inspectorId===Zt&&(yh(i),await o.sendInspectorState("components"))});const s=It(()=>{o.sendInspectorTree(Zt)},120),r=It(()=>{o.sendInspectorState(Zt)},120);ze.on.componentAdded(async(i,l,u,a)=>{var c,f,h;if(q.highPerfModeEnabled||(h=(f=(c=i==null?void 0:i._instance)==null?void 0:c.type)==null?void 0:f.devtools)!=null&&h.hide||!i||typeof l!="number"&&!l||!a)return;const d=await ur({app:i,uid:l,instance:a}),_=await qe(i);a&&(a.__VUE_DEVTOOLS_NEXT_UID__==null&&(a.__VUE_DEVTOOLS_NEXT_UID__=d),_!=null&&_.instanceMap.has(d)||(_==null||_.instanceMap.set(d,a),se.value.id===(_==null?void 0:_.id)&&(se.value.instanceMap=_.instanceMap))),_&&s()}),ze.on.componentUpdated(async(i,l,u,a)=>{var c,f,h;if(q.highPerfModeEnabled||(h=(f=(c=i==null?void 0:i._instance)==null?void 0:c.type)==null?void 0:f.devtools)!=null&&h.hide||!i||typeof l!="number"&&!l||!a)return;const d=await ur({app:i,uid:l,instance:a}),_=await qe(i);a&&(a.__VUE_DEVTOOLS_NEXT_UID__==null&&(a.__VUE_DEVTOOLS_NEXT_UID__=d),_!=null&&_.instanceMap.has(d)||(_==null||_.instanceMap.set(d,a),se.value.id===(_==null?void 0:_.id)&&(se.value.instanceMap=_.instanceMap))),_&&(s(),r())}),ze.on.componentRemoved(async(i,l,u,a)=>{var c,f,h;if(q.highPerfModeEnabled||(h=(f=(c=i==null?void 0:i._instance)==null?void 0:c.type)==null?void 0:f.devtools)!=null&&h.hide||!i||typeof l!="number"&&!l||!a)return;const d=await qe(i);if(!d)return;const _=await ur({app:i,uid:l,instance:a});d==null||d.instanceMap.delete(_),se.value.id===(d==null?void 0:d.id)&&(se.value.instanceMap=d.instanceMap),s()})}]}var Cu,Du;(Du=(Cu=O).__VUE_DEVTOOLS_KIT__REGISTERED_PLUGIN_APPS__)!=null||(Cu.__VUE_DEVTOOLS_KIT__REGISTERED_PLUGIN_APPS__=new Set);function E_(e,t){return ze.setupDevToolsPlugin(e,t)}function Pu(e,t){const[n,o]=e;if(n.app!==t)return;const s=new Fh({plugin:{setupFn:o,descriptor:n},ctx:Xe});n.packageName==="vuex"&&s.on.editInspectorState(r=>{s.sendInspectorState(r.inspectorId)}),o(s)}function b_(e){O.__VUE_DEVTOOLS_KIT__REGISTERED_PLUGIN_APPS__.delete(e)}function Or(e,t){O.__VUE_DEVTOOLS_KIT__REGISTERED_PLUGIN_APPS__.has(e)||q.highPerfModeEnabled&&!(t!=null&&t.inspectingComponent)||(O.__VUE_DEVTOOLS_KIT__REGISTERED_PLUGIN_APPS__.add(e),Kn.forEach(n=>{Pu(n,e)}))}w(),w();var Wn="__VUE_DEVTOOLS_ROUTER__",mn="__VUE_DEVTOOLS_ROUTER_INFO__",Iu,Ru;(Ru=(Iu=O)[mn])!=null||(Iu[mn]={currentRoute:null,routes:[]});var ku,Nu;(Nu=(ku=O)[Wn])!=null||(ku[Wn]={}),new Proxy(O[mn],{get(e,t){return O[mn][t]}}),new Proxy(O[Wn],{get(e,t){if(t==="value")return O[Wn]}});function w_(e){const t=new Map;return((e==null?void 0:e.getRoutes())||[]).filter(n=>!t.has(n.path)&&t.set(n.path,1))}function Ar(e){return e.map(t=>{let{path:n,name:o,children:s,meta:r}=t;return s!=null&&s.length&&(s=Ar(s)),{path:n,name:o,children:s,meta:r}})}function S_(e){if(e){const{fullPath:t,hash:n,href:o,path:s,name:r,matched:i,params:l,query:u}=e;return{fullPath:t,hash:n,href:o,path:s,name:r,params:l,query:u,matched:Ar(i)}}return e}function Vu(e,t){function n(){var o;const s=(o=e.app)==null?void 0:o.config.globalProperties.$router,r=S_(s==null?void 0:s.currentRoute.value),i=Ar(w_(s)),l=console.warn;console.warn=()=>{},O[mn]={currentRoute:r?Ll(r):{},routes:Ll(i)},O[Wn]=s,console.warn=l}n(),ze.on.componentUpdated(It(()=>{var o;((o=t.value)==null?void 0:o.app)===e.app&&(n(),!q.highPerfModeEnabled&&Xe.hooks.callHook("routerInfoUpdated",{state:O[mn]}))},200))}function x_(e){return{async getInspectorTree(t){const n={...t,app:se.value.app,rootNodes:[]};return await new Promise(o=>{e.callHookWith(async s=>{await Promise.all(s.map(r=>r(n))),o()},"getInspectorTree")}),n.rootNodes},async getInspectorState(t){const n={...t,app:se.value.app,state:null},o={currentTab:`custom-inspector:${t.inspectorId}`};return await new Promise(s=>{e.callHookWith(async r=>{await Promise.all(r.map(i=>i(n,o))),s()},"getInspectorState")}),n.state},editInspectorState(t){const n=new Jl,o={...t,app:se.value.app,set:(s,r=t.path,i=t.state.value,l)=>{n.set(s,r,i,l||n.createDefaultSetCallback(t.state))}};e.callHookWith(s=>{s.forEach(r=>r(o))},"editInspectorState")},sendInspectorState(t){const n=Ko(t);e.callHook("sendInspectorState",{inspectorId:t,plugin:{descriptor:n.descriptor,setupFn:()=>({})}})},inspectComponentInspector(){return ch()},cancelInspectComponentInspector(){return ah()},getComponentRenderCode(t){const n=hn(se.value,t);if(n)return typeof(n==null?void 0:n.type)!="function"?n.render.toString():n.type.toString()},scrollToComponent(t){return fh({id:t})},openInEditor:Rh,getVueInspector:hh,toggleApp(t,n){const o=kt.value.find(s=>s.id===t);o&&(gu(t),br(o),Vu(o,se),ou(),Or(o.app,n))},inspectDOM(t){const n=hn(se.value,t);if(n){const[o]=zn(n);o&&(O.__VUE_DEVTOOLS_INSPECT_DOM_TARGET__=o)}},updatePluginSettings(t,n,o){Vh(t,n,o)},getPluginSettings(t){return{options:Nh(t),values:Eu(t)}}}}w();var Lu,$u;($u=(Lu=O).__VUE_DEVTOOLS_ENV__)!=null||(Lu.__VUE_DEVTOOLS_ENV__={vitePluginDetected:!1});function T_(){return O.__VUE_DEVTOOLS_ENV__}var Mu=Th(),Uu,Fu;(Fu=(Uu=O).__VUE_DEVTOOLS_KIT_CONTEXT__)!=null||(Uu.__VUE_DEVTOOLS_KIT_CONTEXT__={hooks:Mu,get state(){return{...q,activeAppRecordId:se.id,activeAppRecord:se.value,appRecords:kt.value}},api:x_(Mu)});var Xe=O.__VUE_DEVTOOLS_KIT_CONTEXT__;w();var O_=jp(Wp()),Bu,Hu,Nt=(Hu=(Bu=O).__VUE_DEVTOOLS_NEXT_APP_RECORD_INFO__)!=null?Hu:Bu.__VUE_DEVTOOLS_NEXT_APP_RECORD_INFO__={id:0,appIds:new Set};function A_(e,t){var n;return((n=e==null?void 0:e._component)==null?void 0:n.name)||`App ${t}`}function C_(e){var t,n,o,s;if(e._instance)return e._instance;if((n=(t=e._container)==null?void 0:t._vnode)!=null&&n.component)return(s=(o=e._container)==null?void 0:o._vnode)==null?void 0:s.component}function D_(e){const t=e.__VUE_DEVTOOLS_NEXT_APP_RECORD_ID__;t!=null&&(Nt.appIds.delete(t),Nt.id--)}function P_(e,t){if(e.__VUE_DEVTOOLS_NEXT_APP_RECORD_ID__!=null)return e.__VUE_DEVTOOLS_NEXT_APP_RECORD_ID__;let n=t??(Nt.id++).toString();if(t&&Nt.appIds.has(n)){let o=1;for(;Nt.appIds.has(`${t}_${o}`);)o++;n=`${t}_${o}`}return Nt.appIds.add(n),e.__VUE_DEVTOOLS_NEXT_APP_RECORD_ID__=n,n}function I_(e,t){const n=C_(e);if(n){Nt.id++;const o=A_(e,Nt.id.toString()),r={id:P_(e,(0,O_.default)(o)),name:o,types:t,instanceMap:new Map,perfGroupIds:new Map,rootInstance:n};e.__VUE_DEVTOOLS_NEXT_APP_RECORD__=r;const i=`${r.id}:root`;return r.instanceMap.set(i,r.rootInstance),r.rootInstance.__VUE_DEVTOOLS_NEXT_UID__=i,r}else return{}}function R_(){var e;wr({vitePluginDetected:T_().vitePluginDetected});const t=((e=O.__VUE_DEVTOOLS_GLOBAL_HOOK__)==null?void 0:e.id)==="vue-devtools-next";if(O.__VUE_DEVTOOLS_GLOBAL_HOOK__&&t)return;const n=$h();if(O.__VUE_DEVTOOLS_HOOK_REPLAY__)try{O.__VUE_DEVTOOLS_HOOK_REPLAY__.forEach(o=>o(n)),O.__VUE_DEVTOOLS_HOOK_REPLAY__=[]}catch(o){console.error("[vue-devtools] Error during hook replay",o)}n.once("init",o=>{O.__VUE_DEVTOOLS_VUE2_APP_DETECTED__=!0,console.log("%c[_____Vue DevTools v7 log_____]","color: red; font-bold: 600; font-size: 16px;"),console.log("%cVue DevTools v7 detected in your Vue2 project. v7 only supports Vue3 and will not work.","font-bold: 500; font-size: 14px;"),console.log("%cThe legacy version that supports both Vue 2 and Vue 3 has been moved to %c https://chromewebstore.google.com/detail/vuejs-devtools/iaajmlceplecbljialhhkmedjlpdblhp","font-size: 14px;","text-decoration: underline; cursor: pointer;font-size: 14px;"),console.log("%cPlease install and enable only the legacy version for your Vue2 app.","font-bold: 500; font-size: 14px;"),console.log("%c[_____Vue DevTools v7 log_____]","color: red; font-bold: 600; font-size: 16px;")}),ze.on.setupDevtoolsPlugin((o,s)=>{var r;kh(o,s);const{app:i}=(r=se)!=null?r:{};o.settings&&bu(o.id,o.settings),i&&Pu([o,s],i)}),Gp(()=>{Kn.filter(([s])=>s.id!=="components").forEach(([s,r])=>{n.emit("devtools-plugin:setup",s,r,{target:"legacy"})})}),ze.on.vueAppInit(async(o,s,r)=>{const l={...I_(o,r),app:o,version:s};Dh(l),kt.value.length===1&&(br(l),gu(l.id),Vu(l,se),Or(l.app)),E_(...y_(l.app)),wr({connected:!0}),n.apps.push(o)}),ze.on.vueAppUnmount(async o=>{const s=kt.value.filter(r=>r.app!==o);s.length===0&&wr({connected:!1}),Ph(o),D_(o),se.value.app===o&&(br(s[0]),Xe.hooks.callHook("sendActiveAppUpdatedToClient")),O.__VUE_DEVTOOLS_GLOBAL_HOOK__.apps.splice(O.__VUE_DEVTOOLS_GLOBAL_HOOK__.apps.indexOf(o),1),b_(o)}),Mh(n),O.__VUE_DEVTOOLS_GLOBAL_HOOK__?Sp||Object.assign(__VUE_DEVTOOLS_GLOBAL_HOOK__,n):Object.defineProperty(O,"__VUE_DEVTOOLS_GLOBAL_HOOK__",{get(){return n}})}w();function k_(e){q.highPerfModeEnabled=e??!q.highPerfModeEnabled,!e&&se.value&&Or(se.value.app)}w(),w(),w();function N_(e){q.devtoolsClientDetected={...q.devtoolsClientDetected,...e};const t=Object.values(q.devtoolsClientDetected).some(Boolean);k_(!t)}var zu,ju;(ju=(zu=O).__VUE_DEVTOOLS_UPDATE_CLIENT_DETECTED__)!=null||(zu.__VUE_DEVTOOLS_UPDATE_CLIENT_DETECTED__=N_),w(),w(),w(),w(),w(),w(),w();var V_=class{constructor(){this.keyToValue=new Map,this.valueToKey=new Map}set(e,t){this.keyToValue.set(e,t),this.valueToKey.set(t,e)}getByKey(e){return this.keyToValue.get(e)}getByValue(e){return this.valueToKey.get(e)}clear(){this.keyToValue.clear(),this.valueToKey.clear()}},Ku=class{constructor(e){this.generateIdentifier=e,this.kv=new V_}register(e,t){this.kv.getByValue(e)||(t||(t=this.generateIdentifier(e)),this.kv.set(t,e))}clear(){this.kv.clear()}getIdentifier(e){return this.kv.getByValue(e)}getValue(e){return this.kv.getByKey(e)}},L_=class extends Ku{constructor(){super(e=>e.name),this.classToAllowedProps=new Map}register(e,t){typeof t=="object"?(t.allowProps&&this.classToAllowedProps.set(e,t.allowProps),super.register(e,t.identifier)):super.register(e,t)}getAllowedProps(e){return this.classToAllowedProps.get(e)}};w(),w();function $_(e){if("values"in Object)return Object.values(e);const t=[];for(const n in e)e.hasOwnProperty(n)&&t.push(e[n]);return t}function M_(e,t){const n=$_(e);if("find"in n)return n.find(t);const o=n;for(let s=0;s<o.length;s++){const r=o[s];if(t(r))return r}}function gn(e,t){Object.entries(e).forEach(([n,o])=>t(o,n))}function Yo(e,t){return e.indexOf(t)!==-1}function Wu(e,t){for(let n=0;n<e.length;n++){const o=e[n];if(t(o))return o}}var U_=class{constructor(){this.transfomers={}}register(e){this.transfomers[e.name]=e}findApplicable(e){return M_(this.transfomers,t=>t.isApplicable(e))}findByName(e){return this.transfomers[e]}};w(),w();var F_=e=>Object.prototype.toString.call(e).slice(8,-1),Gu=e=>typeof e>"u",B_=e=>e===null,Gn=e=>typeof e!="object"||e===null||e===Object.prototype?!1:Object.getPrototypeOf(e)===null?!0:Object.getPrototypeOf(e)===Object.prototype,Cr=e=>Gn(e)&&Object.keys(e).length===0,Vt=e=>Array.isArray(e),H_=e=>typeof e=="string",z_=e=>typeof e=="number"&&!isNaN(e),j_=e=>typeof e=="boolean",K_=e=>e instanceof RegExp,Yn=e=>e instanceof Map,qn=e=>e instanceof Set,Yu=e=>F_(e)==="Symbol",W_=e=>e instanceof Date&&!isNaN(e.valueOf()),G_=e=>e instanceof Error,qu=e=>typeof e=="number"&&isNaN(e),Y_=e=>j_(e)||B_(e)||Gu(e)||z_(e)||H_(e)||Yu(e),q_=e=>typeof e=="bigint",X_=e=>e===1/0||e===-1/0,Z_=e=>ArrayBuffer.isView(e)&&!(e instanceof DataView),J_=e=>e instanceof URL;w();var Xu=e=>e.replace(/\./g,"\\."),Dr=e=>e.map(String).map(Xu).join("."),Xn=e=>{const t=[];let n="";for(let s=0;s<e.length;s++){let r=e.charAt(s);if(r==="\\"&&e.charAt(s+1)==="."){n+=".",s++;continue}if(r==="."){t.push(n),n="";continue}n+=r}const o=n;return t.push(o),t};w();function lt(e,t,n,o){return{isApplicable:e,annotation:t,transform:n,untransform:o}}var Zu=[lt(Gu,"undefined",()=>null,()=>{}),lt(q_,"bigint",e=>e.toString(),e=>typeof BigInt<"u"?BigInt(e):(console.error("Please add a BigInt polyfill."),e)),lt(W_,"Date",e=>e.toISOString(),e=>new Date(e)),lt(G_,"Error",(e,t)=>{const n={name:e.name,message:e.message};return t.allowedErrorProps.forEach(o=>{n[o]=e[o]}),n},(e,t)=>{const n=new Error(e.message);return n.name=e.name,n.stack=e.stack,t.allowedErrorProps.forEach(o=>{n[o]=e[o]}),n}),lt(K_,"regexp",e=>""+e,e=>{const t=e.slice(1,e.lastIndexOf("/")),n=e.slice(e.lastIndexOf("/")+1);return new RegExp(t,n)}),lt(qn,"set",e=>[...e.values()],e=>new Set(e)),lt(Yn,"map",e=>[...e.entries()],e=>new Map(e)),lt(e=>qu(e)||X_(e),"number",e=>qu(e)?"NaN":e>0?"Infinity":"-Infinity",Number),lt(e=>e===0&&1/e===-1/0,"number",()=>"-0",Number),lt(J_,"URL",e=>e.toString(),e=>new URL(e))];function qo(e,t,n,o){return{isApplicable:e,annotation:t,transform:n,untransform:o}}var Ju=qo((e,t)=>Yu(e)?!!t.symbolRegistry.getIdentifier(e):!1,(e,t)=>["symbol",t.symbolRegistry.getIdentifier(e)],e=>e.description,(e,t,n)=>{const o=n.symbolRegistry.getValue(t[1]);if(!o)throw new Error("Trying to deserialize unknown symbol");return o}),Q_=[Int8Array,Uint8Array,Int16Array,Uint16Array,Int32Array,Uint32Array,Float32Array,Float64Array,Uint8ClampedArray].reduce((e,t)=>(e[t.name]=t,e),{}),Qu=qo(Z_,e=>["typed-array",e.constructor.name],e=>[...e],(e,t)=>{const n=Q_[t[1]];if(!n)throw new Error("Trying to deserialize unknown typed array");return new n(e)});function ea(e,t){return e!=null&&e.constructor?!!t.classRegistry.getIdentifier(e.constructor):!1}var ta=qo(ea,(e,t)=>["class",t.classRegistry.getIdentifier(e.constructor)],(e,t)=>{const n=t.classRegistry.getAllowedProps(e.constructor);if(!n)return{...e};const o={};return n.forEach(s=>{o[s]=e[s]}),o},(e,t,n)=>{const o=n.classRegistry.getValue(t[1]);if(!o)throw new Error(`Trying to deserialize unknown class '${t[1]}' - check https://github.com/blitz-js/superjson/issues/116#issuecomment-773996564`);return Object.assign(Object.create(o.prototype),e)}),na=qo((e,t)=>!!t.customTransformerRegistry.findApplicable(e),(e,t)=>["custom",t.customTransformerRegistry.findApplicable(e).name],(e,t)=>t.customTransformerRegistry.findApplicable(e).serialize(e),(e,t,n)=>{const o=n.customTransformerRegistry.findByName(t[1]);if(!o)throw new Error("Trying to deserialize unknown custom value");return o.deserialize(e)}),em=[ta,Ju,na,Qu],oa=(e,t)=>{const n=Wu(em,s=>s.isApplicable(e,t));if(n)return{value:n.transform(e,t),type:n.annotation(e,t)};const o=Wu(Zu,s=>s.isApplicable(e,t));if(o)return{value:o.transform(e,t),type:o.annotation}},sa={};Zu.forEach(e=>{sa[e.annotation]=e});var tm=(e,t,n)=>{if(Vt(t))switch(t[0]){case"symbol":return Ju.untransform(e,t,n);case"class":return ta.untransform(e,t,n);case"custom":return na.untransform(e,t,n);case"typed-array":return Qu.untransform(e,t,n);default:throw new Error("Unknown transformation: "+t)}else{const o=sa[t];if(!o)throw new Error("Unknown transformation: "+t);return o.untransform(e,n)}};w();var vn=(e,t)=>{if(t>e.size)throw new Error("index out of bounds");const n=e.keys();for(;t>0;)n.next(),t--;return n.next().value};function ra(e){if(Yo(e,"__proto__"))throw new Error("__proto__ is not allowed as a property");if(Yo(e,"prototype"))throw new Error("prototype is not allowed as a property");if(Yo(e,"constructor"))throw new Error("constructor is not allowed as a property")}var nm=(e,t)=>{ra(t);for(let n=0;n<t.length;n++){const o=t[n];if(qn(e))e=vn(e,+o);else if(Yn(e)){const s=+o,r=+t[++n]==0?"key":"value",i=vn(e,s);switch(r){case"key":e=i;break;case"value":e=e.get(i);break}}else e=e[o]}return e},Pr=(e,t,n)=>{if(ra(t),t.length===0)return n(e);let o=e;for(let r=0;r<t.length-1;r++){const i=t[r];if(Vt(o)){const l=+i;o=o[l]}else if(Gn(o))o=o[i];else if(qn(o)){const l=+i;o=vn(o,l)}else if(Yn(o)){if(r===t.length-2)break;const u=+i,a=+t[++r]==0?"key":"value",c=vn(o,u);switch(a){case"key":o=c;break;case"value":o=o.get(c);break}}}const s=t[t.length-1];if(Vt(o)?o[+s]=n(o[+s]):Gn(o)&&(o[s]=n(o[s])),qn(o)){const r=vn(o,+s),i=n(r);r!==i&&(o.delete(r),o.add(i))}if(Yn(o)){const r=+t[t.length-2],i=vn(o,r);switch(+s==0?"key":"value"){case"key":{const u=n(i);o.set(u,o.get(i)),u!==i&&o.delete(i);break}case"value":{o.set(i,n(o.get(i)));break}}}return e};function Ir(e,t,n=[]){if(!e)return;if(!Vt(e)){gn(e,(r,i)=>Ir(r,t,[...n,...Xn(i)]));return}const[o,s]=e;s&&gn(s,(r,i)=>{Ir(r,t,[...n,...Xn(i)])}),t(o,n)}function om(e,t,n){return Ir(t,(o,s)=>{e=Pr(e,s,r=>tm(r,o,n))}),e}function sm(e,t){function n(o,s){const r=nm(e,Xn(s));o.map(Xn).forEach(i=>{e=Pr(e,i,()=>r)})}if(Vt(t)){const[o,s]=t;o.forEach(r=>{e=Pr(e,Xn(r),()=>e)}),s&&gn(s,n)}else gn(t,n);return e}var rm=(e,t)=>Gn(e)||Vt(e)||Yn(e)||qn(e)||ea(e,t);function im(e,t,n){const o=n.get(e);o?o.push(t):n.set(e,[t])}function lm(e,t){const n={};let o;return e.forEach(s=>{if(s.length<=1)return;t||(s=s.map(l=>l.map(String)).sort((l,u)=>l.length-u.length));const[r,...i]=s;r.length===0?o=i.map(Dr):n[Dr(r)]=i.map(Dr)}),o?Cr(n)?[o]:[o,n]:Cr(n)?void 0:n}var ia=(e,t,n,o,s=[],r=[],i=new Map)=>{var l;const u=Y_(e);if(!u){im(e,s,t);const _=i.get(e);if(_)return o?{transformedValue:null}:_}if(!rm(e,n)){const _=oa(e,n),v=_?{transformedValue:_.value,annotations:[_.type]}:{transformedValue:e};return u||i.set(e,v),v}if(Yo(r,e))return{transformedValue:null};const a=oa(e,n),c=(l=a==null?void 0:a.value)!=null?l:e,f=Vt(c)?[]:{},h={};gn(c,(_,v)=>{if(v==="__proto__"||v==="constructor"||v==="prototype")throw new Error(`Detected property ${v}. This is a prototype pollution risk, please remove it from your object.`);const y=ia(_,t,n,o,[...s,v],[...r,e],i);f[v]=y.transformedValue,Vt(y.annotations)?h[v]=y.annotations:Gn(y.annotations)&&gn(y.annotations,(g,x)=>{h[Xu(v)+"."+x]=g})});const d=Cr(h)?{transformedValue:f,annotations:a?[a.type]:void 0}:{transformedValue:f,annotations:a?[a.type,h]:h};return u||i.set(e,d),d};w(),w();function la(e){return Object.prototype.toString.call(e).slice(8,-1)}function ua(e){return la(e)==="Array"}function um(e){if(la(e)!=="Object")return!1;const t=Object.getPrototypeOf(e);return!!t&&t.constructor===Object&&t===Object.prototype}function am(e,t,n,o,s){const r={}.propertyIsEnumerable.call(o,t)?"enumerable":"nonenumerable";r==="enumerable"&&(e[t]=n),s&&r==="nonenumerable"&&Object.defineProperty(e,t,{value:n,enumerable:!1,writable:!0,configurable:!0})}function Rr(e,t={}){if(ua(e))return e.map(s=>Rr(s,t));if(!um(e))return e;const n=Object.getOwnPropertyNames(e),o=Object.getOwnPropertySymbols(e);return[...n,...o].reduce((s,r)=>{if(ua(t.props)&&!t.props.includes(r))return s;const i=e[r],l=Rr(i,t);return am(s,r,l,e,t.nonenumerable),s},{})}var ce=class{constructor({dedupe:e=!1}={}){this.classRegistry=new L_,this.symbolRegistry=new Ku(t=>{var n;return(n=t.description)!=null?n:""}),this.customTransformerRegistry=new U_,this.allowedErrorProps=[],this.dedupe=e}serialize(e){const t=new Map,n=ia(e,t,this,this.dedupe),o={json:n.transformedValue};n.annotations&&(o.meta={...o.meta,values:n.annotations});const s=lm(t,this.dedupe);return s&&(o.meta={...o.meta,referentialEqualities:s}),o}deserialize(e){const{json:t,meta:n}=e;let o=Rr(t);return n!=null&&n.values&&(o=om(o,n.values,this)),n!=null&&n.referentialEqualities&&(o=sm(o,n.referentialEqualities)),o}stringify(e){return JSON.stringify(this.serialize(e))}parse(e){return this.deserialize(JSON.parse(e))}registerClass(e,t){this.classRegistry.register(e,t)}registerSymbol(e,t){this.symbolRegistry.register(e,t)}registerCustom(e,t){this.customTransformerRegistry.register({name:t,...e})}allowErrorProps(...e){this.allowedErrorProps.push(...e)}};ce.defaultInstance=new ce,ce.serialize=ce.defaultInstance.serialize.bind(ce.defaultInstance),ce.deserialize=ce.defaultInstance.deserialize.bind(ce.defaultInstance),ce.stringify=ce.defaultInstance.stringify.bind(ce.defaultInstance),ce.parse=ce.defaultInstance.parse.bind(ce.defaultInstance),ce.registerClass=ce.defaultInstance.registerClass.bind(ce.defaultInstance),ce.registerSymbol=ce.defaultInstance.registerSymbol.bind(ce.defaultInstance),ce.registerCustom=ce.defaultInstance.registerCustom.bind(ce.defaultInstance),ce.allowErrorProps=ce.defaultInstance.allowErrorProps.bind(ce.defaultInstance),w(),w(),w(),w(),w(),w(),w(),w(),w(),w(),w(),w(),w(),w();var cm="iframe:server-context";function fm(e){O[cm]=e}w(),w(),w(),w(),w(),w(),w(),w(),w();var aa,ca;(ca=(aa=O).__VUE_DEVTOOLS_KIT_MESSAGE_CHANNELS__)!=null||(aa.__VUE_DEVTOOLS_KIT_MESSAGE_CHANNELS__=[]);var fa,da;(da=(fa=O).__VUE_DEVTOOLS_KIT_RPC_CLIENT__)!=null||(fa.__VUE_DEVTOOLS_KIT_RPC_CLIENT__=null);var pa,ha;(ha=(pa=O).__VUE_DEVTOOLS_KIT_RPC_SERVER__)!=null||(pa.__VUE_DEVTOOLS_KIT_RPC_SERVER__=null);var _a,ma;(ma=(_a=O).__VUE_DEVTOOLS_KIT_VITE_RPC_CLIENT__)!=null||(_a.__VUE_DEVTOOLS_KIT_VITE_RPC_CLIENT__=null);var ga,va;(va=(ga=O).__VUE_DEVTOOLS_KIT_VITE_RPC_SERVER__)!=null||(ga.__VUE_DEVTOOLS_KIT_VITE_RPC_SERVER__=null);var ya,Ea;(Ea=(ya=O).__VUE_DEVTOOLS_KIT_BROADCAST_RPC_SERVER__)!=null||(ya.__VUE_DEVTOOLS_KIT_BROADCAST_RPC_SERVER__=null);function dm(){return O.__VUE_DEVTOOLS_KIT_RPC_CLIENT__}function ba(){return O.__VUE_DEVTOOLS_KIT_RPC_SERVER__}function pm(){return O.__VUE_DEVTOOLS_KIT_VITE_RPC_CLIENT__}w(),w(),w(),w(),w(),w(),w();var wa={hook:ze,init:()=>{R_()},get ctx(){return Xe},get api(){return Xe.api}};function hm(){var e;return(e=O.__VUE_DEVTOOLS_CLIENT_URL__)!=null?e:(()=>{if(Ho){const t=document.querySelector("meta[name=__VUE_DEVTOOLS_CLIENT_URL__]");if(t)return t.getAttribute("content")}return""})()}function kr(e,t={},n){for(const o in e){const s=e[o],r=n?`${n}:${o}`:o;typeof s=="object"&&s!==null?kr(s,t,r):typeof s=="function"&&(t[r]=s)}return t}var _m={run:e=>e()},mm=()=>_m,Sa=typeof console.createTask<"u"?console.createTask:mm;function gm(e,t){const n=t.shift(),o=Sa(n);return e.reduce((s,r)=>s.then(()=>o.run(()=>r(...t))),Promise.resolve())}function vm(e,t){const n=t.shift(),o=Sa(n);return Promise.all(e.map(s=>o.run(()=>s(...t))))}function Nr(e,t){for(const n of[...e])n(t)}var ym=class{constructor(){this._hooks={},this._before=void 0,this._after=void 0,this._deprecatedMessages=void 0,this._deprecatedHooks={},this.hook=this.hook.bind(this),this.callHook=this.callHook.bind(this),this.callHookWith=this.callHookWith.bind(this)}hook(e,t,n={}){if(!e||typeof t!="function")return()=>{};const o=e;let s;for(;this._deprecatedHooks[e];)s=this._deprecatedHooks[e],e=s.to;if(s&&!n.allowDeprecated){let r=s.message;r||(r=`${o} hook has been deprecated`+(s.to?`, please use ${s.to}`:"")),this._deprecatedMessages||(this._deprecatedMessages=new Set),this._deprecatedMessages.has(r)||(console.warn(r),this._deprecatedMessages.add(r))}if(!t.name)try{Object.defineProperty(t,"name",{get:()=>"_"+e.replace(/\W+/g,"_")+"_hook_cb",configurable:!0})}catch{}return this._hooks[e]=this._hooks[e]||[],this._hooks[e].push(t),()=>{t&&(this.removeHook(e,t),t=void 0)}}hookOnce(e,t){let n,o=(...s)=>(typeof n=="function"&&n(),n=void 0,o=void 0,t(...s));return n=this.hook(e,o),n}removeHook(e,t){if(this._hooks[e]){const n=this._hooks[e].indexOf(t);n!==-1&&this._hooks[e].splice(n,1),this._hooks[e].length===0&&delete this._hooks[e]}}deprecateHook(e,t){this._deprecatedHooks[e]=typeof t=="string"?{to:t}:t;const n=this._hooks[e]||[];delete this._hooks[e];for(const o of n)this.hook(e,o)}deprecateHooks(e){Object.assign(this._deprecatedHooks,e);for(const t in e)this.deprecateHook(t,e[t])}addHooks(e){const t=kr(e),n=Object.keys(t).map(o=>this.hook(o,t[o]));return()=>{for(const o of n.splice(0,n.length))o()}}removeHooks(e){const t=kr(e);for(const n in t)this.removeHook(n,t[n])}removeAllHooks(){for(const e in this._hooks)delete this._hooks[e]}callHook(e,...t){return t.unshift(e),this.callHookWith(gm,e,...t)}callHookParallel(e,...t){return t.unshift(e),this.callHookWith(vm,e,...t)}callHookWith(e,t,...n){const o=this._before||this._after?{name:t,args:n,context:{}}:void 0;this._before&&Nr(this._before,o);const s=e(t in this._hooks?[...this._hooks[t]]:[],n);return s instanceof Promise?s.finally(()=>{this._after&&o&&Nr(this._after,o)}):(this._after&&o&&Nr(this._after,o),s)}beforeEach(e){return this._before=this._before||[],this._before.push(e),()=>{if(this._before!==void 0){const t=this._before.indexOf(e);t!==-1&&this._before.splice(t,1)}}}afterEach(e){return this._after=this._after||[],this._after.push(e),()=>{if(this._after!==void 0){const t=this._after.indexOf(e);t!==-1&&this._after.splice(t,1)}}}};function xa(){return new ym}xa(),new Proxy({value:{},functions:{}},{get(e,t){const n=dm();if(t==="value")return n;if(t==="functions")return n.$functions}});var Ta=new Proxy({value:{},functions:{}},{get(e,t){const n=ba();if(t==="value")return n;if(t==="functions")return n.functions}});function Em(e){let t=null;const n=120;function o(){Ta.value.clients.length>0&&(e(),clearTimeout(t))}t=setInterval(()=>{o()},n)}xa(),new Proxy({value:{},functions:{}},{get(e,t){const n=pm();if(t==="value")return n;if(t==="functions")return n==null?void 0:n.$functions}});const bm=["top","right","bottom","left"],Oa=["start","end"],Aa=bm.reduce((e,t)=>e.concat(t,t+"-"+Oa[0],t+"-"+Oa[1]),[]),Zn=Math.min,Jt=Math.max,wm={left:"right",right:"left",bottom:"top",top:"bottom"},Sm={start:"end",end:"start"};function Vr(e,t,n){return Jt(e,Zn(t,n))}function Qt(e,t){return typeof e=="function"?e(t):e}function ut(e){return e.split("-")[0]}function Ze(e){return e.split("-")[1]}function Ca(e){return e==="x"?"y":"x"}function Lr(e){return e==="y"?"height":"width"}function en(e){return["top","bottom"].includes(ut(e))?"y":"x"}function $r(e){return Ca(en(e))}function Da(e,t,n){n===void 0&&(n=!1);const o=Ze(e),s=$r(e),r=Lr(s);let i=s==="x"?o===(n?"end":"start")?"right":"left":o==="start"?"bottom":"top";return t.reference[r]>t.floating[r]&&(i=Zo(i)),[i,Zo(i)]}function xm(e){const t=Zo(e);return[Xo(e),t,Xo(t)]}function Xo(e){return e.replace(/start|end/g,t=>Sm[t])}function Tm(e,t,n){const o=["left","right"],s=["right","left"],r=["top","bottom"],i=["bottom","top"];switch(e){case"top":case"bottom":return n?t?s:o:t?o:s;case"left":case"right":return t?r:i;default:return[]}}function Om(e,t,n,o){const s=Ze(e);let r=Tm(ut(e),n==="start",o);return s&&(r=r.map(i=>i+"-"+s),t&&(r=r.concat(r.map(Xo)))),r}function Zo(e){return e.replace(/left|right|bottom|top/g,t=>wm[t])}function Am(e){return{top:0,right:0,bottom:0,left:0,...e}}function Pa(e){return typeof e!="number"?Am(e):{top:e,right:e,bottom:e,left:e}}function Jn(e){const{x:t,y:n,width:o,height:s}=e;return{width:o,height:s,top:n,left:t,right:t+o,bottom:n+s,x:t,y:n}}function Ia(e,t,n){let{reference:o,floating:s}=e;const r=en(t),i=$r(t),l=Lr(i),u=ut(t),a=r==="y",c=o.x+o.width/2-s.width/2,f=o.y+o.height/2-s.height/2,h=o[l]/2-s[l]/2;let d;switch(u){case"top":d={x:c,y:o.y-s.height};break;case"bottom":d={x:c,y:o.y+o.height};break;case"right":d={x:o.x+o.width,y:f};break;case"left":d={x:o.x-s.width,y:f};break;default:d={x:o.x,y:o.y}}switch(Ze(t)){case"start":d[i]-=h*(n&&a?-1:1);break;case"end":d[i]+=h*(n&&a?-1:1);break}return d}const Cm=async(e,t,n)=>{const{placement:o="bottom",strategy:s="absolute",middleware:r=[],platform:i}=n,l=r.filter(Boolean),u=await(i.isRTL==null?void 0:i.isRTL(t));let a=await i.getElementRects({reference:e,floating:t,strategy:s}),{x:c,y:f}=Ia(a,o,u),h=o,d={},_=0;for(let v=0;v<l.length;v++){const{name:y,fn:g}=l[v],{x,y:D,data:b,reset:P}=await g({x:c,y:f,initialPlacement:o,placement:h,strategy:s,middlewareData:d,rects:a,platform:i,elements:{reference:e,floating:t}});c=x??c,f=D??f,d={...d,[y]:{...d[y],...b}},P&&_<=50&&(_++,typeof P=="object"&&(P.placement&&(h=P.placement),P.rects&&(a=P.rects===!0?await i.getElementRects({reference:e,floating:t,strategy:s}):P.rects),{x:c,y:f}=Ia(a,h,u)),v=-1)}return{x:c,y:f,placement:h,strategy:s,middlewareData:d}};async function Jo(e,t){var n;t===void 0&&(t={});const{x:o,y:s,platform:r,rects:i,elements:l,strategy:u}=e,{boundary:a="clippingAncestors",rootBoundary:c="viewport",elementContext:f="floating",altBoundary:h=!1,padding:d=0}=Qt(t,e),_=Pa(d),y=l[h?f==="floating"?"reference":"floating":f],g=Jn(await r.getClippingRect({element:(n=await(r.isElement==null?void 0:r.isElement(y)))==null||n?y:y.contextElement||await(r.getDocumentElement==null?void 0:r.getDocumentElement(l.floating)),boundary:a,rootBoundary:c,strategy:u})),x=f==="floating"?{x:o,y:s,width:i.floating.width,height:i.floating.height}:i.reference,D=await(r.getOffsetParent==null?void 0:r.getOffsetParent(l.floating)),b=await(r.isElement==null?void 0:r.isElement(D))?await(r.getScale==null?void 0:r.getScale(D))||{x:1,y:1}:{x:1,y:1},P=Jn(r.convertOffsetParentRelativeRectToViewportRelativeRect?await r.convertOffsetParentRelativeRectToViewportRelativeRect({elements:l,rect:x,offsetParent:D,strategy:u}):x);return{top:(g.top-P.top+_.top)/b.y,bottom:(P.bottom-g.bottom+_.bottom)/b.y,left:(g.left-P.left+_.left)/b.x,right:(P.right-g.right+_.right)/b.x}}const Dm=e=>({name:"arrow",options:e,async fn(t){const{x:n,y:o,placement:s,rects:r,platform:i,elements:l,middlewareData:u}=t,{element:a,padding:c=0}=Qt(e,t)||{};if(a==null)return{};const f=Pa(c),h={x:n,y:o},d=$r(s),_=Lr(d),v=await i.getDimensions(a),y=d==="y",g=y?"top":"left",x=y?"bottom":"right",D=y?"clientHeight":"clientWidth",b=r.reference[_]+r.reference[d]-h[d]-r.floating[_],P=h[d]-r.reference[d],U=await(i.getOffsetParent==null?void 0:i.getOffsetParent(a));let H=U?U[D]:0;(!H||!await(i.isElement==null?void 0:i.isElement(U)))&&(H=l.floating[D]||r.floating[_]);const W=b/2-P/2,M=H/2-v[_]/2-1,I=Zn(f[g],M),R=Zn(f[x],M),F=I,z=H-v[_]-R,Q=H/2-v[_]/2+W,Ee=Vr(F,Q,z),Z=!u.arrow&&Ze(s)!=null&&Q!==Ee&&r.reference[_]/2-(Q<F?I:R)-v[_]/2<0,L=Z?Q<F?Q-F:Q-z:0;return{[d]:h[d]+L,data:{[d]:Ee,centerOffset:Q-Ee-L,...Z&&{alignmentOffset:L}},reset:Z}}});function Pm(e,t,n){return(e?[...n.filter(s=>Ze(s)===e),...n.filter(s=>Ze(s)!==e)]:n.filter(s=>ut(s)===s)).filter(s=>e?Ze(s)===e||(t?Xo(s)!==s:!1):!0)}const Im=function(e){return e===void 0&&(e={}),{name:"autoPlacement",options:e,async fn(t){var n,o,s;const{rects:r,middlewareData:i,placement:l,platform:u,elements:a}=t,{crossAxis:c=!1,alignment:f,allowedPlacements:h=Aa,autoAlignment:d=!0,..._}=Qt(e,t),v=f!==void 0||h===Aa?Pm(f||null,d,h):h,y=await Jo(t,_),g=((n=i.autoPlacement)==null?void 0:n.index)||0,x=v[g];if(x==null)return{};const D=Da(x,r,await(u.isRTL==null?void 0:u.isRTL(a.floating)));if(l!==x)return{reset:{placement:v[0]}};const b=[y[ut(x)],y[D[0]],y[D[1]]],P=[...((o=i.autoPlacement)==null?void 0:o.overflows)||[],{placement:x,overflows:b}],U=v[g+1];if(U)return{data:{index:g+1,overflows:P},reset:{placement:U}};const H=P.map(I=>{const R=Ze(I.placement);return[I.placement,R&&c?I.overflows.slice(0,2).reduce((F,z)=>F+z,0):I.overflows[0],I.overflows]}).sort((I,R)=>I[1]-R[1]),M=((s=H.filter(I=>I[2].slice(0,Ze(I[0])?2:3).every(R=>R<=0))[0])==null?void 0:s[0])||H[0][0];return M!==l?{data:{index:g+1,overflows:P},reset:{placement:M}}:{}}}},Rm=function(e){return e===void 0&&(e={}),{name:"flip",options:e,async fn(t){var n,o;const{placement:s,middlewareData:r,rects:i,initialPlacement:l,platform:u,elements:a}=t,{mainAxis:c=!0,crossAxis:f=!0,fallbackPlacements:h,fallbackStrategy:d="bestFit",fallbackAxisSideDirection:_="none",flipAlignment:v=!0,...y}=Qt(e,t);if((n=r.arrow)!=null&&n.alignmentOffset)return{};const g=ut(s),x=en(l),D=ut(l)===l,b=await(u.isRTL==null?void 0:u.isRTL(a.floating)),P=h||(D||!v?[Zo(l)]:xm(l)),U=_!=="none";!h&&U&&P.push(...Om(l,v,_,b));const H=[l,...P],W=await Jo(t,y),M=[];let I=((o=r.flip)==null?void 0:o.overflows)||[];if(c&&M.push(W[g]),f){const Q=Da(s,i,b);M.push(W[Q[0]],W[Q[1]])}if(I=[...I,{placement:s,overflows:M}],!M.every(Q=>Q<=0)){var R,F;const Q=(((R=r.flip)==null?void 0:R.index)||0)+1,Ee=H[Q];if(Ee)return{data:{index:Q,overflows:I},reset:{placement:Ee}};let Z=(F=I.filter(L=>L.overflows[0]<=0).sort((L,X)=>L.overflows[1]-X.overflows[1])[0])==null?void 0:F.placement;if(!Z)switch(d){case"bestFit":{var z;const L=(z=I.filter(X=>{if(U){const ue=en(X.placement);return ue===x||ue==="y"}return!0}).map(X=>[X.placement,X.overflows.filter(ue=>ue>0).reduce((ue,Ue)=>ue+Ue,0)]).sort((X,ue)=>X[1]-ue[1])[0])==null?void 0:z[0];L&&(Z=L);break}case"initialPlacement":Z=l;break}if(s!==Z)return{reset:{placement:Z}}}return{}}}};async function km(e,t){const{placement:n,platform:o,elements:s}=e,r=await(o.isRTL==null?void 0:o.isRTL(s.floating)),i=ut(n),l=Ze(n),u=en(n)==="y",a=["left","top"].includes(i)?-1:1,c=r&&u?-1:1,f=Qt(t,e);let{mainAxis:h,crossAxis:d,alignmentAxis:_}=typeof f=="number"?{mainAxis:f,crossAxis:0,alignmentAxis:null}:{mainAxis:f.mainAxis||0,crossAxis:f.crossAxis||0,alignmentAxis:f.alignmentAxis};return l&&typeof _=="number"&&(d=l==="end"?_*-1:_),u?{x:d*c,y:h*a}:{x:h*a,y:d*c}}const Nm=function(e){return e===void 0&&(e=0),{name:"offset",options:e,async fn(t){var n,o;const{x:s,y:r,placement:i,middlewareData:l}=t,u=await km(t,e);return i===((n=l.offset)==null?void 0:n.placement)&&(o=l.arrow)!=null&&o.alignmentOffset?{}:{x:s+u.x,y:r+u.y,data:{...u,placement:i}}}}},Vm=function(e){return e===void 0&&(e={}),{name:"shift",options:e,async fn(t){const{x:n,y:o,placement:s}=t,{mainAxis:r=!0,crossAxis:i=!1,limiter:l={fn:y=>{let{x:g,y:x}=y;return{x:g,y:x}}},...u}=Qt(e,t),a={x:n,y:o},c=await Jo(t,u),f=en(ut(s)),h=Ca(f);let d=a[h],_=a[f];if(r){const y=h==="y"?"top":"left",g=h==="y"?"bottom":"right",x=d+c[y],D=d-c[g];d=Vr(x,d,D)}if(i){const y=f==="y"?"top":"left",g=f==="y"?"bottom":"right",x=_+c[y],D=_-c[g];_=Vr(x,_,D)}const v=l.fn({...t,[h]:d,[f]:_});return{...v,data:{x:v.x-n,y:v.y-o,enabled:{[h]:r,[f]:i}}}}}},Lm=function(e){return e===void 0&&(e={}),{name:"size",options:e,async fn(t){var n,o;const{placement:s,rects:r,platform:i,elements:l}=t,{apply:u=()=>{},...a}=Qt(e,t),c=await Jo(t,a),f=ut(s),h=Ze(s),d=en(s)==="y",{width:_,height:v}=r.floating;let y,g;f==="top"||f==="bottom"?(y=f,g=h===(await(i.isRTL==null?void 0:i.isRTL(l.floating))?"start":"end")?"left":"right"):(g=f,y=h==="end"?"top":"bottom");const x=v-c.top-c.bottom,D=_-c.left-c.right,b=Zn(v-c[y],x),P=Zn(_-c[g],D),U=!t.middlewareData.shift;let H=b,W=P;if((n=t.middlewareData.shift)!=null&&n.enabled.x&&(W=D),(o=t.middlewareData.shift)!=null&&o.enabled.y&&(H=x),U&&!h){const I=Jt(c.left,0),R=Jt(c.right,0),F=Jt(c.top,0),z=Jt(c.bottom,0);d?W=_-2*(I!==0||R!==0?I+R:Jt(c.left,c.right)):H=v-2*(F!==0||z!==0?F+z:Jt(c.top,c.bottom))}await u({...t,availableWidth:W,availableHeight:H});const M=await i.getDimensions(l.floating);return _!==M.width||v!==M.height?{reset:{rects:!0}}:{}}}};function je(e){var t;return((t=e.ownerDocument)==null?void 0:t.defaultView)||window}function at(e){return je(e).getComputedStyle(e)}const Ra=Math.min,Qn=Math.max,Qo=Math.round;function ka(e){const t=at(e);let n=parseFloat(t.width),o=parseFloat(t.height);const s=e.offsetWidth,r=e.offsetHeight,i=Qo(n)!==s||Qo(o)!==r;return i&&(n=s,o=r),{width:n,height:o,fallback:i}}function Lt(e){return Va(e)?(e.nodeName||"").toLowerCase():""}let es;function Na(){if(es)return es;const e=navigator.userAgentData;return e&&Array.isArray(e.brands)?(es=e.brands.map(t=>t.brand+"/"+t.version).join(" "),es):navigator.userAgent}function ct(e){return e instanceof je(e).HTMLElement}function $t(e){return e instanceof je(e).Element}function Va(e){return e instanceof je(e).Node}function La(e){return typeof ShadowRoot>"u"?!1:e instanceof je(e).ShadowRoot||e instanceof ShadowRoot}function ts(e){const{overflow:t,overflowX:n,overflowY:o,display:s}=at(e);return/auto|scroll|overlay|hidden|clip/.test(t+o+n)&&!["inline","contents"].includes(s)}function $m(e){return["table","td","th"].includes(Lt(e))}function Mr(e){const t=/firefox/i.test(Na()),n=at(e),o=n.backdropFilter||n.WebkitBackdropFilter;return n.transform!=="none"||n.perspective!=="none"||!!o&&o!=="none"||t&&n.willChange==="filter"||t&&!!n.filter&&n.filter!=="none"||["transform","perspective"].some(s=>n.willChange.includes(s))||["paint","layout","strict","content"].some(s=>{const r=n.contain;return r!=null&&r.includes(s)})}function $a(){return!/^((?!chrome|android).)*safari/i.test(Na())}function Ur(e){return["html","body","#document"].includes(Lt(e))}function Ma(e){return $t(e)?e:e.contextElement}const Ua={x:1,y:1};function yn(e){const t=Ma(e);if(!ct(t))return Ua;const n=t.getBoundingClientRect(),{width:o,height:s,fallback:r}=ka(t);let i=(r?Qo(n.width):n.width)/o,l=(r?Qo(n.height):n.height)/s;return i&&Number.isFinite(i)||(i=1),l&&Number.isFinite(l)||(l=1),{x:i,y:l}}function eo(e,t,n,o){var s,r;t===void 0&&(t=!1),n===void 0&&(n=!1);const i=e.getBoundingClientRect(),l=Ma(e);let u=Ua;t&&(o?$t(o)&&(u=yn(o)):u=yn(e));const a=l?je(l):window,c=!$a()&&n;let f=(i.left+(c&&((s=a.visualViewport)==null?void 0:s.offsetLeft)||0))/u.x,h=(i.top+(c&&((r=a.visualViewport)==null?void 0:r.offsetTop)||0))/u.y,d=i.width/u.x,_=i.height/u.y;if(l){const v=je(l),y=o&&$t(o)?je(o):o;let g=v.frameElement;for(;g&&o&&y!==v;){const x=yn(g),D=g.getBoundingClientRect(),b=getComputedStyle(g);D.x+=(g.clientLeft+parseFloat(b.paddingLeft))*x.x,D.y+=(g.clientTop+parseFloat(b.paddingTop))*x.y,f*=x.x,h*=x.y,d*=x.x,_*=x.y,f+=D.x,h+=D.y,g=je(g).frameElement}}return{width:d,height:_,top:h,right:f+d,bottom:h+_,left:f,x:f,y:h}}function Mt(e){return((Va(e)?e.ownerDocument:e.document)||window.document).documentElement}function ns(e){return $t(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.pageXOffset,scrollTop:e.pageYOffset}}function Fa(e){return eo(Mt(e)).left+ns(e).scrollLeft}function to(e){if(Lt(e)==="html")return e;const t=e.assignedSlot||e.parentNode||La(e)&&e.host||Mt(e);return La(t)?t.host:t}function Ba(e){const t=to(e);return Ur(t)?t.ownerDocument.body:ct(t)&&ts(t)?t:Ba(t)}function os(e,t){var n;t===void 0&&(t=[]);const o=Ba(e),s=o===((n=e.ownerDocument)==null?void 0:n.body),r=je(o);return s?t.concat(r,r.visualViewport||[],ts(o)?o:[]):t.concat(o,os(o))}function Ha(e,t,n){return t==="viewport"?Jn(function(o,s){const r=je(o),i=Mt(o),l=r.visualViewport;let u=i.clientWidth,a=i.clientHeight,c=0,f=0;if(l){u=l.width,a=l.height;const h=$a();(h||!h&&s==="fixed")&&(c=l.offsetLeft,f=l.offsetTop)}return{width:u,height:a,x:c,y:f}}(e,n)):$t(t)?Jn(function(o,s){const r=eo(o,!0,s==="fixed"),i=r.top+o.clientTop,l=r.left+o.clientLeft,u=ct(o)?yn(o):{x:1,y:1};return{width:o.clientWidth*u.x,height:o.clientHeight*u.y,x:l*u.x,y:i*u.y}}(t,n)):Jn(function(o){const s=Mt(o),r=ns(o),i=o.ownerDocument.body,l=Qn(s.scrollWidth,s.clientWidth,i.scrollWidth,i.clientWidth),u=Qn(s.scrollHeight,s.clientHeight,i.scrollHeight,i.clientHeight);let a=-r.scrollLeft+Fa(o);const c=-r.scrollTop;return at(i).direction==="rtl"&&(a+=Qn(s.clientWidth,i.clientWidth)-l),{width:l,height:u,x:a,y:c}}(Mt(e)))}function za(e){return ct(e)&&at(e).position!=="fixed"?e.offsetParent:null}function ja(e){const t=je(e);let n=za(e);for(;n&&$m(n)&&at(n).position==="static";)n=za(n);return n&&(Lt(n)==="html"||Lt(n)==="body"&&at(n).position==="static"&&!Mr(n))?t:n||function(o){let s=to(o);for(;ct(s)&&!Ur(s);){if(Mr(s))return s;s=to(s)}return null}(e)||t}function Mm(e,t,n){const o=ct(t),s=Mt(t),r=eo(e,!0,n==="fixed",t);let i={scrollLeft:0,scrollTop:0};const l={x:0,y:0};if(o||!o&&n!=="fixed")if((Lt(t)!=="body"||ts(s))&&(i=ns(t)),ct(t)){const u=eo(t,!0);l.x=u.x+t.clientLeft,l.y=u.y+t.clientTop}else s&&(l.x=Fa(s));return{x:r.left+i.scrollLeft-l.x,y:r.top+i.scrollTop-l.y,width:r.width,height:r.height}}const Um={getClippingRect:function(e){let{element:t,boundary:n,rootBoundary:o,strategy:s}=e;const r=n==="clippingAncestors"?function(a,c){const f=c.get(a);if(f)return f;let h=os(a).filter(y=>$t(y)&&Lt(y)!=="body"),d=null;const _=at(a).position==="fixed";let v=_?to(a):a;for(;$t(v)&&!Ur(v);){const y=at(v),g=Mr(v);(_?g||d:g||y.position!=="static"||!d||!["absolute","fixed"].includes(d.position))?d=y:h=h.filter(x=>x!==v),v=to(v)}return c.set(a,h),h}(t,this._c):[].concat(n),i=[...r,o],l=i[0],u=i.reduce((a,c)=>{const f=Ha(t,c,s);return a.top=Qn(f.top,a.top),a.right=Ra(f.right,a.right),a.bottom=Ra(f.bottom,a.bottom),a.left=Qn(f.left,a.left),a},Ha(t,l,s));return{width:u.right-u.left,height:u.bottom-u.top,x:u.left,y:u.top}},convertOffsetParentRelativeRectToViewportRelativeRect:function(e){let{rect:t,offsetParent:n,strategy:o}=e;const s=ct(n),r=Mt(n);if(n===r)return t;let i={scrollLeft:0,scrollTop:0},l={x:1,y:1};const u={x:0,y:0};if((s||!s&&o!=="fixed")&&((Lt(n)!=="body"||ts(r))&&(i=ns(n)),ct(n))){const a=eo(n);l=yn(n),u.x=a.x+n.clientLeft,u.y=a.y+n.clientTop}return{width:t.width*l.x,height:t.height*l.y,x:t.x*l.x-i.scrollLeft*l.x+u.x,y:t.y*l.y-i.scrollTop*l.y+u.y}},isElement:$t,getDimensions:function(e){return ct(e)?ka(e):e.getBoundingClientRect()},getOffsetParent:ja,getDocumentElement:Mt,getScale:yn,async getElementRects(e){let{reference:t,floating:n,strategy:o}=e;const s=this.getOffsetParent||ja,r=this.getDimensions;return{reference:Mm(t,await s(n),o),floating:{x:0,y:0,...await r(n)}}},getClientRects:e=>Array.from(e.getClientRects()),isRTL:e=>at(e).direction==="rtl"},Fm=(e,t,n)=>{const o=new Map,s={platform:Um,...n},r={...s.platform,_c:o};return Cm(e,t,{...s,platform:r})},tn={disabled:!1,distance:5,skidding:0,container:"body",boundary:void 0,instantMove:!1,disposeTimeout:150,popperTriggers:[],strategy:"absolute",preventOverflow:!0,flip:!0,shift:!0,overflowPadding:0,arrowPadding:0,arrowOverflow:!0,autoHideOnMousedown:!1,themes:{tooltip:{placement:"top",triggers:["hover","focus","touch"],hideTriggers:e=>[...e,"click"],delay:{show:200,hide:0},handleResize:!1,html:!1,loadingContent:"..."},dropdown:{placement:"bottom",triggers:["click"],delay:0,handleResize:!0,autoHide:!0},menu:{$extend:"dropdown",triggers:["hover","focus"],popperTriggers:["hover"],delay:{show:0,hide:400}}}};function Fr(e,t){let n=tn.themes[e]||{},o;do o=n[t],typeof o>"u"?n.$extend?n=tn.themes[n.$extend]||{}:(n=null,o=tn[t]):n=null;while(n);return o}function Bm(e){const t=[e];let n=tn.themes[e]||{};do n.$extend&&!n.$resetCss?(t.push(n.$extend),n=tn.themes[n.$extend]||{}):n=null;while(n);return t.map(o=>`v-popper--theme-${o}`)}function Ka(e){const t=[e];let n=tn.themes[e]||{};do n.$extend?(t.push(n.$extend),n=tn.themes[n.$extend]||{}):n=null;while(n);return t}let no=!1;if(typeof window<"u"){no=!1;try{const e=Object.defineProperty({},"passive",{get(){no=!0}});window.addEventListener("test",null,e)}catch{}}let Wa=!1;typeof window<"u"&&typeof navigator<"u"&&(Wa=/iPad|iPhone|iPod/.test(navigator.userAgent)&&!window.MSStream);const Hm=["auto","top","bottom","left","right"].reduce((e,t)=>e.concat([t,`${t}-start`,`${t}-end`]),[]),Ga={hover:"mouseenter",focus:"focus",click:"click",touch:"touchstart",pointer:"pointerdown"},Ya={hover:"mouseleave",focus:"blur",click:"click",touch:"touchend",pointer:"pointerup"};function qa(e,t){const n=e.indexOf(t);n!==-1&&e.splice(n,1)}function Br(){return new Promise(e=>requestAnimationFrame(()=>{requestAnimationFrame(e)}))}const Je=[];let nn=null;const Xa={};function Za(e){let t=Xa[e];return t||(t=Xa[e]=[]),t}let Hr=function(){};typeof window<"u"&&(Hr=window.Element);function ee(e){return function(t){return Fr(t.theme,e)}}const zr="__floating-vue__popper",Ja=()=>cn({name:"VPopper",provide(){return{[zr]:{parentPopper:this}}},inject:{[zr]:{default:null}},props:{theme:{type:String,required:!0},targetNodes:{type:Function,required:!0},referenceNode:{type:Function,default:null},popperNode:{type:Function,required:!0},shown:{type:Boolean,default:!1},showGroup:{type:String,default:null},ariaId:{default:null},disabled:{type:Boolean,default:ee("disabled")},positioningDisabled:{type:Boolean,default:ee("positioningDisabled")},placement:{type:String,default:ee("placement"),validator:e=>Hm.includes(e)},delay:{type:[String,Number,Object],default:ee("delay")},distance:{type:[Number,String],default:ee("distance")},skidding:{type:[Number,String],default:ee("skidding")},triggers:{type:Array,default:ee("triggers")},showTriggers:{type:[Array,Function],default:ee("showTriggers")},hideTriggers:{type:[Array,Function],default:ee("hideTriggers")},popperTriggers:{type:Array,default:ee("popperTriggers")},popperShowTriggers:{type:[Array,Function],default:ee("popperShowTriggers")},popperHideTriggers:{type:[Array,Function],default:ee("popperHideTriggers")},container:{type:[String,Object,Hr,Boolean],default:ee("container")},boundary:{type:[String,Hr],default:ee("boundary")},strategy:{type:String,validator:e=>["absolute","fixed"].includes(e),default:ee("strategy")},autoHide:{type:[Boolean,Function],default:ee("autoHide")},handleResize:{type:Boolean,default:ee("handleResize")},instantMove:{type:Boolean,default:ee("instantMove")},eagerMount:{type:Boolean,default:ee("eagerMount")},popperClass:{type:[String,Array,Object],default:ee("popperClass")},computeTransformOrigin:{type:Boolean,default:ee("computeTransformOrigin")},autoMinSize:{type:Boolean,default:ee("autoMinSize")},autoSize:{type:[Boolean,String],default:ee("autoSize")},autoMaxSize:{type:Boolean,default:ee("autoMaxSize")},autoBoundaryMaxSize:{type:Boolean,default:ee("autoBoundaryMaxSize")},preventOverflow:{type:Boolean,default:ee("preventOverflow")},overflowPadding:{type:[Number,String],default:ee("overflowPadding")},arrowPadding:{type:[Number,String],default:ee("arrowPadding")},arrowOverflow:{type:Boolean,default:ee("arrowOverflow")},flip:{type:Boolean,default:ee("flip")},shift:{type:Boolean,default:ee("shift")},shiftCrossAxis:{type:Boolean,default:ee("shiftCrossAxis")},noAutoFocus:{type:Boolean,default:ee("noAutoFocus")},disposeTimeout:{type:Number,default:ee("disposeTimeout")}},emits:{show:()=>!0,hide:()=>!0,"update:shown":e=>!0,"apply-show":()=>!0,"apply-hide":()=>!0,"close-group":()=>!0,"close-directive":()=>!0,"auto-hide":()=>!0,resize:()=>!0},data(){return{isShown:!1,isMounted:!1,skipTransition:!1,classes:{showFrom:!1,showTo:!1,hideFrom:!1,hideTo:!0},result:{x:0,y:0,placement:"",strategy:this.strategy,arrow:{x:0,y:0,centerOffset:0},transformOrigin:null},randomId:`popper_${[Math.random(),Date.now()].map(e=>e.toString(36).substring(2,10)).join("_")}`,shownChildren:new Set,lastAutoHide:!0,pendingHide:!1,containsGlobalTarget:!1,isDisposed:!0,mouseDownContains:!1}},computed:{popperId(){return this.ariaId!=null?this.ariaId:this.randomId},shouldMountContent(){return this.eagerMount||this.isMounted},slotData(){return{popperId:this.popperId,isShown:this.isShown,shouldMountContent:this.shouldMountContent,skipTransition:this.skipTransition,autoHide:typeof this.autoHide=="function"?this.lastAutoHide:this.autoHide,show:this.show,hide:this.hide,handleResize:this.handleResize,onResize:this.onResize,classes:{...this.classes,popperClass:this.popperClass},result:this.positioningDisabled?null:this.result,attrs:this.$attrs}},parentPopper(){var e;return(e=this[zr])==null?void 0:e.parentPopper},hasPopperShowTriggerHover(){var e,t;return((e=this.popperTriggers)==null?void 0:e.includes("hover"))||((t=this.popperShowTriggers)==null?void 0:t.includes("hover"))}},watch:{shown:"$_autoShowHide",disabled(e){e?this.dispose():this.init()},async container(){this.isShown&&(this.$_ensureTeleport(),await this.$_computePosition())},triggers:{handler:"$_refreshListeners",deep:!0},positioningDisabled:"$_refreshListeners",...["placement","distance","skidding","boundary","strategy","overflowPadding","arrowPadding","preventOverflow","shift","shiftCrossAxis","flip"].reduce((e,t)=>(e[t]="$_computePosition",e),{})},created(){this.autoMinSize&&console.warn('[floating-vue] `autoMinSize` option is deprecated. Use `autoSize="min"` instead.'),this.autoMaxSize&&console.warn("[floating-vue] `autoMaxSize` option is deprecated. Use `autoBoundaryMaxSize` instead.")},mounted(){this.init(),this.$_detachPopperNode()},activated(){this.$_autoShowHide()},deactivated(){this.hide()},beforeUnmount(){this.dispose()},methods:{show({event:e=null,skipDelay:t=!1,force:n=!1}={}){var o,s;(o=this.parentPopper)!=null&&o.lockedChild&&this.parentPopper.lockedChild!==this||(this.pendingHide=!1,(n||!this.disabled)&&(((s=this.parentPopper)==null?void 0:s.lockedChild)===this&&(this.parentPopper.lockedChild=null),this.$_scheduleShow(e,t),this.$emit("show"),this.$_showFrameLocked=!0,requestAnimationFrame(()=>{this.$_showFrameLocked=!1})),this.$emit("update:shown",!0))},hide({event:e=null,skipDelay:t=!1}={}){var n;if(!this.$_hideInProgress){if(this.shownChildren.size>0){this.pendingHide=!0;return}if(this.hasPopperShowTriggerHover&&this.$_isAimingPopper()){this.parentPopper&&(this.parentPopper.lockedChild=this,clearTimeout(this.parentPopper.lockedChildTimer),this.parentPopper.lockedChildTimer=setTimeout(()=>{this.parentPopper.lockedChild===this&&(this.parentPopper.lockedChild.hide({skipDelay:t}),this.parentPopper.lockedChild=null)},1e3));return}((n=this.parentPopper)==null?void 0:n.lockedChild)===this&&(this.parentPopper.lockedChild=null),this.pendingHide=!1,this.$_scheduleHide(e,t),this.$emit("hide"),this.$emit("update:shown",!1)}},init(){var e;this.isDisposed&&(this.isDisposed=!1,this.isMounted=!1,this.$_events=[],this.$_preventShow=!1,this.$_referenceNode=((e=this.referenceNode)==null?void 0:e.call(this))??this.$el,this.$_targetNodes=this.targetNodes().filter(t=>t.nodeType===t.ELEMENT_NODE),this.$_popperNode=this.popperNode(),this.$_innerNode=this.$_popperNode.querySelector(".v-popper__inner"),this.$_arrowNode=this.$_popperNode.querySelector(".v-popper__arrow-container"),this.$_swapTargetAttrs("title","data-original-title"),this.$_detachPopperNode(),this.triggers.length&&this.$_addEventListeners(),this.shown&&this.show())},dispose(){this.isDisposed||(this.isDisposed=!0,this.$_removeEventListeners(),this.hide({skipDelay:!0}),this.$_detachPopperNode(),this.isMounted=!1,this.isShown=!1,this.$_updateParentShownChildren(!1),this.$_swapTargetAttrs("data-original-title","title"))},async onResize(){this.isShown&&(await this.$_computePosition(),this.$emit("resize"))},async $_computePosition(){if(this.isDisposed||this.positioningDisabled)return;const e={strategy:this.strategy,middleware:[]};(this.distance||this.skidding)&&e.middleware.push(Nm({mainAxis:this.distance,crossAxis:this.skidding}));const t=this.placement.startsWith("auto");if(t?e.middleware.push(Im({alignment:this.placement.split("-")[1]??""})):e.placement=this.placement,this.preventOverflow&&(this.shift&&e.middleware.push(Vm({padding:this.overflowPadding,boundary:this.boundary,crossAxis:this.shiftCrossAxis})),!t&&this.flip&&e.middleware.push(Rm({padding:this.overflowPadding,boundary:this.boundary}))),e.middleware.push(Dm({element:this.$_arrowNode,padding:this.arrowPadding})),this.arrowOverflow&&e.middleware.push({name:"arrowOverflow",fn:({placement:o,rects:s,middlewareData:r})=>{let i;const{centerOffset:l}=r.arrow;return o.startsWith("top")||o.startsWith("bottom")?i=Math.abs(l)>s.reference.width/2:i=Math.abs(l)>s.reference.height/2,{data:{overflow:i}}}}),this.autoMinSize||this.autoSize){const o=this.autoSize?this.autoSize:this.autoMinSize?"min":null;e.middleware.push({name:"autoSize",fn:({rects:s,placement:r,middlewareData:i})=>{var l;if((l=i.autoSize)!=null&&l.skip)return{};let u,a;return r.startsWith("top")||r.startsWith("bottom")?u=s.reference.width:a=s.reference.height,this.$_innerNode.style[o==="min"?"minWidth":o==="max"?"maxWidth":"width"]=u!=null?`${u}px`:null,this.$_innerNode.style[o==="min"?"minHeight":o==="max"?"maxHeight":"height"]=a!=null?`${a}px`:null,{data:{skip:!0},reset:{rects:!0}}}})}(this.autoMaxSize||this.autoBoundaryMaxSize)&&(this.$_innerNode.style.maxWidth=null,this.$_innerNode.style.maxHeight=null,e.middleware.push(Lm({boundary:this.boundary,padding:this.overflowPadding,apply:({availableWidth:o,availableHeight:s})=>{this.$_innerNode.style.maxWidth=o!=null?`${o}px`:null,this.$_innerNode.style.maxHeight=s!=null?`${s}px`:null}})));const n=await Fm(this.$_referenceNode,this.$_popperNode,e);Object.assign(this.result,{x:n.x,y:n.y,placement:n.placement,strategy:n.strategy,arrow:{...n.middlewareData.arrow,...n.middlewareData.arrowOverflow}})},$_scheduleShow(e,t=!1){if(this.$_updateParentShownChildren(!0),this.$_hideInProgress=!1,clearTimeout(this.$_scheduleTimer),nn&&this.instantMove&&nn.instantMove&&nn!==this.parentPopper){nn.$_applyHide(!0),this.$_applyShow(!0);return}t?this.$_applyShow():this.$_scheduleTimer=setTimeout(this.$_applyShow.bind(this),this.$_computeDelay("show"))},$_scheduleHide(e,t=!1){if(this.shownChildren.size>0){this.pendingHide=!0;return}this.$_updateParentShownChildren(!1),this.$_hideInProgress=!0,clearTimeout(this.$_scheduleTimer),this.isShown&&(nn=this),t?this.$_applyHide():this.$_scheduleTimer=setTimeout(this.$_applyHide.bind(this),this.$_computeDelay("hide"))},$_computeDelay(e){const t=this.delay;return parseInt(t&&t[e]||t||0)},async $_applyShow(e=!1){clearTimeout(this.$_disposeTimer),clearTimeout(this.$_scheduleTimer),this.skipTransition=e,!this.isShown&&(this.$_ensureTeleport(),await Br(),await this.$_computePosition(),await this.$_applyShowEffect(),this.positioningDisabled||this.$_registerEventListeners([...os(this.$_referenceNode),...os(this.$_popperNode)],"scroll",()=>{this.$_computePosition()}))},async $_applyShowEffect(){if(this.$_hideInProgress)return;if(this.computeTransformOrigin){const t=this.$_referenceNode.getBoundingClientRect(),n=this.$_popperNode.querySelector(".v-popper__wrapper"),o=n.parentNode.getBoundingClientRect(),s=t.x+t.width/2-(o.left+n.offsetLeft),r=t.y+t.height/2-(o.top+n.offsetTop);this.result.transformOrigin=`${s}px ${r}px`}this.isShown=!0,this.$_applyAttrsToTarget({"aria-describedby":this.popperId,"data-popper-shown":""});const e=this.showGroup;if(e){let t;for(let n=0;n<Je.length;n++)t=Je[n],t.showGroup!==e&&(t.hide(),t.$emit("close-group"))}Je.push(this),document.body.classList.add("v-popper--some-open");for(const t of Ka(this.theme))Za(t).push(this),document.body.classList.add(`v-popper--some-open--${t}`);this.$emit("apply-show"),this.classes.showFrom=!0,this.classes.showTo=!1,this.classes.hideFrom=!1,this.classes.hideTo=!1,await Br(),this.classes.showFrom=!1,this.classes.showTo=!0,this.noAutoFocus||this.$_popperNode.focus()},async $_applyHide(e=!1){if(this.shownChildren.size>0){this.pendingHide=!0,this.$_hideInProgress=!1;return}if(clearTimeout(this.$_scheduleTimer),!this.isShown)return;this.skipTransition=e,qa(Je,this),Je.length===0&&document.body.classList.remove("v-popper--some-open");for(const n of Ka(this.theme)){const o=Za(n);qa(o,this),o.length===0&&document.body.classList.remove(`v-popper--some-open--${n}`)}nn===this&&(nn=null),this.isShown=!1,this.$_applyAttrsToTarget({"aria-describedby":void 0,"data-popper-shown":void 0}),clearTimeout(this.$_disposeTimer);const t=this.disposeTimeout;t!==null&&(this.$_disposeTimer=setTimeout(()=>{this.$_popperNode&&(this.$_detachPopperNode(),this.isMounted=!1)},t)),this.$_removeEventListeners("scroll"),this.$emit("apply-hide"),this.classes.showFrom=!1,this.classes.showTo=!1,this.classes.hideFrom=!0,this.classes.hideTo=!1,await Br(),this.classes.hideFrom=!1,this.classes.hideTo=!0},$_autoShowHide(){this.shown?this.show():this.hide()},$_ensureTeleport(){if(this.isDisposed)return;let e=this.container;if(typeof e=="string"?e=window.document.querySelector(e):e===!1&&(e=this.$_targetNodes[0].parentNode),!e)throw new Error("No container for popover: "+this.container);e.appendChild(this.$_popperNode),this.isMounted=!0},$_addEventListeners(){const e=n=>{this.isShown&&!this.$_hideInProgress||(n.usedByTooltip=!0,!this.$_preventShow&&this.show({event:n}))};this.$_registerTriggerListeners(this.$_targetNodes,Ga,this.triggers,this.showTriggers,e),this.$_registerTriggerListeners([this.$_popperNode],Ga,this.popperTriggers,this.popperShowTriggers,e);const t=n=>{n.usedByTooltip||this.hide({event:n})};this.$_registerTriggerListeners(this.$_targetNodes,Ya,this.triggers,this.hideTriggers,t),this.$_registerTriggerListeners([this.$_popperNode],Ya,this.popperTriggers,this.popperHideTriggers,t)},$_registerEventListeners(e,t,n){this.$_events.push({targetNodes:e,eventType:t,handler:n}),e.forEach(o=>o.addEventListener(t,n,no?{passive:!0}:void 0))},$_registerTriggerListeners(e,t,n,o,s){let r=n;o!=null&&(r=typeof o=="function"?o(r):o),r.forEach(i=>{const l=t[i];l&&this.$_registerEventListeners(e,l,s)})},$_removeEventListeners(e){const t=[];this.$_events.forEach(n=>{const{targetNodes:o,eventType:s,handler:r}=n;!e||e===s?o.forEach(i=>i.removeEventListener(s,r)):t.push(n)}),this.$_events=t},$_refreshListeners(){this.isDisposed||(this.$_removeEventListeners(),this.$_addEventListeners())},$_handleGlobalClose(e,t=!1){this.$_showFrameLocked||(this.hide({event:e}),e.closePopover?this.$emit("close-directive"):this.$emit("auto-hide"),t&&(this.$_preventShow=!0,setTimeout(()=>{this.$_preventShow=!1},300)))},$_detachPopperNode(){this.$_popperNode.parentNode&&this.$_popperNode.parentNode.removeChild(this.$_popperNode)},$_swapTargetAttrs(e,t){for(const n of this.$_targetNodes){const o=n.getAttribute(e);o&&(n.removeAttribute(e),n.setAttribute(t,o))}},$_applyAttrsToTarget(e){for(const t of this.$_targetNodes)for(const n in e){const o=e[n];o==null?t.removeAttribute(n):t.setAttribute(n,o)}},$_updateParentShownChildren(e){let t=this.parentPopper;for(;t;)e?t.shownChildren.add(this.randomId):(t.shownChildren.delete(this.randomId),t.pendingHide&&t.hide()),t=t.parentPopper},$_isAimingPopper(){const e=this.$_referenceNode.getBoundingClientRect();if(oo>=e.left&&oo<=e.right&&so>=e.top&&so<=e.bottom){const t=this.$_popperNode.getBoundingClientRect(),n=oo-Ut,o=so-Ft,s=t.left+t.width/2-Ut+(t.top+t.height/2)-Ft+t.width+t.height,r=Ut+n*s,i=Ft+o*s;return ss(Ut,Ft,r,i,t.left,t.top,t.left,t.bottom)||ss(Ut,Ft,r,i,t.left,t.top,t.right,t.top)||ss(Ut,Ft,r,i,t.right,t.top,t.right,t.bottom)||ss(Ut,Ft,r,i,t.left,t.bottom,t.right,t.bottom)}return!1}},render(){return this.$slots.default(this.slotData)}});if(typeof document<"u"&&typeof window<"u"){if(Wa){const e=no?{passive:!0,capture:!0}:!0;document.addEventListener("touchstart",t=>Qa(t),e),document.addEventListener("touchend",t=>ec(t,!0),e)}else window.addEventListener("mousedown",e=>Qa(e),!0),window.addEventListener("click",e=>ec(e,!1),!0);window.addEventListener("resize",Km)}function Qa(e,t){for(let n=0;n<Je.length;n++){const o=Je[n];try{o.mouseDownContains=o.popperNode().contains(e.target)}catch{}}}function ec(e,t){zm(e,t)}function zm(e,t){const n={};for(let o=Je.length-1;o>=0;o--){const s=Je[o];try{const r=s.containsGlobalTarget=s.mouseDownContains||s.popperNode().contains(e.target);s.pendingHide=!1,requestAnimationFrame(()=>{if(s.pendingHide=!1,!n[s.randomId]&&tc(s,r,e)){if(s.$_handleGlobalClose(e,t),!e.closeAllPopover&&e.closePopover&&r){let l=s.parentPopper;for(;l;)n[l.randomId]=!0,l=l.parentPopper;return}let i=s.parentPopper;for(;i&&tc(i,i.containsGlobalTarget,e);)i.$_handleGlobalClose(e,t),i=i.parentPopper}})}catch{}}}function tc(e,t,n){return n.closeAllPopover||n.closePopover&&t||jm(e,n)&&!t}function jm(e,t){if(typeof e.autoHide=="function"){const n=e.autoHide(t);return e.lastAutoHide=n,n}return e.autoHide}function Km(){for(let e=0;e<Je.length;e++)Je[e].$_computePosition()}let Ut=0,Ft=0,oo=0,so=0;typeof window<"u"&&window.addEventListener("mousemove",e=>{Ut=oo,Ft=so,oo=e.clientX,so=e.clientY},no?{passive:!0}:void 0);function ss(e,t,n,o,s,r,i,l){const u=((i-s)*(t-r)-(l-r)*(e-s))/((l-r)*(n-e)-(i-s)*(o-t)),a=((n-e)*(t-r)-(o-t)*(e-s))/((l-r)*(n-e)-(i-s)*(o-t));return u>=0&&u<=1&&a>=0&&a<=1}const Wm={extends:Ja()},jr=(e,t)=>{const n=e.__vccOpts||e;for(const[o,s]of t)n[o]=s;return n};function Gm(e,t,n,o,s,r){return Re(),Ct("div",{ref:"reference",class:ht(["v-popper",{"v-popper--shown":e.slotData.isShown}])},[Ro(e.$slots,"default",Mc(fl(e.slotData)))],2)}const Ym=jr(Wm,[["render",Gm]]);function qm(){var e=window.navigator.userAgent,t=e.indexOf("MSIE ");if(t>0)return parseInt(e.substring(t+5,e.indexOf(".",t)),10);var n=e.indexOf("Trident/");if(n>0){var o=e.indexOf("rv:");return parseInt(e.substring(o+3,e.indexOf(".",o)),10)}var s=e.indexOf("Edge/");return s>0?parseInt(e.substring(s+5,e.indexOf(".",s)),10):-1}let rs;function Kr(){Kr.init||(Kr.init=!0,rs=qm()!==-1)}var is={name:"ResizeObserver",props:{emitOnMount:{type:Boolean,default:!1},ignoreWidth:{type:Boolean,default:!1},ignoreHeight:{type:Boolean,default:!1}},emits:["notify"],mounted(){Kr(),Oo(()=>{this._w=this.$el.offsetWidth,this._h=this.$el.offsetHeight,this.emitOnMount&&this.emitSize()});const e=document.createElement("object");this._resizeObject=e,e.setAttribute("aria-hidden","true"),e.setAttribute("tabindex",-1),e.onload=this.addResizeHandlers,e.type="text/html",rs&&this.$el.appendChild(e),e.data="about:blank",rs||this.$el.appendChild(e)},beforeUnmount(){this.removeResizeHandlers()},methods:{compareAndNotify(){(!this.ignoreWidth&&this._w!==this.$el.offsetWidth||!this.ignoreHeight&&this._h!==this.$el.offsetHeight)&&(this._w=this.$el.offsetWidth,this._h=this.$el.offsetHeight,this.emitSize())},emitSize(){this.$emit("notify",{width:this._w,height:this._h})},addResizeHandlers(){this._resizeObject.contentDocument.defaultView.addEventListener("resize",this.compareAndNotify),this.compareAndNotify()},removeResizeHandlers(){this._resizeObject&&this._resizeObject.onload&&(!rs&&this._resizeObject.contentDocument&&this._resizeObject.contentDocument.defaultView.removeEventListener("resize",this.compareAndNotify),this.$el.removeChild(this._resizeObject),this._resizeObject.onload=null,this._resizeObject=null)}}};const Xm=kf();If("data-v-b329ee4c");const Zm={class:"resize-observer",tabindex:"-1"};Rf();const Jm=Xm((e,t,n,o,s,r)=>(Re(),dn("div",Zm)));is.render=Jm,is.__scopeId="data-v-b329ee4c",is.__file="src/components/ResizeObserver.vue";const nc=(e="theme")=>({computed:{themeClass(){return Bm(this[e])}}}),Qm=cn({name:"VPopperContent",components:{ResizeObserver:is},mixins:[nc()],props:{popperId:String,theme:String,shown:Boolean,mounted:Boolean,skipTransition:Boolean,autoHide:Boolean,handleResize:Boolean,classes:Object,result:Object},emits:["hide","resize"],methods:{toPx(e){return e!=null&&!isNaN(e)?`${e}px`:null}}}),eg=["id","aria-hidden","tabindex","data-popper-placement"],tg={ref:"inner",class:"v-popper__inner"},ng=ie("div",{class:"v-popper__arrow-outer"},null,-1),og=ie("div",{class:"v-popper__arrow-inner"},null,-1),sg=[ng,og];function rg(e,t,n,o,s,r){const i=Bs("ResizeObserver");return Re(),Ct("div",{id:e.popperId,ref:"popover",class:ht(["v-popper__popper",[e.themeClass,e.classes.popperClass,{"v-popper__popper--shown":e.shown,"v-popper__popper--hidden":!e.shown,"v-popper__popper--show-from":e.classes.showFrom,"v-popper__popper--show-to":e.classes.showTo,"v-popper__popper--hide-from":e.classes.hideFrom,"v-popper__popper--hide-to":e.classes.hideTo,"v-popper__popper--skip-transition":e.skipTransition,"v-popper__popper--arrow-overflow":e.result&&e.result.arrow.overflow,"v-popper__popper--no-positioning":!e.result}]]),style:Ne(e.result?{position:e.result.strategy,transform:`translate3d(${Math.round(e.result.x)}px,${Math.round(e.result.y)}px,0)`}:void 0),"aria-hidden":e.shown?"false":"true",tabindex:e.autoHide?0:void 0,"data-popper-placement":e.result?e.result.placement:void 0,onKeyup:t[2]||(t[2]=up(l=>e.autoHide&&e.$emit("hide"),["esc"]))},[ie("div",{class:"v-popper__backdrop",onClick:t[0]||(t[0]=l=>e.autoHide&&e.$emit("hide"))}),ie("div",{class:"v-popper__wrapper",style:Ne(e.result?{transformOrigin:e.result.transformOrigin}:void 0)},[ie("div",tg,[e.mounted?(Re(),Ct(Ie,{key:0},[ie("div",null,[Ro(e.$slots,"default")]),e.handleResize?(Re(),dn(i,{key:0,onNotify:t[1]||(t[1]=l=>e.$emit("resize",l))})):$o("",!0)],64)):$o("",!0)],512),ie("div",{ref:"arrow",class:"v-popper__arrow-container",style:Ne(e.result?{left:e.toPx(e.result.arrow.x),top:e.toPx(e.result.arrow.y)}:void 0)},sg,4)],4)],46,eg)}const oc=jr(Qm,[["render",rg]]),sc={methods:{show(...e){return this.$refs.popper.show(...e)},hide(...e){return this.$refs.popper.hide(...e)},dispose(...e){return this.$refs.popper.dispose(...e)},onResize(...e){return this.$refs.popper.onResize(...e)}}};let Wr=function(){};typeof window<"u"&&(Wr=window.Element);const ig=cn({name:"VPopperWrapper",components:{Popper:Ym,PopperContent:oc},mixins:[sc,nc("finalTheme")],props:{theme:{type:String,default:null},referenceNode:{type:Function,default:null},shown:{type:Boolean,default:!1},showGroup:{type:String,default:null},ariaId:{default:null},disabled:{type:Boolean,default:void 0},positioningDisabled:{type:Boolean,default:void 0},placement:{type:String,default:void 0},delay:{type:[String,Number,Object],default:void 0},distance:{type:[Number,String],default:void 0},skidding:{type:[Number,String],default:void 0},triggers:{type:Array,default:void 0},showTriggers:{type:[Array,Function],default:void 0},hideTriggers:{type:[Array,Function],default:void 0},popperTriggers:{type:Array,default:void 0},popperShowTriggers:{type:[Array,Function],default:void 0},popperHideTriggers:{type:[Array,Function],default:void 0},container:{type:[String,Object,Wr,Boolean],default:void 0},boundary:{type:[String,Wr],default:void 0},strategy:{type:String,default:void 0},autoHide:{type:[Boolean,Function],default:void 0},handleResize:{type:Boolean,default:void 0},instantMove:{type:Boolean,default:void 0},eagerMount:{type:Boolean,default:void 0},popperClass:{type:[String,Array,Object],default:void 0},computeTransformOrigin:{type:Boolean,default:void 0},autoMinSize:{type:Boolean,default:void 0},autoSize:{type:[Boolean,String],default:void 0},autoMaxSize:{type:Boolean,default:void 0},autoBoundaryMaxSize:{type:Boolean,default:void 0},preventOverflow:{type:Boolean,default:void 0},overflowPadding:{type:[Number,String],default:void 0},arrowPadding:{type:[Number,String],default:void 0},arrowOverflow:{type:Boolean,default:void 0},flip:{type:Boolean,default:void 0},shift:{type:Boolean,default:void 0},shiftCrossAxis:{type:Boolean,default:void 0},noAutoFocus:{type:Boolean,default:void 0},disposeTimeout:{type:Number,default:void 0}},emits:{show:()=>!0,hide:()=>!0,"update:shown":e=>!0,"apply-show":()=>!0,"apply-hide":()=>!0,"close-group":()=>!0,"close-directive":()=>!0,"auto-hide":()=>!0,resize:()=>!0},computed:{finalTheme(){return this.theme??this.$options.vPopperTheme}},methods:{getTargetNodes(){return Array.from(this.$el.children).filter(e=>e!==this.$refs.popperContent.$el)}}});function lg(e,t,n,o,s,r){const i=Bs("PopperContent"),l=Bs("Popper");return Re(),dn(l,dl({ref:"popper"},e.$props,{theme:e.finalTheme,"target-nodes":e.getTargetNodes,"popper-node":()=>e.$refs.popperContent.$el,class:[e.themeClass],onShow:t[0]||(t[0]=()=>e.$emit("show")),onHide:t[1]||(t[1]=()=>e.$emit("hide")),"onUpdate:shown":t[2]||(t[2]=u=>e.$emit("update:shown",u)),onApplyShow:t[3]||(t[3]=()=>e.$emit("apply-show")),onApplyHide:t[4]||(t[4]=()=>e.$emit("apply-hide")),onCloseGroup:t[5]||(t[5]=()=>e.$emit("close-group")),onCloseDirective:t[6]||(t[6]=()=>e.$emit("close-directive")),onAutoHide:t[7]||(t[7]=()=>e.$emit("auto-hide")),onResize:t[8]||(t[8]=()=>e.$emit("resize"))}),{default:Do(({popperId:u,isShown:a,shouldMountContent:c,skipTransition:f,autoHide:h,show:d,hide:_,handleResize:v,onResize:y,classes:g,result:x})=>[Ro(e.$slots,"default",{shown:a,show:d,hide:_}),Se(i,{ref:"popperContent","popper-id":u,theme:e.finalTheme,shown:a,mounted:c,"skip-transition":f,"auto-hide":h,"handle-resize":v,classes:g,result:x,onHide:_,onResize:y},{default:Do(()=>[Ro(e.$slots,"popper",{shown:a,hide:_})]),_:2},1032,["popper-id","theme","shown","mounted","skip-transition","auto-hide","handle-resize","classes","result","onHide","onResize"])]),_:3},16,["theme","target-nodes","popper-node","class"])}const Gr=jr(ig,[["render",lg]]);({...Gr},{...Gr}),{...Gr},Ja();function rc(e){return oi()?(Hc(e),!0):!1}const Yr=new WeakMap,ug=(...e)=>{var t;const n=e[0],o=(t=Qs())==null?void 0:t.proxy;if(o==null&&!zi())throw new Error("injectLocal must be called in setup");return o&&Yr.has(o)&&n in Yr.get(o)?Yr.get(o)[n]:Nn(...e)},ic=typeof window<"u"&&typeof document<"u";typeof WorkerGlobalScope<"u"&&globalThis instanceof WorkerGlobalScope;const ag=e=>e!=null,cg=Object.prototype.toString,fg=e=>cg.call(e)==="[object Object]",qr=()=>{};function lc(e,t){function n(...o){return new Promise((s,r)=>{Promise.resolve(e(()=>t.apply(this,o),{fn:t,thisArg:this,args:o})).then(s).catch(r)})}return n}const uc=e=>e();function dg(e,t={}){let n,o,s=qr;const r=u=>{clearTimeout(u),s(),s=qr};let i;return u=>{const a=Ae(e),c=Ae(t.maxWait);return n&&r(n),a<=0||c!==void 0&&c<=0?(o&&(r(o),o=null),Promise.resolve(u())):new Promise((f,h)=>{s=t.rejectOnCancel?h:f,i=u,c&&!o&&(o=setTimeout(()=>{n&&r(n),o=null,f(i())},c)),n=setTimeout(()=>{o&&r(o),o=null,f(u())},a)})}}function pg(e=uc,t={}){const{initialState:n="active"}=t,o=cc(n==="active");function s(){o.value=!1}function r(){o.value=!0}const i=(...l)=>{o.value&&e(...l)};return{isActive:An(o),pause:s,resume:r,eventFilter:i}}function ac(e){return e.endsWith("rem")?Number.parseFloat(e)*16:Number.parseFloat(e)}function hg(e){return Qs()}function ls(e){return Array.isArray(e)?e:[e]}function cc(...e){if(e.length!==1)return gf(...e);const t=e[0];return typeof t=="function"?An(hf(()=>({get:t,set:qr}))):Ve(t)}function _g(e,t=200,n={}){return lc(dg(t,n),e)}function mg(e,t,n={}){const{eventFilter:o=uc,...s}=n;return Ye(e,lc(o,t),s)}function gg(e,t,n={}){const{eventFilter:o,initialState:s="active",...r}=n,{eventFilter:i,pause:l,resume:u,isActive:a}=pg(o,{initialState:s});return{stop:mg(e,t,{...r,eventFilter:i}),pause:l,resume:u,isActive:a}}function Xr(e,t=!0,n){hg()?In(e,n):t?e():Oo(e)}function vg(e,t,n){return Ye(e,t,{...n,immediate:!0})}const wt=ic?window:void 0;function us(e){var t;const n=Ae(e);return(t=n==null?void 0:n.$el)!=null?t:n}function Pe(...e){const t=[],n=()=>{t.forEach(l=>l()),t.length=0},o=(l,u,a,c)=>(l.addEventListener(u,a,c),()=>l.removeEventListener(u,a,c)),s=_e(()=>{const l=ls(Ae(e[0])).filter(u=>u!=null);return l.every(u=>typeof u!="string")?l:void 0}),r=vg(()=>{var l,u;return[(u=(l=s.value)==null?void 0:l.map(a=>us(a)))!=null?u:[wt].filter(a=>a!=null),ls(Ae(s.value?e[1]:e[0])),ls(J(s.value?e[2]:e[1])),Ae(s.value?e[3]:e[2])]},([l,u,a,c])=>{if(n(),!(l!=null&&l.length)||!(u!=null&&u.length)||!(a!=null&&a.length))return;const f=fg(c)?{...c}:c;t.push(...l.flatMap(h=>u.flatMap(d=>a.map(_=>o(h,d,_,f)))))},{flush:"post"}),i=()=>{r(),n()};return rc(n),i}function yg(){const e=Le(!1),t=Qs();return t&&In(()=>{e.value=!0},t),e}function fc(e){const t=yg();return _e(()=>(t.value,!!e()))}function Eg(e,t,n={}){const{window:o=wt,...s}=n;let r;const i=fc(()=>o&&"MutationObserver"in o),l=()=>{r&&(r.disconnect(),r=void 0)},u=_e(()=>{const h=Ae(e),d=ls(h).map(us).filter(ag);return new Set(d)}),a=Ye(()=>u.value,h=>{l(),i.value&&h.size&&(r=new MutationObserver(t),h.forEach(d=>r.observe(d,s)))},{immediate:!0,flush:"post"}),c=()=>r==null?void 0:r.takeRecords(),f=()=>{a(),l()};return rc(f),{isSupported:i,stop:f,takeRecords:c}}const bg=Symbol("vueuse-ssr-width");function wg(){const e=zi()?ug(bg,null):null;return typeof e=="number"?e:void 0}function dc(e,t={}){const{window:n=wt,ssrWidth:o=wg()}=t,s=fc(()=>n&&"matchMedia"in n&&typeof n.matchMedia=="function"),r=Le(typeof o=="number"),i=Le(),l=Le(!1),u=a=>{l.value=a.matches};return qs(()=>{if(r.value){r.value=!s.value;const a=Ae(e).split(",");l.value=a.some(c=>{const f=c.includes("not all"),h=c.match(/\(\s*min-width:\s*(-?\d+(?:\.\d*)?[a-z]+\s*)\)/),d=c.match(/\(\s*max-width:\s*(-?\d+(?:\.\d*)?[a-z]+\s*)\)/);let _=!!(h||d);return h&&_&&(_=o>=ac(h[1])),d&&_&&(_=o<=ac(d[1])),f?!_:_});return}s.value&&(i.value=n.matchMedia(Ae(e)),l.value=i.value.matches)}),Pe(i,"change",u,{passive:!0}),_e(()=>l.value)}const as=typeof globalThis<"u"?globalThis:typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{},cs="__vueuse_ssr_handlers__",Sg=xg();function xg(){return cs in as||(as[cs]=as[cs]||{}),as[cs]}function pc(e,t){return Sg[e]||t}function Tg(e){return dc("(prefers-color-scheme: dark)",e)}function Og(e){return e==null?"any":e instanceof Set?"set":e instanceof Map?"map":e instanceof Date?"date":typeof e=="boolean"?"boolean":typeof e=="string"?"string":typeof e=="object"?"object":Number.isNaN(e)?"any":"number"}const Ag={boolean:{read:e=>e==="true",write:e=>String(e)},object:{read:e=>JSON.parse(e),write:e=>JSON.stringify(e)},number:{read:e=>Number.parseFloat(e),write:e=>String(e)},any:{read:e=>e,write:e=>String(e)},string:{read:e=>e,write:e=>String(e)},map:{read:e=>new Map(JSON.parse(e)),write:e=>JSON.stringify(Array.from(e.entries()))},set:{read:e=>new Set(JSON.parse(e)),write:e=>JSON.stringify(Array.from(e))},date:{read:e=>new Date(e),write:e=>e.toISOString()}},hc="vueuse-storage";function _c(e,t,n,o={}){var s;const{flush:r="pre",deep:i=!0,listenToStorageChanges:l=!0,writeDefaults:u=!0,mergeDefaults:a=!1,shallow:c,window:f=wt,eventFilter:h,onError:d=R=>{console.error(R)},initOnMounted:_}=o,v=(c?Le:Ve)(typeof t=="function"?t():t),y=_e(()=>Ae(e));if(!n)try{n=pc("getDefaultStorage",()=>{var R;return(R=wt)==null?void 0:R.localStorage})()}catch(R){d(R)}if(!n)return v;const g=Ae(t),x=Og(g),D=(s=o.serializer)!=null?s:Ag[x],{pause:b,resume:P}=gg(v,()=>H(v.value),{flush:r,deep:i,eventFilter:h});Ye(y,()=>M(),{flush:r}),f&&l&&Xr(()=>{n instanceof Storage?Pe(f,"storage",M,{passive:!0}):Pe(f,hc,I),_&&M()}),_||M();function U(R,F){if(f){const z={key:y.value,oldValue:R,newValue:F,storageArea:n};f.dispatchEvent(n instanceof Storage?new StorageEvent("storage",z):new CustomEvent(hc,{detail:z}))}}function H(R){try{const F=n.getItem(y.value);if(R==null)U(F,null),n.removeItem(y.value);else{const z=D.write(R);F!==z&&(n.setItem(y.value,z),U(F,z))}}catch(F){d(F)}}function W(R){const F=R?R.newValue:n.getItem(y.value);if(F==null)return u&&g!=null&&n.setItem(y.value,D.write(g)),g;if(!R&&a){const z=D.read(F);return typeof a=="function"?a(z,g):x==="object"&&!Array.isArray(z)?{...g,...z}:z}else return typeof F!="string"?F:D.read(F)}function M(R){if(!(R&&R.storageArea!==n)){if(R&&R.key==null){v.value=g;return}if(!(R&&R.key!==y.value)){b();try{(R==null?void 0:R.newValue)!==D.write(v.value)&&(v.value=W(R))}catch(F){d(F)}finally{R?Oo(P):P()}}}}function I(R){M(R.detail)}return v}const Cg="*,*::before,*::after{-webkit-transition:none!important;-moz-transition:none!important;-o-transition:none!important;-ms-transition:none!important;transition:none!important}";function Dg(e={}){const{selector:t="html",attribute:n="class",initialValue:o="auto",window:s=wt,storage:r,storageKey:i="vueuse-color-scheme",listenToStorageChanges:l=!0,storageRef:u,emitAuto:a,disableTransition:c=!0}=e,f={auto:"",light:"light",dark:"dark",...e.modes||{}},h=Tg({window:s}),d=_e(()=>h.value?"dark":"light"),_=u||(i==null?cc(o):_c(i,o,r,{window:s,listenToStorageChanges:l})),v=_e(()=>_.value==="auto"?d.value:_.value),y=pc("updateHTMLAttrs",(b,P,U)=>{const H=typeof b=="string"?s==null?void 0:s.document.querySelector(b):us(b);if(!H)return;const W=new Set,M=new Set;let I=null;if(P==="class"){const F=U.split(/\s/g);Object.values(f).flatMap(z=>(z||"").split(/\s/g)).filter(Boolean).forEach(z=>{F.includes(z)?W.add(z):M.add(z)})}else I={key:P,value:U};if(W.size===0&&M.size===0&&I===null)return;let R;c&&(R=s.document.createElement("style"),R.appendChild(document.createTextNode(Cg)),s.document.head.appendChild(R));for(const F of W)H.classList.add(F);for(const F of M)H.classList.remove(F);I&&H.setAttribute(I.key,I.value),c&&(s.getComputedStyle(R).opacity,document.head.removeChild(R))});function g(b){var P;y(t,n,(P=f[b])!=null?P:b)}function x(b){e.onChanged?e.onChanged(b,g):g(b)}Ye(v,x,{flush:"post",immediate:!0}),Xr(()=>x(v.value));const D=_e({get(){return a?_.value:v.value},set(b){_.value=b}});return Object.assign(D,{store:_,system:d,state:v})}function fs(e,t,n={}){const{window:o=wt,initialValue:s,observe:r=!1}=n,i=Le(s),l=_e(()=>{var a;return us(t)||((a=o==null?void 0:o.document)==null?void 0:a.documentElement)});function u(){var a;const c=Ae(e),f=Ae(l);if(f&&o&&c){const h=(a=o.getComputedStyle(f).getPropertyValue(c))==null?void 0:a.trim();i.value=h||i.value||s}}return r&&Eg(l,u,{attributeFilter:["style","class"],window:o}),Ye([l,()=>Ae(e)],(a,c)=>{c[0]&&c[1]&&c[0].style.removeProperty(c[1]),u()},{immediate:!0}),Ye([i,l],([a,c])=>{const f=Ae(e);c!=null&&c.style&&f&&(a==null?c.style.removeProperty(f):c.style.setProperty(f,a))},{immediate:!0}),i}function Pg(e,t,n={}){const{window:o=wt}=n;return _c(e,t,o==null?void 0:o.localStorage,n)}const mc="--vueuse-safe-area-top",gc="--vueuse-safe-area-right",vc="--vueuse-safe-area-bottom",yc="--vueuse-safe-area-left";function Ig(){const e=Le(""),t=Le(""),n=Le(""),o=Le("");if(ic){const r=fs(mc),i=fs(gc),l=fs(vc),u=fs(yc);r.value="env(safe-area-inset-top, 0px)",i.value="env(safe-area-inset-right, 0px)",l.value="env(safe-area-inset-bottom, 0px)",u.value="env(safe-area-inset-left, 0px)",s(),Pe("resize",_g(s),{passive:!0})}function s(){e.value=ds(mc),t.value=ds(gc),n.value=ds(vc),o.value=ds(yc)}return{top:e,right:t,bottom:n,left:o,update:s}}function ds(e){return getComputedStyle(document.documentElement).getPropertyValue(e)}function Rg(e={}){const{window:t=wt,initialWidth:n=Number.POSITIVE_INFINITY,initialHeight:o=Number.POSITIVE_INFINITY,listenOrientation:s=!0,includeScrollbar:r=!0,type:i="inner"}=e,l=Le(n),u=Le(o),a=()=>{if(t)if(i==="outer")l.value=t.outerWidth,u.value=t.outerHeight;else if(i==="visual"&&t.visualViewport){const{width:f,height:h,scale:d}=t.visualViewport;l.value=Math.round(f*d),u.value=Math.round(h*d)}else r?(l.value=t.innerWidth,u.value=t.innerHeight):(l.value=t.document.documentElement.clientWidth,u.value=t.document.documentElement.clientHeight)};a(),Xr(a);const c={passive:!0};if(Pe("resize",a,c),t&&i==="visual"&&t.visualViewport&&Pe(t.visualViewport,"resize",a,c),s){const f=dc("(orientation: portrait)");Ye(f,()=>a())}return{width:l,height:u}}Le();const kg="__vue-devtools-theme__";function Ng(e={}){const t=Dg({...e,storageKey:kg});return{colorMode:t,isDark:_e(()=>t.value==="dark")}}function Vg(e,t){const n=Ve();function o(){return n.value||(n.value=document.createElement("iframe"),n.value.id="vue-devtools-iframe",n.value.src=e,n.value.setAttribute("data-v-inspector-ignore","true"),n.value.onload=t),n.value}return{getIframe:o,iframe:n}}const Zr=Pg("__vue-devtools-frame-state__",{width:80,height:60,top:0,left:50,open:!1,route:"/",position:"bottom",isFirstVisit:!0,closeOnOutsideClick:!1,minimizePanelInactive:5e3,preferShowFloatingPanel:!0,reduceMotion:!1});function ps(){function e(t){Zr.value={...Zr.value,...t}}return{state:An(Zr),updateState:e}}function Lg(){const{state:e,updateState:t}=ps(),n=_e({get(){return e.value.open},set(r){t({open:r})}}),o=(r,i)=>{n.value=i??!n.value},s=()=>{n.value&&(n.value=!1)};return In(()=>{Pe(window,"keydown",r=>{r.code==="KeyD"&&r.altKey&&r.shiftKey&&o()})}),{panelVisible:n,togglePanelVisible:o,closePanel:s}}function hs(e,t,n){return Math.min(Math.max(e,t),n)}const $g=()=>navigator.userAgent.includes("Safari")&&!navigator.userAgent.includes("Chrome");function _s(e){return typeof e=="string"?e.endsWith("px")?+e.slice(0,-2):+e:e}function Ec(e){return e<5?0:e>95?100:Math.abs(e-50)<2?50:e}function Mg(e){const{width:t,height:n}=Rg(),{state:o,updateState:s}=ps(),r=Ve(!1),i=Ve(!1),l=rn({x:0,y:0}),u=rn({x:0,y:0}),a=rn({left:10,top:10,right:10,bottom:10});let c=null;const f=Ig();qs(()=>{a.left=_s(f.left.value)+10,a.top=_s(f.top.value)+10,a.right=_s(f.right.value)+10,a.bottom=_s(f.bottom.value)+10});const h=b=>{i.value=!0;const{left:P,top:U,width:H,height:W}=e.value.getBoundingClientRect();l.x=b.clientX-P-H/2,l.y=b.clientY-U-W/2},d=()=>{r.value=!0,!(o.value.minimizePanelInactive<0)&&(c&&clearTimeout(c),c=setTimeout(()=>{r.value=!1},+o.value.minimizePanelInactive||0))};In(()=>{d()}),Pe("pointerup",()=>{i.value=!1}),Pe("pointerleave",()=>{i.value=!1}),Pe("pointermove",b=>{if(!i.value)return;const P=t.value/2,U=n.value/2,H=b.clientX-l.x,W=b.clientY-l.y;u.x=H,u.y=W;const M=Math.atan2(W-U,H-P),I=70,R=Math.atan2(0-U+I,0-P),F=Math.atan2(0-U+I,t.value-P),z=Math.atan2(n.value-I-U,0-P),Q=Math.atan2(n.value-I-U,t.value-P);s({position:M>=R&&M<=F?"top":M>=F&&M<=Q?"right":M>=Q&&M<=z?"bottom":"left",left:Ec(H/t.value*100),top:Ec(W/n.value*100)})});const _=_e(()=>o.value.position==="left"||o.value.position==="right"),v=_e(()=>{if(o.value.minimizePanelInactive<0)return!1;if(o.value.minimizePanelInactive===0)return!0;const b="ontouchstart"in window||navigator.maxTouchPoints>0||navigator.msMaxTouchPoints>0;return!i.value&&!o.value.open&&!r.value&&!b&&o.value.minimizePanelInactive}),y=_e(()=>{var W,M;const b=(((W=e.value)==null?void 0:W.clientWidth)||0)/2,P=(((M=e.value)==null?void 0:M.clientHeight)||0)/2,U=o.value.left*t.value/100,H=o.value.top*n.value/100;switch(o.value.position){case"top":return{left:hs(U,b+a.left,t.value-b-a.right),top:a.top+P};case"right":return{left:t.value-a.right-P,top:hs(H,b+a.top,n.value-b-a.bottom)};case"left":return{left:a.left+P,top:hs(H,b+a.top,n.value-b-a.bottom)};case"bottom":default:return{left:hs(U,b+a.left,t.value-b-a.right),top:n.value-a.bottom-P}}}),g=_e(()=>({left:`${y.value.left}px`,top:`${y.value.top}px`})),x=_e(()=>{var Z;u.x,u.y;const b=(((Z=e.value)==null?void 0:Z.clientHeight)||0)/2,P={left:a.left+b,top:a.top+b,right:a.right+b,bottom:a.bottom+b},U=P.left+P.right,H=P.top+P.bottom,W=t.value-U,M=n.value-H,I={zIndex:-1,pointerEvents:i.value?"none":"auto",width:`min(${o.value.width}vw, calc(100vw - ${U}px))`,height:`min(${o.value.height}vh, calc(100vh - ${H}px))`},R=y.value,F=Math.min(W,o.value.width*t.value/100),z=Math.min(M,o.value.height*n.value/100),Q=(R==null?void 0:R.left)||0,Ee=(R==null?void 0:R.top)||0;switch(o.value.position){case"top":case"bottom":I.left=0,I.transform="translate(-50%, 0)",Q-P.left<F/2?I.left=`${F/2-Q+P.left}px`:t.value-Q-P.right<F/2&&(I.left=`${t.value-Q-F/2-P.right}px`);break;case"right":case"left":I.top=0,I.transform="translate(0, -50%)",Ee-P.top<z/2?I.top=`${z/2-Ee+P.top}px`:n.value-Ee-P.bottom<z/2&&(I.top=`${n.value-Ee-z/2-P.bottom}px`);break}switch(o.value.position){case"top":I.top=0;break;case"right":I.right=0;break;case"left":I.left=0;break;case"bottom":default:I.bottom=0;break}return I}),D=_e(()=>{const b={transform:_.value?`translate(${v.value?`calc(-50% ${o.value.position==="right"?"+":"-"} 15px)`:"-50%"}, -50%) rotate(90deg)`:`translate(-50%, ${v.value?`calc(-50% ${o.value.position==="top"?"-":"+"} 15px)`:"-50%"})`};if(v.value)switch(o.value.position){case"top":case"right":b.borderTopLeftRadius="0",b.borderTopRightRadius="0";break;case"bottom":case"left":b.borderBottomLeftRadius="0",b.borderBottomRightRadius="0";break}return i.value&&(b.transition="none !important"),b});return{isHidden:v,isDragging:i,isVertical:_,anchorStyle:g,iframeStyle:x,panelStyle:D,onPointerDown:h,bringUp:d}}const ms=20,gs=100,Ug=cn({__name:"FrameBox",props:{isDragging:{type:Boolean},client:{},viewMode:{}},setup(e){const t=e,{state:n,updateState:o}=ps(),s=Ve(),r=Ve(!1);Em(()=>{Ta.functions.on("update-client-state",l=>{l&&o({minimizePanelInactive:l.minimizePanelInteractive,closeOnOutsideClick:l.closeOnOutsideClick,preferShowFloatingPanel:l.showFloatingPanel,reduceMotion:l.reduceMotion})})}),qs(()=>{if(s.value&&n.value.open){const l=t.client.getIFrame();l.style.pointerEvents=r.value||t.isDragging?"none":"auto",Array.from(s.value.children).every(u=>u!==l)&&s.value.appendChild(l)}}),Pe(window,"keydown",l=>{}),Pe(window,"mousedown",l=>{if(!n.value.closeOnOutsideClick||!n.value.open||r.value)return;l.composedPath().find(a=>{var f;const c=a;return Array.from(c.classList||[]).some(h=>h.startsWith("vue-devtools"))||((f=c.tagName)==null?void 0:f.toLowerCase())==="iframe"})||o({open:!1})}),Pe(window,"mousemove",l=>{if(!r.value||!n.value.open)return;const a=t.client.getIFrame().getBoundingClientRect();if(r.value.right){const f=Math.abs(l.clientX-((a==null?void 0:a.left)||0))/window.innerWidth*100;o({width:Math.min(gs,Math.max(ms,f))})}else if(r.value.left){const f=Math.abs(((a==null?void 0:a.right)||0)-l.clientX)/window.innerWidth*100;o({width:Math.min(gs,Math.max(ms,f))})}if(r.value.top){const f=Math.abs(((a==null?void 0:a.bottom)||0)-l.clientY)/window.innerHeight*100;o({height:Math.min(gs,Math.max(ms,f))})}else if(r.value.bottom){const f=Math.abs(l.clientY-((a==null?void 0:a.top)||0))/window.innerHeight*100;o({height:Math.min(gs,Math.max(ms,f))})}}),Pe(window,"mouseup",()=>{r.value=!1}),Pe(window,"mouseleave",()=>{r.value=!1});const i=_e(()=>t.viewMode==="xs"?"view-mode-xs":t.viewMode==="fullscreen"?"view-mode-fullscreen":"");return(l,u)=>tt((Re(),Ct("div",{ref_key:"container",ref:s,class:ht(["vue-devtools-frame",i.value])},[tt(ie("div",{class:"vue-devtools-resize vue-devtools-resize--horizontal",style:{top:0},onMousedown:u[0]||(u[0]=Pt(()=>r.value={top:!0},["prevent"]))},null,544),[[st,J(n).position!=="top"]]),tt(ie("div",{class:"vue-devtools-resize vue-devtools-resize--horizontal",style:{bottom:0},onMousedown:u[1]||(u[1]=Pt(()=>r.value={bottom:!0},["prevent"]))},null,544),[[st,J(n).position!=="bottom"]]),tt(ie("div",{class:"vue-devtools-resize vue-devtools-resize--vertical",style:{left:0},onMousedown:u[2]||(u[2]=Pt(()=>r.value={left:!0},["prevent"]))},null,544),[[st,J(n).position!=="left"]]),tt(ie("div",{class:"vue-devtools-resize vue-devtools-resize--vertical",style:{right:0},onMousedown:u[3]||(u[3]=Pt(()=>r.value={right:!0},["prevent"]))},null,544),[[st,J(n).position!=="right"]]),tt(ie("div",{class:"vue-devtools-resize vue-devtools-resize-corner",style:{top:0,left:0,cursor:"nwse-resize"},onMousedown:u[4]||(u[4]=Pt(()=>r.value={top:!0,left:!0},["prevent"]))},null,544),[[st,J(n).position!=="top"&&J(n).position!=="left"]]),tt(ie("div",{class:"vue-devtools-resize vue-devtools-resize-corner",style:{top:0,right:0,cursor:"nesw-resize"},onMousedown:u[5]||(u[5]=Pt(()=>r.value={top:!0,right:!0},["prevent"]))},null,544),[[st,J(n).position!=="top"&&J(n).position!=="right"]]),tt(ie("div",{class:"vue-devtools-resize vue-devtools-resize-corner",style:{bottom:0,left:0,cursor:"nesw-resize"},onMousedown:u[6]||(u[6]=Pt(()=>r.value={bottom:!0,left:!0},["prevent"]))},null,544),[[st,J(n).position!=="bottom"&&J(n).position!=="left"]]),tt(ie("div",{class:"vue-devtools-resize vue-devtools-resize-corner",style:{bottom:0,right:0,cursor:"nwse-resize"},onMousedown:u[7]||(u[7]=Pt(()=>r.value={bottom:!0,right:!0},["prevent"]))},null,544),[[st,J(n).position!=="bottom"&&J(n).position!=="right"]])],2)),[[st,J(n).open]])}}),bc=(e,t)=>{const n=e.__vccOpts||e;for(const[o,s]of t)n[o]=s;return n},Fg=bc(Ug,[["__scopeId","data-v-399f5059"]]),Bg=bc(cn({__name:"App",setup(e){const t=Ve(),n=Ve(),{colorMode:o}=Ng({selector:t}),s=Ve({viewMode:"default"}),r=_e(()=>{const M=o.value==="dark";return{"--vue-devtools-widget-bg":M?"#121212":"#ffffff","--vue-devtools-widget-fg":M?"#F5F5F5":"#111","--vue-devtools-widget-border":M?"#3336":"#efefef","--vue-devtools-widget-shadow":M?"rgba(0,0,0,0.3)":"rgba(128,128,128,0.1)"}}),{onPointerDown:i,bringUp:l,anchorStyle:u,iframeStyle:a,isDragging:c,isVertical:f,isHidden:h,panelStyle:d}=Mg(n),{togglePanelVisible:_,closePanel:v,panelVisible:y}=Lg(),g=hm(),x=Ve(!0);O.__VUE_DEVTOOLS_TOGGLE_OVERLAY__=M=>{x.value=M};const{state:D}=ps();function b(M,I=50,R=200){return new Promise(F=>{var z;(z=M==null?void 0:M.contentWindow)==null||z.postMessage("__VUE_DEVTOOLS_CREATE_CLIENT__","*"),window.addEventListener("message",Q=>{Q.data==="__VUE_DEVTOOLS_CLIENT_READY__"&&F()})})}const P=Ve();Ih(()=>{ba().functions.on("toggle-panel",(I=!y)=>{_(void 0,I)}),wa.ctx.api.getVueInspector().then(I=>{P.value=I;let R=y.value;P.value.onEnabled=()=>{R=y.value,_(void 0,!1)},P.value.onDisabled=()=>{_(void 0,R)}})}),addEventListener("keyup",M=>{var I,R,F;((I=M.key)==null?void 0:I.toLowerCase())==="escape"&&((R=P.value)!=null&&R.enabled)&&((F=P.value)==null||F.disable())});const U=_e(()=>!!P.value);function H(){P.value.enable()}const{getIframe:W}=Vg(g,async()=>{const M=W();fm(M),await b(M)});return(M,I)=>tt((Re(),Ct("div",{ref_key:"anchorEle",ref:t,class:ht(["vue-devtools__anchor",{"vue-devtools__anchor--vertical":J(f),"vue-devtools__anchor--hide":J(h),fullscreen:s.value.viewMode==="fullscreen","reduce-motion":J(D).reduceMotion}]),style:Ne([J(u),r.value]),onMousemove:I[2]||(I[2]=(...R)=>J(l)&&J(l)(...R))},[J($g)()?$o("",!0):(Re(),Ct("div",{key:0,class:"vue-devtools__anchor--glowing",style:Ne(J(c)?"opacity: 0.6 !important":"")},null,4)),ie("div",{ref_key:"panelEle",ref:n,class:"vue-devtools__panel",style:Ne(J(d)),onPointerdown:I[1]||(I[1]=(...R)=>J(i)&&J(i)(...R))},[ie("div",{class:"vue-devtools__anchor-btn panel-entry-btn",title:"Toggle Vue DevTools","aria-label":"Toggle devtools panel",style:Ne(J(y)?"":"filter:saturate(0)"),onClick:I[0]||(I[0]=(...R)=>J(_)&&J(_)(...R))},I[3]||(I[3]=[ie("svg",{viewBox:"0 0 256 198",fill:"none",xmlns:"http://www.w3.org/2000/svg"},[ie("path",{fill:"#41B883",d:"M204.8 0H256L128 220.8L0 0h97.92L128 51.2L157.44 0h47.36Z"}),ie("path",{fill:"#41B883",d:"m0 0l128 220.8L256 0h-51.2L128 132.48L50.56 0H0Z"}),ie("path",{fill:"#35495E",d:"M50.56 0L128 133.12L204.8 0h-47.36L128 51.2L97.92 0H50.56Z"})],-1)]),4),J(wa).ctx.state.vitePluginDetected&&U.value?(Re(),Ct(Ie,{key:0},[I[5]||(I[5]=ie("div",{class:"vue-devtools__panel-content vue-devtools__panel-divider"},null,-1)),ie("div",{class:ht(["vue-devtools__anchor-btn vue-devtools__panel-content vue-devtools__inspector-button",{active:U.value}]),title:"Toggle Component Inspector",onClick:H},[(Re(),Ct("svg",{xmlns:"http://www.w3.org/2000/svg",style:Ne([{height:"1.1em",width:"1.1em",opacity:"0.5"},U.value?"opacity:1;color:#00dc82;":""]),viewBox:"0 0 24 24"},I[4]||(I[4]=[ie("g",{fill:"none",stroke:"currentColor","stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2"},[ie("circle",{cx:"12",cy:"12",r:".5",fill:"currentColor"}),ie("path",{d:"M5 12a7 7 0 1 0 14 0a7 7 0 1 0-14 0m7-9v2m-9 7h2m7 7v2m7-9h2"})],-1)]),4))],2)],64)):$o("",!0)],36),Se(Fg,{style:Ne(J(a)),"is-dragging":J(c),client:{close:J(v),getIFrame:J(W)},"view-mode":s.value.viewMode},null,8,["style","is-dragging","client","view-mode"])],38)),[[st,J(D).preferShowFloatingPanel?x.value:J(y)]])}}),[["__scopeId","data-v-640ec535"]]);function Hg(e){const t="__vue-devtools-container__",n=document.createElement("div");n.setAttribute("id",t),n.setAttribute("data-v-inspector-ignore","true"),document.getElementsByTagName("body")[0].appendChild(n),fp({render:()=>Md(e),devtools:{hide:!0}}).mount(n)}Hg(Bg)})();
