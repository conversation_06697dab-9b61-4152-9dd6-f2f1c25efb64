import type { PropType } from 'vue';
import type { ChartData, Plugin } from 'chart.js';
export declare const CommonProps: {
    readonly data: {
        readonly type: PropType<ChartData<keyof import("chart.js").ChartTypeRegistry, (number | [number, number] | import("chart.js").Point | import("chart.js").BubbleDataPoint | null)[], unknown>>;
        readonly required: true;
    };
    readonly options: {
        readonly type: PropType<import("chart.js/dist/types/utils")._DeepPartialObject<import("chart.js").CoreChartOptions<keyof import("chart.js").ChartTypeRegistry> & import("chart.js").ElementChartOptions<keyof import("chart.js").ChartTypeRegistry> & import("chart.js").PluginChartOptions<keyof import("chart.js").ChartTypeRegistry> & import("chart.js").DatasetChartOptions<keyof import("chart.js").ChartTypeRegistry> & import("chart.js").ScaleChartOptions<keyof import("chart.js").ChartTypeRegistry>>>;
        readonly default: () => {};
    };
    readonly plugins: {
        readonly type: PropType<Plugin<keyof import("chart.js").ChartTypeRegistry, import("chart.js/dist/types/basic").AnyObject>[]>;
        readonly default: () => never[];
    };
    readonly datasetIdKey: {
        readonly type: StringConstructor;
        readonly default: "label";
    };
    readonly updateMode: {
        readonly type: PropType<"resize" | "reset" | "none" | "hide" | "show" | "default" | "active">;
        readonly default: undefined;
    };
};
export declare const A11yProps: {
    readonly ariaLabel: {
        readonly type: StringConstructor;
    };
    readonly ariaDescribedby: {
        readonly type: StringConstructor;
    };
};
export declare const Props: {
    readonly ariaLabel: {
        readonly type: StringConstructor;
    };
    readonly ariaDescribedby: {
        readonly type: StringConstructor;
    };
    readonly data: {
        readonly type: PropType<ChartData<keyof import("chart.js").ChartTypeRegistry, (number | [number, number] | import("chart.js").Point | import("chart.js").BubbleDataPoint | null)[], unknown>>;
        readonly required: true;
    };
    readonly options: {
        readonly type: PropType<import("chart.js/dist/types/utils")._DeepPartialObject<import("chart.js").CoreChartOptions<keyof import("chart.js").ChartTypeRegistry> & import("chart.js").ElementChartOptions<keyof import("chart.js").ChartTypeRegistry> & import("chart.js").PluginChartOptions<keyof import("chart.js").ChartTypeRegistry> & import("chart.js").DatasetChartOptions<keyof import("chart.js").ChartTypeRegistry> & import("chart.js").ScaleChartOptions<keyof import("chart.js").ChartTypeRegistry>>>;
        readonly default: () => {};
    };
    readonly plugins: {
        readonly type: PropType<Plugin<keyof import("chart.js").ChartTypeRegistry, import("chart.js/dist/types/basic").AnyObject>[]>;
        readonly default: () => never[];
    };
    readonly datasetIdKey: {
        readonly type: StringConstructor;
        readonly default: "label";
    };
    readonly updateMode: {
        readonly type: PropType<"resize" | "reset" | "none" | "hide" | "show" | "default" | "active">;
        readonly default: undefined;
    };
    readonly type: {
        readonly type: PropType<keyof import("chart.js").ChartTypeRegistry>;
        readonly required: true;
    };
    readonly destroyDelay: {
        readonly type: NumberConstructor;
        readonly default: 0;
    };
};
//# sourceMappingURL=props.d.ts.map