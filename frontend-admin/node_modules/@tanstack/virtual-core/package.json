{"name": "@tanstack/virtual-core", "version": "3.13.9", "description": "Headless UI for virtualizing scrollable elements in TS/JS + Frameworks", "author": "<PERSON>", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/TanStack/virtual.git", "directory": "packages/virtual-core"}, "homepage": "https://tanstack.com/virtual", "funding": {"type": "github", "url": "https://github.com/sponsors/tanner<PERSON>ley"}, "keywords": ["react", "vue", "solid", "virtual", "virtual-core", "datagrid"], "type": "module", "types": "dist/esm/index.d.ts", "main": "dist/cjs/index.cjs", "module": "dist/esm/index.js", "exports": {".": {"import": {"types": "./dist/esm/index.d.ts", "default": "./dist/esm/index.js"}, "require": {"types": "./dist/cjs/index.d.cts", "default": "./dist/cjs/index.cjs"}}, "./package.json": "./package.json"}, "sideEffects": false, "files": ["dist", "src"], "scripts": {"clean": "premove ./dist ./coverage", "test:eslint": "eslint ./src", "test:types": "tsc", "test:lib": "vitest", "test:lib:dev": "pnpm run test:lib --watch", "test:build": "publint --strict", "build": "vite build"}}