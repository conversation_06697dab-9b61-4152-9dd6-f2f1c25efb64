{"version": 3, "file": "index.cjs", "sources": ["../../src/index.ts"], "sourcesContent": ["import {\n  Virtualizer,\n  elementScroll,\n  observeElementOffset,\n  observeElementRect,\n  observeWindowOffset,\n  observeWindowRect,\n  windowScroll,\n} from '@tanstack/virtual-core'\nimport {\n  computed,\n  onScopeDispose,\n  shallowRef,\n  triggerRef,\n  unref,\n  watch,\n} from 'vue'\nimport type { PartialKeys, VirtualizerOptions } from '@tanstack/virtual-core'\nimport type { Ref } from 'vue'\n\nexport * from '@tanstack/virtual-core'\n\ntype MaybeRef<T> = T | Ref<T>\n\nfunction useVirtualizerBase<\n  TScrollElement extends Element | Window,\n  TItemElement extends Element,\n>(\n  options: MaybeRef<VirtualizerOptions<TScrollElement, TItemElement>>,\n): Ref<Virtualizer<TScrollElement, TItemElement>> {\n  const virtualizer = new Virtualizer(unref(options))\n  const state = shallowRef(virtualizer)\n\n  const cleanup = virtualizer._didMount()\n\n  watch(\n    () => unref(options).getScrollElement(),\n    (el) => {\n      if (el) {\n        virtualizer._willUpdate()\n      }\n    },\n    {\n      immediate: true,\n    },\n  )\n\n  watch(\n    () => unref(options),\n    (options) => {\n      virtualizer.setOptions({\n        ...options,\n        onChange: (instance, sync) => {\n          triggerRef(state)\n          options.onChange?.(instance, sync)\n        },\n      })\n\n      virtualizer._willUpdate()\n      triggerRef(state)\n    },\n    {\n      immediate: true,\n    },\n  )\n\n  onScopeDispose(cleanup)\n\n  return state\n}\n\nexport function useVirtualizer<\n  TScrollElement extends Element,\n  TItemElement extends Element,\n>(\n  options: MaybeRef<\n    PartialKeys<\n      VirtualizerOptions<TScrollElement, TItemElement>,\n      'observeElementRect' | 'observeElementOffset' | 'scrollToFn'\n    >\n  >,\n): Ref<Virtualizer<TScrollElement, TItemElement>> {\n  return useVirtualizerBase<TScrollElement, TItemElement>(\n    computed(() => ({\n      observeElementRect: observeElementRect,\n      observeElementOffset: observeElementOffset,\n      scrollToFn: elementScroll,\n      ...unref(options),\n    })),\n  )\n}\n\nexport function useWindowVirtualizer<TItemElement extends Element>(\n  options: MaybeRef<\n    PartialKeys<\n      VirtualizerOptions<Window, TItemElement>,\n      | 'observeElementRect'\n      | 'observeElementOffset'\n      | 'scrollToFn'\n      | 'getScrollElement'\n    >\n  >,\n): Ref<Virtualizer<Window, TItemElement>> {\n  return useVirtualizerBase<Window, TItemElement>(\n    computed(() => ({\n      getScrollElement: () => (typeof document !== 'undefined' ? window : null),\n      observeElementRect: observeWindowRect,\n      observeElementOffset: observeWindowOffset,\n      scrollToFn: windowScroll,\n      initialOffset: () =>\n        typeof document !== 'undefined' ? window.scrollY : 0,\n      ...unref(options),\n    })),\n  )\n}\n"], "names": ["Virtualizer", "unref", "shallowRef", "watch", "options", "triggerRef", "onScopeDispose", "computed", "observeElementRect", "observeElementOffset", "elementScroll", "observeWindowRect", "observeWindowOffset", "windowScroll"], "mappings": ";;;;AAwBA,SAAS,mBAIP,SACgD;AAChD,QAAM,cAAc,IAAIA,YAAAA,YAAYC,IAAA,MAAM,OAAO,CAAC;AAC5C,QAAA,QAAQC,eAAW,WAAW;AAE9B,QAAA,UAAU,YAAY,UAAU;AAEtCC,MAAA;AAAA,IACE,MAAMF,IAAA,MAAM,OAAO,EAAE,iBAAiB;AAAA,IACtC,CAAC,OAAO;AACN,UAAI,IAAI;AACN,oBAAY,YAAY;AAAA,MAAA;AAAA,IAE5B;AAAA,IACA;AAAA,MACE,WAAW;AAAA,IAAA;AAAA,EAEf;AAEAE,MAAA;AAAA,IACE,MAAMF,IAAAA,MAAM,OAAO;AAAA,IACnB,CAACG,aAAY;AACX,kBAAY,WAAW;AAAA,QACrB,GAAGA;AAAAA,QACH,UAAU,CAAC,UAAU,SAAS;;AAC5BC,cAAAA,WAAW,KAAK;AAChBD,yBAAQ,aAARA,kCAAmB,UAAU;AAAA,QAAI;AAAA,MACnC,CACD;AAED,kBAAY,YAAY;AACxBC,UAAAA,WAAW,KAAK;AAAA,IAClB;AAAA,IACA;AAAA,MACE,WAAW;AAAA,IAAA;AAAA,EAEf;AAEAC,MAAAA,eAAe,OAAO;AAEf,SAAA;AACT;AAEO,SAAS,eAId,SAMgD;AACzC,SAAA;AAAA,IACLC,IAAAA,SAAS,OAAO;AAAA,MAAA,oBACdC,YAAA;AAAA,MAAA,sBACAC,YAAA;AAAA,MACA,YAAYC,YAAA;AAAA,MACZ,GAAGT,UAAM,OAAO;AAAA,IAAA,EAChB;AAAA,EACJ;AACF;AAEO,SAAS,qBACd,SASwC;AACjC,SAAA;AAAA,IACLM,IAAAA,SAAS,OAAO;AAAA,MACd,kBAAkB,MAAO,OAAO,aAAa,cAAc,SAAS;AAAA,MACpE,oBAAoBI,YAAA;AAAA,MACpB,sBAAsBC,YAAA;AAAA,MACtB,YAAYC,YAAA;AAAA,MACZ,eAAe,MACb,OAAO,aAAa,cAAc,OAAO,UAAU;AAAA,MACrD,GAAGZ,UAAM,OAAO;AAAA,IAAA,EAChB;AAAA,EACJ;AACF;;;;;;;;;"}