# Monex Setup Guide

This guide will help you set up the Monex AI-Powered Stock Selection Platform on your local development environment.

## Prerequisites

Before you begin, ensure you have the following installed:

- **Java 21+** - For the Spring Boot backend
- **Node.js 18+** - For the Vue.js frontends
- **MySQL 8.0** - For the database
- **Redis 6+** - For caching
- **Docker & Docker Compose** (optional) - For containerized deployment

## Quick Start with Docker

The easiest way to get started is using Docker Compose:

```bash
# Clone the repository
git clone <repository-url>
cd monex

# Copy environment file
cp .env.example .env

# Edit .env file with your configuration
nano .env

# Start all services
docker-compose up -d

# Check service status
docker-compose ps
```

The application will be available at:
- User Frontend: http://localhost:3000
- Admin Frontend: http://localhost:3001
- Backend API: http://localhost:8080
- API Documentation: http://localhost:8080/swagger-ui.html

## Manual Setup

### 1. Database Setup

```bash
# Start MySQL
mysql -u root -p

# Create database and user
CREATE DATABASE monex CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE USER 'monex_user'@'localhost' IDENTIFIED BY 'monex_password';
GRANT ALL PRIVILEGES ON monex.* TO 'monex_user'@'localhost';
FLUSH PRIVILEGES;

# Import schema
mysql -u monex_user -p monex < database/schema.sql
```

### 2. Redis Setup

```bash
# Start Redis server
redis-server

# Or with Docker
docker run -d --name monex-redis -p 6379:6379 redis:7-alpine
```

### 3. Backend Setup

```bash
cd monex-api

# Copy and configure application properties
cp src/main/resources/application.yml.example src/main/resources/application.yml

# Edit configuration
nano src/main/resources/application.yml

# Build and run
./mvnw clean install
./mvnw spring-boot:run
```

### 4. Frontend Setup

#### User Frontend
```bash
cd frontend-user

# Install dependencies
npm install

# Start development server
npm run dev
```

#### Admin Frontend
```bash
cd frontend-admin

# Install dependencies
npm install

# Start development server
npm run dev
```

## Configuration

### Environment Variables

Copy `.env.example` to `.env` and configure the following:

```bash
# Database
DB_USERNAME=monex_user
DB_PASSWORD=monex_password
DB_HOST=localhost
DB_PORT=3306
DB_NAME=monex

# Redis
REDIS_HOST=localhost
REDIS_PORT=6379

# JWT
JWT_SECRET=your-secret-key-change-this-in-production

# External APIs
STOCK_API_KEY=your-stock-api-key
LLM_API_KEY=your-openai-api-key
```

### External API Keys

You'll need to obtain API keys for:

1. **Stock Data API** - For real-time stock data
   - Alpha Vantage: https://www.alphavantage.co/
   - IEX Cloud: https://iexcloud.io/
   - Finnhub: https://finnhub.io/

2. **LLM API** - For AI-powered analysis
   - OpenAI: https://platform.openai.com/
   - Anthropic: https://www.anthropic.com/
   - Google AI: https://ai.google.dev/

## Development

### Running Tests

```bash
# Backend tests
cd monex-api
./mvnw test

# Frontend tests
cd frontend-user
npm run test

cd ../frontend-admin
npm run test
```

### Building for Production

```bash
# Backend
cd monex-api
./mvnw clean package

# Frontend User
cd frontend-user
npm run build

# Frontend Admin
cd frontend-admin
npm run build
```

## Troubleshooting

### Common Issues

1. **Database Connection Failed**
   - Check MySQL is running
   - Verify credentials in configuration
   - Ensure database exists

2. **Redis Connection Failed**
   - Check Redis is running
   - Verify Redis host and port

3. **Frontend Build Errors**
   - Clear node_modules and reinstall
   - Check Node.js version compatibility

4. **Backend Startup Issues**
   - Check Java version
   - Verify all dependencies are available
   - Check application logs

### Logs

- Backend logs: `monex-api/logs/monex-backend.log`
- Docker logs: `docker-compose logs [service-name]`

## Next Steps

1. Configure external API keys
2. Set up user authentication
3. Import initial stock data
4. Configure LLM analysis parameters
5. Set up monitoring and alerting

For more detailed information, see the individual component documentation in the `docs/` directory.
