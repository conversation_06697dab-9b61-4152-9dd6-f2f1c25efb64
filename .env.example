# Database Configuration
DB_USERNAME=monex_user
DB_PASSWORD=monex_password
DB_HOST=localhost
DB_PORT=3306
DB_NAME=monex

# Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=

# JWT Configuration
JWT_SECRET=your-secret-key-change-this-in-production
JWT_ISSUER_URI=http://localhost:8080

# External API Configuration
STOCK_API_URL=https://api.example.com
STOCK_API_KEY=your-stock-api-key

# LLM API Configuration
LLM_API_URL=https://api.openai.com/v1
LLM_API_KEY=your-openai-api-key
LLM_MODEL=gpt-4

# Application Configuration
SPRING_PROFILES_ACTIVE=dev
SERVER_PORT=8080

# Frontend Configuration
VITE_API_BASE_URL=http://localhost:8080/api
VITE_APP_TITLE=Monex - Stock Selection Platform
